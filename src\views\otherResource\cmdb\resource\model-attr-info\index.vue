<!--
 * @Descripttion: 
 * @Author: wangl
 * @version: 
 * @Date: 2025-03-04 09:08:12
 * @LastEditors: wangl
 * @LastEditTime: 2025-03-24 16:03:51
-->
<template>
  <div class="app-container">
    <search v-model="query" @search="get" />
    <div class="filter-container filter">
      <el-button v-if="checkButtonPermission(322141)" v-bind="addBtnConfig" @click="openBase('edit')">新增</el-button>
    </div>
    <el-table v-loading="waiting" :data="dataList" v-bind="tableConfig" v-on="tableEventConfig">
      <el-table-column width="100" type="index" label="序号" v-bind="columnConifg" />
      <el-table-column min-width="13.89vh" prop="equipType" label="型号" v-bind="columnConifg" />
      <el-table-column min-width="13.89vh" prop="typeCname" label="资源类型" v-bind="columnConifg" />
      <el-table-column min-width="13.89vh" prop="manufacturerName" label="厂商" v-bind="columnConifg" />
      <el-table-column v-bind="columnFixConifg" :width="tableButtonWidth" v-if="showActionColumn">
        <template slot-scope="scope">
          <template v-for="item in tableButtonList">
            <el-button v-if="item.visible !== false" :key="item.name + random.getUuid()" type="text"
              @click="item.click(scope)">
              {{ item.name }}</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <app-pager />
    <edit v-if="v === 'edit'" v-bind="{ id }" :is-dialog="true" @saved="get" />
    <el-dialog v-dialogDrag title="属性选择" :visible.sync="attrDialogVisible" width="60%" :close-on-click-modal="false">
      <div v-for="sublist, index in isQueryResourceCiTypeAttr" :key="sublist.attrType">
        <div v-if="sublist.newConfCiTypeAttriList.length">
          <el-checkbox-group v-if="checkedAttr[index]" v-model="checkedAttr[index]" style="height: 10.37vh;">
            <el-divider v-if="sublist.newConfCiTypeAttriList.length" content-position="left">{{ ['基础上报', '运行状态', '配置信息',
              '基础自定义'][sublist.attrType] || '' }}</el-divider>
            <el-checkbox v-for="attr in sublist.newConfCiTypeAttriList" :key="attr.typeAttrId"
              :label="attr.typeAttrId">{{
                attr.cname }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="attrDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveAttrs">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import edit from './edit'
import { listPage } from '@/views/otherResource/soc/mixins'
import permission from '@/views/otherResource/soc/mixins/permission'
import search from './search'
import {
  queryAttriByParams
} from '@/api/otherResource/cmdb/confCiTypeAttr'
import {
  saveBatchList,
  getModelInfoAttr
} from '@/api/otherResource/cmdb/sysModelInfo'
export default {
  components: { edit, search },
  mixins: [listPage, permission],
  data() {
    return {
      attrDialogVisible: false,
      checkedAttr: [[], [], [], []],
      isQueryResourceCiTypeAttr: [],
      editModelType: ''
    }
  },
  computed: {
    tableButtonList() {
      return [
        { name: '编辑', permission: 322142, click: (scope) => this.openBase('edit', scope.row.modelId) },
        { name: '属性配置', permission: 322143, click: (scope) => this.handleAttrDialog(scope.row) },
        { name: '删除', permission: 322144, click: (scope) => this.del({ ...scope.row }) }
      ].filter(button => !button.permission || this.checkButtonPermission(button.permission))
    },
    showActionColumn() {
      return this.hasAnyActionPermission([322142, 322143, 322144])
    }
  },
  created() {
    this.get()
  },
  methods: {
    get() {
      this.getBase({
        url: '/cmdbService/model-info/page',
        params: this.query
      })
    },
    del(item) {
      this.delData({
        url: '/cmdbService/model-info/' + item.modelId,
        success: () => this.get()
      })
    },
    handleAttrDialog(row) {
      this.editModelType = row.modelId
      queryAttriByParams({ deviceType: row.deviceType }).then((res) => {
        if (res.data.data != null) {
          this.isQueryResourceCiTypeAttr = res.data.data
        }
        getModelInfoAttr({ modelId: this.editModelType }).then((res) => {
          this.checkedAttr = [[], [], [], []]
          res.data.data.forEach(ele => {
            ele.attrList.forEach(i => {
              this.checkedAttr[ele.attrType].push(Number(i.typeAttrId))
            })
          })
          this.attrDialogVisible = true
        })
      })
    },
    saveAttrs() {
      const sysModelAttrInfo = []
      this.checkedAttr.forEach((i, index) => {
        let tempList = []
        i.forEach(j => {
          const innerMap = {
            modelId: this.editModelType,
            typeAttrId: j
          }
          tempList.push(innerMap)
        })
        sysModelAttrInfo.push({ attrType: index, modelId: this.editModelType, ibmsSysModelAttributesList: tempList })
      })
      saveBatchList(sysModelAttrInfo).then(res => {
        if (res.data.code === 0) {
          this.checkedAttr = []
          this.$message.success('保存成功')
          this.attrDialogVisible = false
        }
      })
    }
  }
}
</script>
