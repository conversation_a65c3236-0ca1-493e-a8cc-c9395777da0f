import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken, getRfleshToken } from '@/utils/auth' // get token from cookie
import getPageTitle from '@/utils/get-page-title'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

// 前往后台
function gotoBack(next) {
  if (store.getters.permission_routers !== null && store.getters.permission_routers !== undefined) {
    var router = null
    // 先判断有没有告警列表
    store.getters.permission_routers.forEach((item) => {
      if (!item.hidden && item.name !== undefined && router === null) {
        // 如果是隐藏目录则不显示
        router = isAlarmList(item, 0)
        if (router) {
          next({ name: router.name })
        } else {
          router = null
        }
      }
    })
    store.getters.permission_routers.forEach((item) => {
      if (!item.hidden && item.name !== undefined && router === null) {
        // 如果是隐藏目录则不显示
        router = getRouter(item, 0)
        console.log('a', router)
        if (router) {
          next({ name: router.name })
        }
      }
    })
  }
}

function isAlarmList(router, type) {
  if (router.children) {
    var result = null
    router.children.forEach((item) => {
      if (!result) {
        result = isAlarmList(item, type + 1)
      }
    })
    if (result) {
      return result
    }
  } else {
    if (router.name === '1768') {
      return router
    }
  }
}

function getRouter(router, type) {
  if (router.children) {
    var result = null
    router.children.forEach((item) => {
      if (!result) {
        result = getRouter(item, type + 1)
      }
    })
    if (result) {
      return result
    }
  } else {
    return router
  }
}

function hasPermission(permissiomRouters, toPath) {
  return true
  // if (!toPath) {
  //   menuTree.forEach(e => {
  //     if (e.path.indexOf(toPath) > 0) {
  //       return true
  //     } else {
  //       if (e.children.length > 0) {
  //         e.children.forEach(sub => {
  //           if (sub.path.indexOf(toPath) > 0) {
  //             return true
  //           }
  //         })
  //       }
  //     }
  //     return false
  //   })
  // }
  // if (menuTree.indexOf('admin') >= 0) return true // admin permission passed directly
  // if (!permissionRoles) return true
  // return roles.some(role => permissionRoles.indexOf(role) >= 0)
}

const whiteList = ['/login', '/newLogin'] // no redirect whitelist

router.beforeEach(async (to, from, next) => {
  // start progress bar
  NProgress.start()

  // set page title
  document.title = getPageTitle(to.meta.title)
  // determine whether the user has logged in
  const hasToken = getToken()
  if (hasToken) {
    if (store.getters.menuTree.length === 0) { // 判断当前用户是否已拉取完user_info信息
      store.dispatch('GetUserInfo').then(res => { // 拉取user_info
        // const menuTree = res.data.data.menuTreeVo // note: roles must be a array! such as: ['editor','develop']
        store.dispatch('GenerateRoutes', res).then(() => { // 根据roles权限生成可访问的路由表
          router.addRoutes(store.getters.addRouters) // 动态添加可访问路由表
          if (to.path === '/' || getRfleshToken() && to.path === '/newLogin') {
            next({ name: store.getters.firstMenu.dashboard || store.getters.firstMenu.manage, replace: true })
          } else {
            next({ ...to, replace: true }) // hack方法 确保addRoutes已完成 ,set the replace: true so the navigation will not leave a history record
          }
        })
      }).catch((err) => {
        if (err.code === 205) {
          next('/welcome')
        } else if (err.message === '用户无基础权限') {
          // 权限不足错误，已在store中处理弹窗和跳转，这里不需要额外处理
          console.log('用户权限不足，已跳转到外部登录页面')
          // 不调用next()，因为页面会跳转到外部URL
        } else {
          store.dispatch('FedLogOut').then(() => {
            Message.error({
              message: err || 'Verification failed, please login again'
            })
            next({ path: '/login' })
          })
        }
      })
    } else {
      if (to.path.indexOf('realTime') > -1) {
        store.commit('SET_ISREAL', '1')
      } else {
        store.commit('SET_ISREAL', '0')
        store.commit('SET_REAL_FILTER', null)
        store.commit('SET_ALARM_DATA', [])
      }
      if (hasPermission(store.getters.permission_routers, to.path)) {
        next() //
      } else {
        next({ path: '/401', replace: true, query: { noGoBack: true } })
      }
    }
  } else {
    /* has no token*/
    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next()
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      next(`/login`)
      // next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
