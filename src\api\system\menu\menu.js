import request from '@/utils/request'
export function fetchList(data) {
  return request({
    url: '/userService/api/menu/list',
    method: 'post',
    data
  })
}

export function fetchDetail(data) {
  return request({
    url: '/userService/api/menu/detail',
    method: 'post',
    data
  })
}

export function fetchDelete(menuIds) {
  var data = { menuIds }
  return request({
    url: '/userService/api/menu/delete',
    method: 'post',
    data
  })
}

export function fetchCreate(data) {
  return request({
    url: '/userService/api/menu/create',
    method: 'post',
    data
  })
}

export function fetchUpdate(data) {
  return request({
    url: '/userService/api/menu/update',
    method: 'post',
    data
  })
}

export function fetchTreeList(data) {
  return request({
    url: '/userService/api/menu/tree',
    method: 'post',
    data
  })
}

export function fetchRoleTreeList(data) {
  return request({
    url: '/userService/api/menu/roleTree',
    method: 'post',
    data
  })
}

export function getMenuColumnList(data) {
  return request({
    url: '/userService/api/menu/getMenuColumnList',
    method: 'post',
    data
  })
}

