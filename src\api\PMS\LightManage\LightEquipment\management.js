import request from '@/utils/request'

// 获取分组
export function lampGroupList(data) {
  return request({
    url: '/energyService/api/lampGroup/lampGroupList',
    method: 'post',
    data
  })
}
// 添加分组
export function insertLampGroup(data) {
  return request({
    url: '/energyService/api/lampGroup/insertLampGroup',
    method: 'post',
    data
  })
}
// 更新分组
export function updateLampGroup(data) {
  return request({
    url: '/energyService/api/lampGroup/updateLampGroup',
    method: 'post',
    data
  })
}
// 删除分组
export function deleteLampGroup(data) {
  return request({
    url: '/energyService/api/lampGroup/deleteLampGroup',
    method: 'post',
    data
  })
}
// 迁移分组
export function moveLampGroupDevice(data) {
  return request({
    url: '/energyService/api/lampGroup/moveLampGroupDevice',
    method: 'post',
    data
  })
}

// 单个设备编辑分组
export function configLampGroupDevice(data) {
  return request({
    url: '/energyService/api/lampGroup/configLampGroupDevice',
    method: 'post',
    data
  })
}
// 获取列表
export function lampGroupDeviceList(data) {
  return request({
    url: '/energyService/api/lampGroup/lampGroupDeviceList',
    method: 'post',
    data
  })
}
// 列表详情
export function getLampGroupInfo(data) {
  return request({
    url: '/energyService/api/lampGroup/getLampGroupInfo',
    method: 'post',
    data
  })
}
// 开关配置详情
export function getLampConfigInfo(data) {
  return request({
    url: '/energyService/api/lampGroup/getLampConfigInfo',
    method: 'get',
    params: data
  })
}
// 开关配置下发
export function issuedInstruction(data) {
  return request({
    url: '/szdlService/api/deviceManage/issuedInstruction',
    method: 'post',
    data
  })
}

export function getLampBri(data) {
  return request({
    url: '/szdlService/api/deviceManage/getLampBri',
    method: 'post',
    data
  })
}

export function checkChnNo(data) {
  return request({
    url: '/szdlService/api/deviceManage/checkChnNo',
    method: 'post',
    data
  })
}

export function lampReboot(data) {
  return request({
    url: '/szdlService/api/deviceManage/lampReboot',
    method: 'post',
    data
  })
}

export function switchLights(data) {
  return request({
    url: '/szdlService/api/deviceManage/switchLights',
    method: 'post',
    data
  })
}

export function planDelete(data) {
  return request({
    url: '/szdlService/api/deviceManage/planDelete',
    method: 'post',
    data
  })
}

export function planDelivery(data) {
  return request({
    url: '/szdlService/api/deviceManage/planDelivery',
    method: 'post',
    data
  })
}

export function planDeliveryAll(data) {
  return request({
    url: '/szdlService/api/deviceManage/planDeliveryAll',
    method: 'post',
    data
  })
}

// 下发场景查询
export function planQuery(data) {
  return request({
    url: '/szdlService/api/deviceManage/planQuery',
    method: 'post',
    params: data
  })
}

// 实际场景查询
export function nowPlanQuery(data) {
  return request({
    url: '/szdlService/api/deviceManage/nowPlanQuery',
    method: 'post',
    params: data
  })
}
