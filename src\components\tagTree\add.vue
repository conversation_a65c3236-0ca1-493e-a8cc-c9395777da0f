<template>
  <div>
    <el-form ref="dataForm" :model="childData" :rules="rules" label-width="10.19vh">
      <el-row>
        <el-form-item label="标签名称" prop="tagName">
          <el-input v-model="childData.tagName" class="form-item-width" maxlength="20" />
        </el-form-item>
        <el-form-item label="排序" prop="orderNo">
          <el-input-number v-model="childData.orderNo" :min="0" :max="999" style="width: 27.7800vh;" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="childData.remark" type="textarea" maxlength="100" class="form-item-width" />
        </el-form-item>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handCancle">{{ $t('button.cancel') }}</el-button>
      <el-button type="primary" @click="handCreate" :loading="loading">{{ $t('button.confirm') }}</el-button>
    </div>
  </div>
</template>

<script>
import { insertDeviceTag, updateDeviceTag } from '@/api/otherSystem/equipmentManagement/tag'
export default {
  props: {
    showDialog: {
      type: Boolean
    },
    checked: {
      type: Object
    },
    type: {
      type: String
    }
    // deviceTypeId: {
    //   type: Number
    // }
  },
  data() {
    return {
      rules: {
        tagName: [{ required: true, message: '请输入标签名称', trigger: 'blur' }],
        // orderNo: [{ required: true, message: '请输入标签排序', trigger: 'change' }]

      },
      childData: {
        tagName: '',
        remark: '',
        orderNo: ''
        // upProductId: this.checked.id,
        // deviceTypeId: this.deviceTypeId
      },
      loading: false,
      lesseeList: []
    }
  },
  watch: {
    showDialog: function(n, o) {
      if (n) {
        this.$refs.dataForm.resetFields()
        this.init()
      }
    }
  },
  created() {
    // this.getLessee()
    this.init()
  },
  methods: {
    getLessee() {
      fetchLessee({ page: 1, limit: 999999 }).then(res => {
        if (res.data.code === 200) {
          this.lesseeList = res.data.data
        }
      })
    },
    init() {
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      if (this.type === 'edit') {
        this.childData.tagName = this.checked.tagName
        this.childData.orderNo = this.checked.orderNo
        this.childData.remark = this.checked.remark
        this.childData.tagId = this.checked.tagId
      } else {
        this.childData = {
          tagName: '',
          orderNo: '',
          remark: ''
          // upProductId: this.checked.id,
          // deviceTypeId: this.deviceTypeId
        }
      }
    },
    handCreate() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loading = true
          if (this.type === 'edit') {
            updateDeviceTag(this.childData).then(res => {
              this.loading = false
              if (res.data.code === 200) {
                this.$emit('handCancle', true)
                this.Alert({ alertType: 'update' })
              }
            }).catch(err => {
              this.loading = false
            })
          } else if (this.type === 'add') {
            insertDeviceTag(this.childData).then(res => {
              this.loading = false
              if (res.data.code === 200) {
                this.$emit('handCancle', true)
                this.Alert({ alertType: 'add' })
              }
            }).catch(err => {
              this.loading = false
            })
          }
        } else {
          this.Alert({ alertType: 'valid' })
        }
      })
    },
    handCancle() {
      this.$emit('handCancle', false)
    }
  }
}
</script>

<style>
</style>
