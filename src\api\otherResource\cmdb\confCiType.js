import request from '@/utils/request'

const url = '/cmdbService'
// const tempUrl = "confCiTempViewStorage";
export function queryAllCiTypeResource(params) {
  return request({
    url: url + '/confCiType/queryAllCiTypeResource',
    method: 'get',
    params: params
  })
} 

export function queryAllCiTypeList(params) {
  return request({
    url: url + '/confCiType/list',
    method: 'get',
    params: params
  })
}

export function saveResource(params) {
  return request({
    url: url + '/confCiType/',
    method: 'post',
    data: params
  })
}

export function queryAllISPResource(params) {
  return request({
    url: url + '/confCiType/queryAllISPResource/',
    method: 'get',
    params: params
  })
}

export function queryAllConfCiIcon() {
  return request({
    url: url + '/confCiType/queryAllConfCiIcon/',
    method: 'get'
  })
}

export function confCiTempViewStorage(data) {
  return request({
    url: url + '/confCiTempViewStorage',
    method: 'post',
    data: data
  })
}

export function queryTempViewList(data) {
  return request({
    url: url + '/confCiTempViewStorage/queryTempViewList/',
    method: 'post',
    data: data
  })
}

export function saveTypeRelations(data) {
  return request({
    url: url + '/confCiTypeRel/save',
    method: 'post',
    data: data
  })
}

export function getSonRelation(params) {
  return request({
    url: url + '/confCiTypeRel/getSonRelation',
    method: 'get',
    params: params
  })
}

export function deleteTempItem(id) {
  return request({
    url: url + `/confCiTempViewStorage/${id}`,
    method: 'delete'
  })
}

export function getResCiInstanceByTypeCode(typeCode) {
  return request({
    url: url + '/resCiInstance/getResCiInstanceByTypeCode',
    method: 'post',
    data: {
      typeCode
    }
  })
}

export function getAllRelationOfTypeCode(params) {
  return request({
    url: url + '/confCiTypeRel/getAllRelationOfTypeCode',
    method: 'get',
    params: params
  })
}

export function addConfCiInstanceRelByTopo(data) {
  return request({
    url: url + '/confCiInstanceRel/addConfCiInstanceRelByTopo',
    method: 'post',
    data: data
  })
}

export function getSonRelationOfVirtual(params) {
  return request({
    url: url + '/confCiTypeRel/getSonRelationOfVirtual',
    method: 'get',
    params: params
  })
}

export function getTypeRelTopo(params) {
  return request({
    url: url + '/confCiTypeRel/getTypeRelTopo',
    method: 'get',
    params: params
  })
}
