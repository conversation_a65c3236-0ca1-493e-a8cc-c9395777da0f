<template>
  <div>
    <!-- <div class="menu_container" ref="menuHome" @click="toggleMenu">
            <img :src="menuSrc">
        </div> -->
    <div v-for="(item,index) in menuItems" :id="item.cmdName" :key="index" class="menu_item" @click="clickMenu(item,index)">
      <el-tooltip v-if="item.cmdName == '查询运行状态'" class="item" content="查询运行状态" placement="top" effect="light">
        <a>
          <img style="width:1.8vw;height:1.8vw;" src="/static/img/lampPole/yxztBtn.png">
        </a>
      </el-tooltip>
      <el-tooltip v-if="item.cmdName == '重启设备'" class="item" content="重启设备" placement="right" effect="light">
        <a>
          <img style="width:1.8vw;height:1.8vw;" src="/static/img/lampPole/cqsbBtn.png">
        </a>
      </el-tooltip>
      <el-tooltip v-if="item.cmdName == '告警参数配置'" class="item" content="告警参数配置" placement="bottom" effect="light">
        <a>
          <img style="width:1.8vw;height:1.8vw;" src="/static/img/lampPole/gjcspzBtn.png">
        </a>
      </el-tooltip>
      <el-tooltip v-if="item.cmdName == '照明开关'" class="item" content="照明开关" placement="left" effect="light">
        <a>
          <img style="width:1.8vw;height:1.8vw;" src="/static/img/lampPole/zmkgBtn.png">
        </a>
      </el-tooltip>
      <el-tooltip v-if="item.cmdName == '属性卡'" class="item" content="属性卡" placement="top" effect="light">
        <a>
          <img style="width:1.8vw;height:1.8vw;" src="/static/img/lampPole/sxkBtn.png">
        </a>
      </el-tooltip>
      <el-tooltip v-if="item.cmdName == '查询通用查询'" class="item" content="查询通用查询" placement="left" effect="light">
        <a>
          <img style="width:1.8vw;height:1.8vw;" src="/static/img/lampPole/sxkBtn.png">
        </a>
      </el-tooltip>
      <!--item图-->
    </div>
  </div>
</template>

<script>
export default {
  props: { // 开放的属性，方便自定义
    menuSrc: {
      default: '/static/img/lampPole/yxztBtn.png'
    },
    position: {
      default: 'LT' // 可选择LT、LB、RT、RB4个角落
    },
    width: {
      default: 38
    },
    baseDistance: {
      default: 38
    },
    menuBg: {
      default: ''
    },
    itemBg: {
      default: ''
    },
    menuItems: {
      type: Array,
      default: () => []
    },
    keyValue: {
      type: Object
    },
    menuLeft: {
      default: ''
    },
    menuTop: {
      default: ''
    },
    refDiv: {
      default: ''
    }
  },
  data() {
    return {
      openFlag: false, // 展开合并标志
      operators: ['+', '+'] // 用于记录展开时动画XY方向
    }
  },
  watch: {
    refDiv(val) {
      this.init()
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      // 根据props初始化各内容的各种style
      // this.$refs.menuHome.style.width = this.width + 'px';
      // this.$refs.menuHome.style.height = this.width + 'px';
      // this.$refs.menuHome.style.lineHeight = this.width + 'px';
      // this.$refs.menuHome.style.background = this.menuBg;
      console.log(this.menuItems)
      if (this.menuItems !== null && this.menuItems.length > 0) {
        this.menuItems.forEach((item) => {
          const el = document.getElementById(item.cmdName)
          el.style.width = `${this.width * 0.8}px`
          el.style.height = `${this.width * 0.8}px`
          el.style.lineHeight = `${this.width * 0.8}px`
          el.style.background = this.itemBg
          el.style.display = 'none'
        })
      }
      // 根据position，选择不同的定位
      switch (this.position) {
      case 'LT':
        if (this.menuItems !== null && this.menuItems.length > 0) {
          // this.$refs.menuHome.style.left = '20px';
          // this.$refs.menuHome.style.top = '20px';
          this.menuItems.forEach((item) => {
            const el = document.getElementById(item.cmdName)
            el.style.left = this.menuLeft + 'px'
            el.style.top = this.menuTop + 'px'
          })
          this.operators = ['+', '+']
        }
        break
      }
    },
    toggleMenu() {
      if (!this.openFlag) { // 合并时，点击展开操作
        if (this.menuItems !== null && this.menuItems.length > 0) {
          this.menuItems.forEach((item, index) => {
            this.toggleMenuTransition(item.cmdName, index, false)
          })
          // menu本身转一周
          // this.$refs.menuHome.style.transform = 'rotate(360deg)';
        }
      } else {
        if (this.menuItems !== null && this.menuItems.length > 0) {
          this.menuItems.forEach((item, index) => {
            this.toggleMenuTransition(item.cmdName, index, true)
          })
          // menu恢复
          // this.$refs.menuHome.style.transform = 'rotate(0)';
        }
      }
      this.openFlag = !this.openFlag
    },
    toggleMenuTransition(cmdName, index, revert) {
      const oneArea = 360 / (this.menuItems.length) // 每一块所占的角度
      const axisX = Math.sin((this.menuItems.length - 1 - index) * oneArea * 2 * Math.PI / 360) // 横坐标所偏移的比例
      const axisY = Math.cos((this.menuItems.length - 1 - index) * oneArea * 2 * Math.PI / 360) // 纵坐标所偏移的比例
      const el = document.getElementById(cmdName) // 若所传的name一致，会报错。
      const that = this
      if (!revert) {
        setTimeout(function() {
          el.style.display = 'block'
          el.style.transitionDuration = '200ms'
          el.style.transform = `translate(${that.baseDistance * axisX}px,${that.baseDistance * axisY}px)` // 进行动画
          console.log(cmdName + ':' + index + ':' + el.style.transform)
        }, index * 100) // 通过定时器的方式，达到一个一个弹出来的效果
      } else {
        // item恢复
        el.style.transitionDuration = '200ms'
        el.style.transform = `translate(0,0)`
        el.style.display = 'none'
      }
    },
    clickMenu(item, index) {
      this.menuItems.forEach((item, index) => {
        this.toggleMenuTransition(item.cmdName, index, true)
      })
      // 暴露方法给父组件，进行点击事件的操作
      this.$emit('clickMenu', item, index, this.keyValue)
    },
    clearMenu() {
      this.menuItems.forEach((item, index) => {
        this.toggleMenuTransition(item.cmdName, index, true)
      })
    }
  }
}
</script>

<style>
    .menu_container {
        position: absolute;
        z-index: 100;
        transition-duration: 400ms;
        text-align: center;
    }

    .menu_item {
        position: absolute;
        z-index: 99;
        text-align: center;
    }
</style>
