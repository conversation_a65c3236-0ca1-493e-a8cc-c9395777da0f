<template>
  <draggable class="dragArea" tag="ul" :list="tasks" :group="{ name: 'form-design' }" @change="log">
    <el-row v-for="(row, i) in tasks" :key="i">
      <el-col v-for="(col, j) in row.cols" :key="j" :span="col.span">
        <p>{{ col.name }}</p>
        <nested-draggable :tasks="col.rows" />
      </el-col>
    </el-row>
  </draggable>
</template>
<script>
import draggable from 'vuedraggable'

export default {
  name: 'NestedDraggable',
  components: {
    draggable
  },
  props: {
    tasks: {
      required: true,
      type: Array
    }
  },
  methods: {
    log(e) {
      console.log('nested change', e)
    }
  }
}
</script>
<style scoped>
.dragArea {
  min-height: 4.6300vh;
  outline: 0.0926vh dashed;
  margin: 0.9260vh;
}
</style>
