import { constantRouterMap } from '@/router'
const _import = require('../../router/_import_' + process.env.NODE_ENV)
import Layout from '../../layout/index'
import FullScreenLayout from '../../views/layout/FullScreenLayout'
// import energyLayout from '../../views/layout/energyLayout'
import store from '@/store'
// import FullScreenLayout from '../../views/layout/FullScreenLayout'

const permission = {
  state: {
    routers: constantRouterMap,
    choosenRouters: [], // 被选中的左侧菜单
    addRouters: []
  },
  mutations: {
    SET_ROUTERS: (state, routers) => {
      state.addRouters = routers
      state.routers = constantRouterMap.concat(routers)
      // 如果一开始没有被选中，则指定第一个选中
      if (state.choosenRouters !== null && state.choosenRouters.length <= 0) {
        state.choosenRouters = routers[0]
      }
    },
    CHOOSE_ROUTERS: (state, routers) => { // 修改选中的左侧菜单
      state.choosenRouters = routers
    },
    RESET_ROUTERS: (state, routers) => { // 重置菜单（退出登录后重置）
      state = {
        routers: constantRouterMap,
        choosenRouters: [], // 被选中的左侧菜单
        addRouters: []
      }
    }
  },
  actions: {
    GenerateRoutesById({ commit }, index) {
      return new Promise(resolve => {
        const menuTree = store.getters.menuTree
        var accessedRouters = []
        // 后台首页
        // accessedRouters.push({
        //   path: '',
        //   component: Layout,
        //   redirect: 'dashboard',
        //   hidden: true,
        //   children: [{
        //     path: 'dashboard',
        //     component: _import('dashboard/admin/index'),
        //     name: 'dashboard',
        //     meta: { title: '首页', icon: 'dashboard', noCache: true }
        //   }]
        // })
        // 大屏首页
        accessedRouters.push({
          path: '/',
          component: FullScreenLayout,
          redirect: { name: 'dashboard' },
          hidden: true,
          children: [{
            path: 'dashboard',
            component: _import('views/dashboard/index'),
            name: 'dashboard',
            meta: { title: '首页', icon: 'dashboard', noCache: true }
          }]
        })
        menuTree.forEach(e => {
          if (e.menuId === parseInt(index)) {
            accessedRouters.push(e)
          }
        })

        accessedRouters.push({ path: '*', redirect: '/404', hidden: true })
        commit('SET_ROUTERS', accessedRouters)
        resolve()
      })
    },
    GenerateRoutes({ commit }, data) {
      return new Promise(resolve => {
        const menuTree = data
        var accessedRouters = []
        menuTreeFormat(menuTree)
        menuTree.push({ path: '*', redirect: '/404', hidden: true })
        accessedRouters = menuTree
        console.log(111, accessedRouters)

        commit('SET_ROUTERS', accessedRouters)
        resolve()
      })
    },
    ChooseRouters({ commit, state }, routers) { // 修改选中的左侧菜单
      console.log('修改左侧菜单')
      return new Promise(resolve => {
        commit('CHOOSE_ROUTERS', routers)
        resolve()
      })
    },
    ResetRouters({ commit, state }, routers) { // 修改选中的左侧菜单
      console.log('重置菜单')
      return new Promise(resolve => {
        commit('RESET_ROUTERS', routers)
        resolve()
      })
    },
    IsYulan({ commit, state }, routers) {
      return new Promise(resolve => {
        commit('SET_IS_YULAN', routers)
        resolve()
      })
    }
  }
}

function menuFormat(e) {
  if (e.componentType === 'layout' && e.menuUpId === 0) {
    e.component = Layout
  } else {
    if (e.componentType === 'url') {
      e.component = _import(e.component)
    }
    if (e.componentType === 'layout') { // 次级菜单
      e.component = _import(e.component)
      if (e.meta.title === '设备维修工单管理' || e.meta.title === '设备运维工单管理') {
        e.children = [...e.children, {
          name: '流程办理',
          path: '/flow/todo/:procInstId/:taskId',
          component: Layout,
          redirect: '/flow/todo/:procInstId/:taskId',
          children: [{
            name: 'ToDoPage',
            path: '/',
            component: () => import('@/views/otherResource/soc/flow/do/todo')
          }],
          hidden: true
        }, {
          name: 'NewWorkPage',
          path: '/instance/service-proc/proc-create/:modelKey',
          component: () => import('@/views/otherResource/soc/instance/service-proc/create'),
          hidden: true
        }, {
          name: 'NewTechnicalSupportWorkPage',
          path: '/instance/technical-support-proc/proc-create/:modelKey',
          component: () => import('@/views/otherResource/soc/instance/technical-support-proc/create'),
          hidden: true
        }]
      }
    }
  }
}

function menuTreeFormat(menuTree) {
  if (menuTree.length > 0) {
    menuTree.forEach(e => {
      menuFormat(e)
      if (e.children != null && e.children.length > 0) {
        menuTreeFormat(e.children)
      }
    })
  }
}

export default permission
