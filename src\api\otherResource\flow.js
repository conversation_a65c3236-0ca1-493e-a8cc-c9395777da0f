import request from '@/utils/request'

const url = '/flowService'

// 派单
export function sendExtand(data) {
  return request({
    url: '/spotService/api/basisSpot/creatOrderAlarm',
    method: 'post',
    data
  })
}

// 签单
export function webSign(id, type) {
  return request({
    url: url + `/${type}/webSign/` + id,
    method: 'post'
  })
}

// 回单
export function replayOrder(data, type) {
  return request({
    url: url + `/${type}/replayOrder`,
    method: 'post',
    data
  })
}

// 审核
export function auditOrder(data, type) {
  return request({
    url: url + `/${type}/auditOrder`,
    method: 'post',
    data
  })
}
// 批量回单
export function batchReplayOrder(data) {
  return request({
    url: url + '/instance-service-proc/batchReplayOrder',
    method: 'post',
    data
  })
}

/* 概览 */
// 2025 03 12 新增 工单概览 接口
// chart 数据
export function getWorkflowChartData(data) {
  return request({
    url: url + '/order-analysis/listOrderQuantityStatistics',
    method: 'post',
    data
  })
}

// 工单统计
export function getWorkflowData() {
  return request({
    url: url + '/order-analysis/getOrderAmount',
    method: 'get'
  })
}

// 工单处理排行
export function getDealRank(data) {
  return request({
    url: url + '/order-analysis/getReplayOrderSort',
    method: 'post',
    data
  })
}

// 设备工单排行
export function getDeviceRank(data) {
  return request({
    url: url + '/order-analysis/getDeviceTypeCountSort',
    method: 'post',
    data
  })
}

// 查询列表统计数据
export function getListStatistics(data) {
  return request({
    url: url + '/act-query/getListStatistics',
    method: 'post',
    data
  })
}

// 批量签单
export function flowableSign(data) {
  return request({
    url: url + '/flowable/sign',
    method: 'post',
    data
  })
}
// 批量签单
export function batchAuditOrder(data) {
  return request({
    url: url + '/technical-support/batchAuditOrder',
    method: 'post',
    data
  })
}
