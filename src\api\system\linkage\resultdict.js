import request from '@/utils/request'
export function fetchList(data) {
  return request({
    url: '/pmslinkage/api/linkage/resultDict/list',
    method: 'post',
    data
  })
}

export function fetchDetail(queryData) {
  return request({
    url: '/pmslinkage/api/linkage/resultDict/detail',
    method: 'get',
    params: queryData
  })
}

export function fetchDelete(ids) {
  var data = { ids }
  return request({
    url: '/pmslinkage/api/linkage/resultDict/delete',
    method: 'post',
    data
  })
}

export function fetchCreate(data) {
  return request({
    url: '/pmslinkage/api/linkage/resultDict/create',
    method: 'post',
    data
  })
}

export function fetchUpdate(data) {
  return request({
    url: '/pmslinkage/api/linkage/resultDict/update',
    method: 'post',
    data
  })
}
