import request from '@/utils/request'
export function fetchList(data) {
  return request({
    url: '/userService/api/dept/list',
    method: 'post',
    data
  })
}

export function fetchDetail(data) {
  return request({
    url: '/userService/api/dept/detail',
    method: 'post',
    data
  })
}

export function fetchDelete(deptIds) {
  var data = { deptIds }
  return request({
    url: '/userService/api/dept/delete',
    method: 'post',
    data
  })
}

export function fetchCreate(data) {
  return request({
    url: '/userService/api/dept/create',
    method: 'post',
    data
  })
}

export function fetchUpdate(data) {
  return request({
    url: '/userService/api/dept/update',
    method: 'post',
    data
  })
}

export function fetchDeptTree(data) {
  return request({
    url: '/userService/api/dept/tree',
    method: 'post',
    data
  })
}
