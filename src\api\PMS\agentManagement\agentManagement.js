import request from '@/utils/request'
export function fetchList(data) {
  return request({
    url: '/zhdxService/api/agent/list',
    method: 'post',
    data
  })
}

export function cancelAgent(data) {
  return request({
    url: '/zhdxService/api/agent/cancelAgent',
    method: 'post',
    data
  })
}

export function createAgent(data) {
  return request({
    url: '/zhdxService/api/agent/createAgent',
    method: 'post',
    data
  })
}

export function getWorkflowTaskList(data) {
  return request({
    url: '/zhdxService/api/agent/getWorkflowTaskList',
    method: 'post',
    data
  })
}

export function selectTreeMenu(data) {
  return request({
    url: '/zhdxService/api/agent/selectTreeMenu',
    method: 'post',
    data
  })
}

export function fetchGet(data) {
  return request({
    url: '/zhdxService/api/agent/get',
    method: 'post',
    data
  })
}
