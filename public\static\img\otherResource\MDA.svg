<svg width="85" height="89" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden"><defs><clipPath id="clip0"><rect x="71" y="420" width="85" height="89"/></clipPath><clipPath id="clip1"><rect x="0" y="0" width="790575" height="828675"/></clipPath><image width="130" height="137" xlink:href="data:image/png;base64,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" preserveAspectRatio="none" id="img2"></image><clipPath id="clip3"><rect x="0" y="0" width="786334" height="828675"/></clipPath></defs><g clip-path="url(#clip0)" transform="translate(-71 -420)"><g clip-path="url(#clip1)" transform="matrix(0.000104987 0 0 0.000104987 72 421)"><g clip-path="url(#clip3)" transform="matrix(1.00539 0 0 1 -0.0625 -0.25)"><use width="100%" height="100%" xlink:href="#img2" transform="scale(6048.72 6048.72)"></use></g></g></g></svg>