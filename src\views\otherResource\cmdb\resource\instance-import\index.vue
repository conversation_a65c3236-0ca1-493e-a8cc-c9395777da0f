<template>
  <div class="app-container">
    <search v-model="query" @search="get" />
    <el-table v-loading="waiting" :data="dataList" v-bind="tableConfig" v-on="tableEventConfig">
      <el-table-column width="100" type="index" label="序号" v-bind="columnConifg" />
      <el-table-column min-width="13.89vh" prop="fileName" label="文件名称" v-bind="columnConifg" />
      <el-table-column min-width="7.41vh" prop="createUserName" label="创建人" v-bind="columnConifg" />
      <el-table-column min-width="7.41vh" prop="createTime" label="创建时间" :formatter="table.formatMinute"
        v-bind="columnConifg" />
      <el-table-column min-width="7.41vh" prop="importStatus" label="导入状态" v-bind="columnConifg">
        <template slot-scope="scope">
          <el-tag :type="importInstanceData.filter({ id: scope.row.importStatus }).type"
            :color="importInstanceData.filter({ id: scope.row.importStatus }).color" effect="dark">
            {{ importInstanceData.get(scope.row.importStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column min-width="7.41vh" prop="startTime" label="开始时间" :formatter="table.formatMinute"
        v-bind="columnConifg" />
      <el-table-column min-width="7.41vh" prop="endTime" label="结束时间" :formatter="table.formatMinute"
        v-bind="columnConifg" />
      <el-table-column min-width="13.89vh" prop="exceptionResume" label="异常信息" v-bind="columnConifg" />
      <el-table-column v-if="showActionColumn" v-bind="columnFixConifg" :width="tableButtonWidth">
        <template slot-scope="scope">
          <template v-for="item in tableButtonList">
            <el-button v-if="item.visible(scope) !== false" :key="item.name + random.getUuid()" type="text"
              @click="item.click(scope)">{{ item.name }}</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <app-pager />
    <import-result v-if="v === 'import-result'" v-bind="{ id }" :is-dialog="true" @saved="get" />
  </div>
</template>
<script>
import { listPage } from '@/views/otherResource/soc/mixins'
import permission from '@/views/otherResource/soc/mixins/permission'
import importResult from './import-result'
import { importInstanceData } from '@/views/otherResource/cmdb/resource/data'
import search from './search'
import request from '@/utils/request'
import fileSaver from 'file-saver'
export default {
  components: { importResult, search },
  mixins: [listPage, permission],
  data() {
    return {
      importInstanceData
    }
  },
  computed: {
    tableButtonList() {
      return [
        { name: '导入详情', permission: 322151, visible: (scope) => '3456'.includes(scope.row.importStatus), click: (scope) => this.openBase('import-result', scope.row.id) },
        { name: '下载文件', permission: 322152, visible: (scope) => true, click: (scope) => this.downloadFile(scope.row) },
        { name: '删除', permission: 322153, visible: (scope) => scope.row.importStatus === '1', click: (scope) => this.del({ ...scope.row }) }
      ].filter(button => !button.permission || this.checkButtonPermission(button.permission))
    },
    showActionColumn() {
      return this.hasAnyActionPermission([322151, 322152, 322153])
    }
  },
  created() {
    this.get()
  },
  methods: {
    get() {
      this.getBase({
        url: '/cmdbService/instance-import-record/getList',
        params: this.query
      })
    },
    del(item) {
      this.delData({
        url: '/cmdbService/instance-import-record/' + item.id,
        success: () => this.get()
      })
    },
    downloadFile(item) {
      this.postData({
        url: '/cmdbService/instance-import-record/downloadExcel',
        params: { id: item.id },
        responseType: 'blob',
        isSuccessNotify: false,
        success: (r) => {
          const blob = new Blob([r.data], {
            type: 'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          })
          fileSaver.saveAs(blob, item.fileName)
        },
        error: (r) => {
          if (r.data.msg) this.notify.warn(r.data.msg)
        }
      })
    }
  }
}
</script>
