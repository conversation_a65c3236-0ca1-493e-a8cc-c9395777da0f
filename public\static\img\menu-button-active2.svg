<svg width="102" height="102" viewBox="0 0 102 102" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_2751_3811)">
<circle cx="50.9716" cy="50.9708" r="50.1353" transform="rotate(19.4543 50.9716 50.9708)" fill="#536DFC"/>
</g>
<circle cx="50.9716" cy="50.9708" r="48.6353" transform="rotate(19.4543 50.9716 50.9708)" stroke="url(#paint0_linear_2751_3811)" stroke-width="3" stroke-miterlimit="2" stroke-linecap="round" stroke-dasharray="50 50"/>
<defs>
<filter id="filter0_i_2751_3811" x="0.822266" y="0.821777" width="100.299" height="100.298" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="15"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.795199 0 0 0 0 0.826812 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2751_3811"/>
</filter>
<linearGradient id="paint0_linear_2751_3811" x1="50.9716" y1="0.835484" x2="50.9716" y2="101.106" gradientUnits="userSpaceOnUse">
<stop stop-color="#C7E3FD"/>
<stop offset="1" stop-color="#C5F1FF"/>
</linearGradient>
</defs>
</svg>
