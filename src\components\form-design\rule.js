import request from '@/utils/request'
export function getRules(ruleList, formAttr) {
  var rule = []
  ruleList.forEach(item => {
    if (item !== null && item !== undefined) {
      if (item.type === 'input') {
        rule.push({
          type: 'input',
          title: item.title,
          field: item.key,
          value: item.options.defaultValue,
          col: {
            span: parseInt(item.options.width),
            labelWidth: formAttr.labelWidth
          },
          props: {
            type: 'text',
            placeholder: item.options.placeholder,
            size: formAttr.size,
            disabled: item.options.disabled
          },
          className: '', // 自定义class
          validate: [{
            required: item.options.required,
            message: item.options.valid_str,
            type: 'string',
            trigger: 'blur',
            pattern: item.options.regexp,
            transform(value) {
              // if (formData.valid_type === 'number') { // 判断是不是数字型，是则需要做转换
              //     return parseInt(value);
              // }
              return value
            }
          }]
        })
      }
      // 输入文本框都是input，需要修改
      if (item.type === 'textarea') {
        rule.push({
          type: 'input',
          title: item.title,
          field: item.key,
          value: item.options.defaultValue,
          col: {
            span: parseInt(item.options.width),
            labelWidth: formAttr.labelWidth
          },
          props: {
            type: 'textarea',
            placeholder: item.options.placeholder,
            size: formAttr.size,
            disabled: item.options.disabled
          },
          className: '', // 自定义class
          validate: [{
            required: item.options.required,
            message: item.options.valid_str,
            type: 'string',
            trigger: 'blur',
            pattern: item.options.regexp,
            transform(value) {
              // if (formData.valid_type === 'number') { // 判断是不是数字型，是则需要做转换
              //     return parseInt(value);
              // }
              return value
            }
          }]
        })
      }
      if (item.type === 'number') {
        rule.push({
          type: 'InputNumber',
          title: item.title,
          field: item.key,
          value: item.options.defaultValue,
          col: {
            span: parseInt(item.options.width),
            labelWidth: formAttr.labelWidth
          },
          props: {
            placeholder: item.options.placeholder,
            size: formAttr.size,
            disabled: item.options.disabled,
            max: item.options.max,
            min: item.options.min
          },
          className: '', // 自定义class
          validate: [{
            required: item.options.required,
            message: item.options.valid_str,
            type: 'number',
            trigger: 'blur',
            pattern: item.options.regexp,
            transform(value) {
              return parseInt(value)
            }
          }]
        })
      }
      if (item.type === 'radio') {
        rule.push({
          type: 'radio',
          title: item.title,
          field: item.key,
          value: item.options.defaultValue, // radio和checkbox的值特殊
          col: {
            span: parseInt(item.options.width),
            labelWidth: formAttr.labelWidth
          },
          props: {
            size: formAttr.size,
            disabled: item.options.disabled
          },
          options: item.options.option,
          className: '', // 自定义class
          validate: [{
            required: item.options.required,
            message: item.options.valid_str,
            trigger: 'blur'
          }]
        })
      }
      if (item.type === 'checkbox') {
        rule.push({
          type: 'checkbox',
          title: item.title,
          field: item.key,
          value: item.options.defaultValue, // radio和checkbox的值特殊
          col: {
            span: parseInt(item.options.width),
            labelWidth: formAttr.labelWidth
          },
          props: {
            size: formAttr.size,
            disabled: item.options.disabled
          },
          options: item.options.option,
          className: '', // 自定义class
          validate: [{
            required: item.options.required,
            message: item.options.valid_str,
            trigger: 'blur'
          }]
        })
      }
      if (item.type === 'datetime') {
        var dateType = ''
        var type = 'date'
        if (item.options.type[0] === 'ymd') { // 判断时间类型的格式
          dateType = 'DatePicker'
          type = 'date'
        }
        if (item.options.type[0] === 'ym') { // 判断时间类型的格式
          dateType = 'DatePicker'
          type = 'month'
        }
        if (item.options.type[0] === 'y') { // 判断时间类型的格式
          dateType = 'DatePicker'
          type = 'year'
        }
        if (item.options.type[0] === 'hm') { // 判断时间类型的格式
          dateType = 'TimePicker'
        }
        if (item.options.type[0] === 'hms') { // 判断时间类型的格式
          dateType = 'TimePicker'
        }
        if (item.options.type[0] === 'ymdhm') { // 判断时间类型的格式
          dateType = 'DatePicker'
          type = 'datetime'
        }
        if (item.options.type[0] === 'ymdhms') { // 判断时间类型的格式
          dateType = 'DatePicker'
          type = 'datetime'
        }
        rule.push({
          type: dateType,
          title: item.title,
          field: item.key,
          value: item.options.defaultValue, // radio和checkbox的值特殊
          col: {
            span: parseInt(item.options.width),
            labelWidth: formAttr.labelWidth
          },
          props: {
            type: type,
            format: item.options.type[1],
            placeholder: item.options.placeholder,
            disabled: item.options.disabled,
            readonly: item.options.readonly
          },
          className: '', // 自定义class
          validate: [{
            required: item.options.required,
            message: item.options.valid_str,
            trigger: 'blur'
          }]
        })
      }
      if (item.type === 'select') {
        if (item.itemType === 1 && item.options.autoOption.url !== '') { // 动态远端获取数据
          item.options.option = []
          const json = item.options.autoOption.json
          const data = JSON.parse(json)
          request({
            url: item.options.autoOption.url,
            method: 'post',
            data
          }).then(result => {
            var list = result.data.data
            if (list !== null && list.length >= 0) {
              list.forEach(e => {
                item.options.option.push({
                  value: e[item.options.autoOption.value],
                  label: e[item.options.autoOption.label]
                })
              })
              // 默认值改为第一个选项的值
              item.options.defaultValue = list[0][item.options.autoOption.value]
            }
          })
        }
        rule.push({
          type: 'select',
          title: item.title,
          field: item.key,
          value: item.options.defaultValue, // radio和checkbox的值特殊
          col: {
            span: parseInt(item.options.width),
            labelWidth: formAttr.labelWidth
          },
          props: {
            disabled: item.options.disabled
          },
          options: item.options.option,
          className: '', // 自定义class
          validate: [{
            required: item.options.required,
            message: item.options.valid_str,
            trigger: 'blur'
          }]
        })
      }
      if (item.type === 'switch') {
        rule.push({
          type: 'switch',
          title: item.title,
          field: item.key,
          value: item.options.defaultValue, // radio和checkbox的值特殊
          col: {
            span: parseInt(item.options.width),
            labelWidth: formAttr.labelWidth
          },
          props: {
            disabled: item.options.disabled
          },
          className: '', // 自定义class
          validate: [{
            required: item.options.required,
            message: item.options.valid_str,
            trigger: 'blur'
          }]
        })
      }
      if (item.type === 'upload') {
        rule.push({
          type: 'upload',
          title: item.title,
          field: item.key,
          value: item.options.fileList,
          col: {
            span: parseInt(item.options.width),
            labelWidth: formAttr.labelWidth
          },
          props: {
            type: 'select',
            uploadType: 'image',
            action: item.options.url,
            multiple: item.options.multiple,
            disabled: item.options.disabled,
            accept: 'image\/*',
            format: ['jpg', 'jpeg', 'png', 'gif'],
            limit: item.options.limit,
            onSuccess: function(res, file, fileList) {
              console.log(res.data)
              return res.data.fileName
            }
          },
          className: '', // 自定义class
          validate: [{
            required: item.options.required,
            message: item.options.valid_str,
            trigger: 'blur'
          }]
        })
      }
    }
  })
  return rule
}

