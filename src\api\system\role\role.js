import request from '@/utils/request'
export function fetchList(data) {
  return request({
    url: '/userService/api/role/list',
    method: 'post',
    data
  })
}

export function fetchUserList(data) {
  return request({
    url: '/userService/api/role/getUserList',
    method: 'post',
    data
  })
}

export function fetchDetail(data) {
  return request({
    url: '/userService/api/role/detail',
    method: 'post',
    data
  })
}

export function fetchDelete(roleIds) {
  var data = { roleIds }
  return request({
    url: '/userService/api/role/delete',
    method: 'post',
    data
  })
}

export function fetchCreate(data) {
  return request({
    url: '/userService/api/role/create',
    method: 'post',
    data
  })
}

export function fetchUpdate(data) {
  return request({
    url: '/userService/api/role/update',
    method: 'post',
    data
  })
}
export function fetchRoleMenu(roleId) {
  const data = { roleId }
  return request({
    url: '/userService/api/role/getRoleMenuList',
    method: 'post',
    data
  })
}
export function fetchCreateRoleMenu(roleId, addIdList) {
  const data = { roleId, addIdList }
  return request({
    url: '/userService/api/role/insertRoleMenu',
    method: 'post',
    data
  })
}
export function fetchRoleDeptUser(roleId, deptId) {
  const data = { roleId, deptId }
  return request({
    url: '/userService/api/role/getDeptUserList',
    method: 'post',
    data
  })
}
export function fetchCreateRoleUser(roleId, addIdList) {
  const data = { roleId, addIdList }
  return request({
    url: '/userService/api/role/insertUserRole',
    method: 'post',
    data
  })
}

export function roleShift(data) {
  return request({
    url: '/userService/api/role/roleShift',
    method: 'post',
    data
  })
}

export function fetchSystem(data) {
  return request({
    url: '/userService/api/encryption/login/list',
    method: 'post',
    data
  })
}

export function fetchSystemAdd(data) {
  return request({
    url: '/userService/api/role/addEncryption',
    method: 'post',
    data
  })
}
