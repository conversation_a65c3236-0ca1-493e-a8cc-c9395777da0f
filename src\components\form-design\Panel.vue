<template>
  <div class="main">
    <el-form
      ref="form"
      :label-position="formAttr.align"
      :label-width="formAttr.labelWidth"
      :size="formAttr.size"
      :class="{'dragArea-empty':$store.state.formDesign.formList.length > 0}"
      :rules="$store.state.formDesign.rules"
    >
      <draggable
        class="dragArea dragArea-empty"
        :list="list"
        :group="{ name: 'form-design'}"
        @change="log"
      >
        <Cell
          v-for="(item, i) in $store.state.formDesign.formList"
          :key="i"
          :data="item"
          :form-attr="formAttr"
          @syncList="syncList"
        />
      </draggable>
    </el-form>
    <!-- 表单为空的时候默认是一个空容器 -->
    <!-- <draggable
      class="dragArea-empty"
      @change="log"
      :list="list"
      :group="{ name: 'form-design', pull: 'move' }"
      v-if="$store.state.formDesign.formList.length == 0"
    ></draggable>-->
  </div>
</template>

<script>
import Cell from '@/components/form-design/Cell.vue'
import draggable from 'vuedraggable'
import common from '@/utils/common'
import bus from '@/utils/bus'
import FDGridPanel from '@/components/form-design/FDGridPanel'

export default {
  components: {
    Cell,
    draggable,
    FDGridPanel
  },
  filters: {
    labelWidth(value) {
      return `${value}px`
    }
  },
  props: {
    formAttr: {
      type: Object,
      default: function() {
        return {
          align: 'top',
          size: 'medium',
          labelWidth: 80
        }
      }
    }
  },
  data() {
    return {
      'list': [
      ]
    }
  },
  watch: {
    list: {
      handler: function(value) {
      },
      deep: true
    }
  },
  mounted() {
    window.localStorage.formList = []
    bus.$on('formDesign.syncList', (list) => {
      console.log('list', list)
      this.syncList(list)
    })
  },
  methods: {
    log: function(evt) { // 拖动编辑器中的组件事件
      console.log(' 拖动编辑器中的组件事件')
      window.console.log(evt)
      console.log('common.deepClone(this.$store.state.formDesign.formList)', common.deepClone(this.$store.state.formDesign.formList))
      const newFormList = common.deepClone(this.$store.state.formDesign.formList)
      for (let i = 0; i < this.$store.state.formDesign.formList.length; i++) {
        const element = this.$store.state.formDesign.formList[i]
        if (element.type === 'grid') {
          newFormList[i] = common.deepClone(this.$store.state.formDesign.grid[element.key])
          console.log('this.$store.state.formDesign.grid', this.$store.state.formDesign.grid)
        }
      }
      console.log('newFormList', common.deepClone(newFormList))
      console.log(evt)
      let form
      if (evt.added) {
        form = evt.added.element
        const newIndex = evt.added.newIndex
        form.key = form.type + '_' + common.getGuidNew()
        newFormList.splice(newIndex, 0, form)
        this.$store.dispatch('formDesign/setFormList', common.deepClone(newFormList)) // 异步更新组件集合
        this.$store.commit('formDesign/updateActiveForm', common.deepClone(form)) // 更新组件
        this.$store.commit('formDesign/updateActiveKey', form.key) // 更新key标签
        this.$store.commit('formDesign/updateShowType', form.type) // 更新类型
        console.log('获取保存的结果')
        console.log(this.$store.state.formDesign.formList)
        console.log(this.$store.state.formDesign.activeForm)
        this.syncList(newFormList)
      }
      if (evt.moved) {
        form = evt.moved.element
        // eslint-disable-next-line no-unused-vars
        // const newIndex = evt.moved.newIndex
        // eslint-disable-next-line no-unused-vars
        // const oldIndex = evt.moved.oldIndex
        // 先直接在新位置插入表单，然后旧位置索引+1的位置表单直接减掉
        // newFormList.splice(newIndex, 0, form);
        // newFormList.splice(oldIndex, oldIndex + 1);
        this.$store.commit('formDesign/updateShowType', form.type)
        this.$store.commit('formDesign/updateActiveKey', form.key)
        this.$store.commit('formDesign/updateActiveForm', common.deepClone(form))
        this.$store.dispatch('formDesign/setFormList', common.deepClone(this.list))
        this.syncList(newFormList)
      }
    },
    syncList(value) {
      this.$emit('callback', value)
      this.list = common.deepClone(value)
      bus.$emit('formMenu.init')
      console.log('调用编辑器回调方法')
    },
    init(value) {
      this.list = value
      bus.$emit('formMenu.init')
      this.$store.dispatch('formDesign/setFormList', common.deepClone(value))
    }
  }
}
</script>

<style lang="css" scoped>
.main {
  width: 100%;
  height: 100%;
  min-height: 46.3000vh;
}
.dragArea-empty {
  min-height: 46.3000vh;
}
</style>

