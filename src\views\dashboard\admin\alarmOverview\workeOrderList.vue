<template>
  <layout width="129.64vh" header="设备在途工单" class="dashboard-dialog">
    <el-table v-loading="waiting" :data="dataList" v-bind="tableConfig" v-on="tableEventConfig">
      <el-table-column width="100px" type="index" label="序号" v-bind="columnConifg" />
      <el-table-column min-width="250px" prop="alarmMsg" label="工单内容（告警信息）" sortable="custom" v-bind="columnConifg" />
      <el-table-column min-width="150px" prop="orderType" label="工单状态" sortable="custom" v-bind="columnConifg" />
      <el-table-column min-width="150px" prop="dispatchTime" label="派单时间" sortable="custom"
        :formatter="table.formatMinute" v-bind="columnConifg" />
        <el-table-column min-width="150px" prop="currentStepName" label="当前步骤" v-bind="columnConifg" />
      <el-table-column min-width="150px" prop="signUserName" label="签单人" sortable="custom" v-bind="columnConifg" />
      <el-table-column min-width="150px" prop="createUserName" label="发起人" sortable="custom" v-bind="columnConifg" />
    </el-table>
    <!-- <app-pager :parent="this" /> -->
  </layout>
</template>
<script>
import { listPage } from '@/views/otherResource/soc/mixins'
import { flowListPage } from '@/views/otherResource/soc/flow/mixins/'
export default {
  props: {
    resourceSn: {
      type: String,
      required: true
    },
    showDialog: {
      type: Boolean,
      default: false
    }
  },
  mixins: [listPage, flowListPage],
  created() {
    this.addMessageListener('refreshTaskDone', this.get)
    this.get()
  },
  watch: {
    showDialog(val) {
      if (val) {
        this.get()
      }
    },
    resourceSn() {
      this.get()
    }
  },
  methods: {
    get() {
      this.getBase({
        // url: '/flowService/act-query/getTodoList',
        url: '/flowService/act-query/getNewTodoList',
        data: { keyName: this.resourceSn },
        method: 'post'
      })
    },
    handCancle() {
      this.$emit('handCancle')
    }
  }
}
</script>
