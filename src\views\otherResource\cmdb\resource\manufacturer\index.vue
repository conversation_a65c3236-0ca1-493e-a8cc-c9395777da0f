<!--
 * @Descripttion: 
 * @Author: wangl
 * @version: 
 * @Date: 2025-03-04 09:08:12
 * @LastEditors: wangl
 * @LastEditTime: 2025-03-24 15:58:06
-->
<template>
  <div class="app-container">
    <search v-model="query" @search="get" />
    <div class="filter-container filter">
      <el-button v-if="checkButtonPermission(322131)" v-bind="addBtnConfig" @click="openBase('edit')">新增</el-button>
    </div>
    <el-table v-loading="waiting" :data="dataList" v-bind="tableConfig" v-on="tableEventConfig">
      <el-table-column width="100" type="index" label="序号" v-bind="columnConifg" />
      <el-table-column min-width="9.26vh" prop="manufacturerName" label="厂商" v-bind="columnConifg" />
      <el-table-column min-width="9.26vh" prop="manufacturerEname" label="英文名" v-bind="columnConifg" />
      <el-table-column min-width="13.89vh" prop="manufacturerFullName" label="厂商全称" v-bind="columnConifg" />
      <el-table-column min-width="13.89vh" prop="phoneNumber" label="电话号码" v-bind="columnConifg" />
      <el-table-column min-width="13.89vh" prop="address" label="地址" v-bind="columnConifg" />
      <el-table-column v-bind="columnFixConifg" :width="tableButtonWidth" v-if="showActionColumn">
        <template slot-scope="scope">
          <template v-for="item in tableButtonList">
            <el-button v-if="item.visible !== false" :key="item.name + random.getUuid()" type="text"
              @click="item.click(scope)">{{ item.name }}</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <app-pager />
    <edit v-if="v === 'edit'" v-bind="{ id }" :is-dialog="true" @saved="get" />
  </div>
</template>
<script>
import edit from './edit'
import { listPage } from '@/views/otherResource/soc/mixins'
import permission from '@/views/otherResource/soc/mixins/permission'
import search from './search'
export default {
  components: { edit, search },
  mixins: [listPage, permission],
  computed: {
    tableButtonList() {
      return [
        { name: '编辑', permission: 322132, click: (scope) => this.openBase('edit', scope.row.manufacturerId) },
        { name: '删除', permission: 322133, click: (scope) => this.del({ ...scope.row }) }
      ].filter(button => !button.permission || this.checkButtonPermission(button.permission))
    },
    showActionColumn() {
      return this.hasAnyActionPermission([322132, 322133])
    }
  },
  created() {
    this.get()
  },
  methods: {
    get() {
      this.getBase({
        url: '/cmdbService/manufacturer/page',
        params: this.query
      })
    },
    del(item) {
      this.delData({
        url: '/cmdbService/manufacturer/' + item.manufacturerId,
        success: () => this.get()
      })
    }
  }
}
</script>
