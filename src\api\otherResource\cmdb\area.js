import request from '@/utils/request'

export function getRoadlistByArea(data) {
  return request({
    url: `/cmdbService/api/layerManagement/roadList`,
    method: 'post',
    data
  })
}

export function geAreaList(data) {
  return request({
    url: `/cmdbService/api/layerManagement/list`,
    method: 'post',
    data
  })
}

export function getGroupList(params) {
  return request({
    url: `/cmdbService/api/layerManagement/getGroupList`,
    method: 'get',
    params
  })
}

export function findChildrenList(data) {
  return request({
    url: `/cmdbService/api/layerManagement/findChildrenList`,
    method: 'post',
    data
  })
}

export function getMakeLayerCode(params) {
  return request({
    url: '/cmdbService/api/layerManagement/makeLayerCode',
    method: 'get',
    params
  })
}
