<template>
  <div class="app-container flow-container">
    <div class="flow-fr pd-0">
      <div class="header">
        <el-row :gutter="20">
          <el-col v-for="item in headerItems" :key="item.title" :xs="8" :sm="8" :md="8" :lg="8" :xl="4">
            <div class="header-item" :style="{ borderLeft: `4px solid ${item.border}` }">
              <div class="header-item-left">
                <div :class="[item.icon, 'icon']" />
              </div>
              <div class="header-item-right">
                <div class="header-text">{{ statisticstObj[item.text] || 0 }}</div>
                <div class="header-title">{{ item.title }}</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="flow-fr">
      <search v-model="query" @search="get" />
      <div style="clear:both" />
      <div class="table-button">
        <el-button 
          v-if="checkButtonPermission('324221')"
          v-waves 
          icon="el-icon-download" 
          style="height: 35px" 
          :loading="exportLoading"
          @click.stop="handleExportExcel"
        >导出</el-button>
      </div>
      <el-table v-loading="waiting" :data="dataList" v-bind="tableConfig" @sort-change="sortChange">
        <el-table-column width="100" type="index" label="序号" v-bind="columnConifg" />
        <el-table-column min-width="23.15vh" prop="alarmMsg" label="工单内容（告警信息）" sortable="custom"
          v-bind="columnConifg" />
        <el-table-column min-width="23.15vh" prop="resourceName" label="资源名称" sortable="custom" v-bind="columnConifg" />
        <el-table-column min-width="13.89vh" prop="assetTypeName" label="设备类型" sortable="custom"
          v-bind="columnConifg" />
        <el-table-column min-width="13.89vh" prop="faultReason" label="故障原因" sortable="custom" v-bind="columnConifg">
          <template slot-scope="scope">
            <span>
              {{ ['其他', '电力故障', '光缆故障', '设备故障'][scope.row.faultReason] || '-' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column min-width="13.89vh" prop="comments" label="处理意见" sortable="custom" v-bind="columnConifg" />
        <el-table-column min-width="13.89vh" prop="createUserName" label="发起人" sortable="custom"
          v-bind="columnConifg" />
        <el-table-column min-width="13.89vh" prop="receiverName" label="接收人" sortable="custom" v-bind="columnConifg" />
        <el-table-column min-width="13.89vh" prop="orderLevel" label="工单级别" sortable="custom" v-bind="columnConifg" />
        <el-table-column min-width="13.89vh" prop="influenceScope" label="影响范围" sortable="custom" v-bind="columnConifg">
          <template slot-scope="scope">
            <span>
              {{ scope.row.influenceScope === '1' ? '大' : scope.row.influenceScope === '2' ? '中'
                : scope.row.influenceScope === '3' ? '小' : '' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column min-width="13.89vh" prop="orderType" label="工单状态" sortable="custom" v-bind="columnConifg" />
        <el-table-column min-width="11.11vh" prop="assetOnlineStatus" label="在线状态" sortable="custom"
          v-bind="columnConifg">
          <template slot-scope="scope">
            <span v-if="scope.row.assetOnlineStatus === null">-</span>
            <el-tag v-else :type="scope.row.assetOnlineStatus === 0 ? 'info' : 'success'">{{ scope.row.assetOnlineStatus
              === 0 ? '离线' : '在线' }}</el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column min-width="120px" prop="assetAlarmStatus" label="告警信息" sortable="custom" v-bind="columnConifg">
          <template slot-scope="scope">
            <span v-if="scope.row.assetAlarmStatus === null">-</span>
            <el-tag v-else :type="scope.row.assetAlarmStatus === 1 ? 'danger' : 'success'">{{ scope.row.assetAlarmStatus
              === 1 ? '有' : '无' }}</el-tag>
          </template>
        </el-table-column> -->
        <el-table-column min-width="13.89vh" prop="signUserName" label="签单人" sortable="custom" v-bind="columnConifg" />
        <el-table-column min-width="13.89vh" prop="areaName" label="设备所在地" sortable="custom" v-bind="columnConifg" />
        <el-table-column min-width="13.89vh" prop="dispatchTime" label="派单时间" sortable="custom"
          :formatter="table.formatMinute" v-bind="columnConifg" />
        <el-table-column min-width="13.89vh" prop="updateTime" label="更新时间" sortable="custom"
          :formatter="table.formatMinute" v-bind="columnConifg" />
        <el-table-column v-bind="columnFixConifg" :width="tableButtonWidth">
          <template slot-scope="scope">
            <div v-if="hasAnyActionPermission(['324222'])">
              <template v-for="item in tableButtonList">
                <el-button 
                  v-if="item.visible(scope) && checkButtonPermission('324222')" 
                  :key="item.name + random.getUuid()" 
                  type="text"
                  @click="item.click(scope)"
                >{{ item.name }}</el-button>
              </template>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <app-pager />
      <undo v-if="v === 'undo'" v-bind="{ procInstId }" :is-dialog="true" @saved="get" />
    </div>
  </div>
</template>
<script>
import search from '../search'
import { listPage } from '@/views/otherResource/soc/mixins'
import { flowListPage } from '@/views/otherResource/soc/flow/mixins/'
import { getListStatistics } from '@/api/otherResource/flow.js'
import undo from '../components/undo'
import download from '@/views/otherResource/soc/mixins/download'
import permission from '@/views/otherResource/soc/mixins/permission'

export default {
  components: { undo, search },
  mixins: [listPage, flowListPage, download, permission],
  data() {
    return {
      headerItems: [
        { title: '工单总数', text: 'total', icon: 'icon-total', border: '#7733FF' },
        { title: '待签单', text: 'send', icon: 'icon-send', border: '#198CFF' },
        { title: '待回单', text: 'sign', icon: 'icon-sign', border: '#1AE65D' },
        { title: '已挂起', text: 'hang', icon: 'icon-hang', border: '#47EBB4' },
        { title: '待审核', text: 'reply', icon: 'icon-reply', border: '#FFB54C' },
        { title: '已办结', text: 'complete', icon: 'icon-complete', border: '#EB4747' }
      ],
      statisticstObj: {
        complete: 0,
        hang: 0,
        reply: 0,
        send: 0,
        sign: 0,
        total: 0
      },
      exportLoading: false,
      procInstId: null,
      query: {
        page: 1,
        limit: 10,
        orderBy: '',
        order: ''
      }
    }
  },
  computed: {
    tableButtonList() {
      return [
        { name: '详情', click: (scope) => this.openDone({ procInstId: scope.row.procInstId }), visible: () => true }
        // {
        //   name: '撤销',
        //   click: (scope) => {
        //     this.v = 'undo'
        //     this.procInstId = scope.row.procInstId
        //   },
        //   visible: (scope) => !scope.row.endActId && scope.row.isUndo !== '1'
        // },
        // {
        //   name: '还原',
        //   click: (scope) => this.recoverUndo({ procInstId: scope.row.procInstId, success: this.get }),
        //   visible: (scope) => !scope.row.endActId && scope.row.isUndo === '1'
        // },
        // {
        //   name: '删除',
        //   click: (scope) => {
        //     this.deleteProc({ procInstId: scope.row.procInstId, success: this.get })
        //   },
        //   visible: (scope) => !scope.row.endActId
        // }
      ]
    }
  },
  created() {
    this.addMessageListener('refreshTaskDone', this.get)
    this.get()
    this.getStatisticst()
  },
  methods: {
    get() {
      const data = { ...this.query }
      if (data.areaId) {
        data.areaId = data.areaId[data.areaId.length - 1]
      }
      this.getBase({
        url: '/flowService/act-query/getNewList',
        data,
        method: 'post'
      })
    },
    sortChange(row) {
      this.query.orderBy = row.prop
      this.query.order = row.order === 'ascending' ? 'ASC' : row.order === 'descending' ? 'DESC' : null
      this.get()
    },
    getStatisticst() {
      getListStatistics({}).then(res => {
        if (res.data.code === 0) {
          Object.assign(this.statisticstObj, res.data.data)
        }
      })
    },
    // 列表导出
    handleExportExcel() {
      // 处理loading
      this.exportLoading = true
      const data = { ...this.query }
      if (data.areaId) {
        data.areaId = data.areaId[data.areaId.length - 1]
      }
      delete data.page
      delete data.limit
      const url = '/flowService/act-query/allListExportExcel'
      const title = `全部工单导出报表`

      this.download({
        url,
        data,
        method: 'post',
        fileName: `${title}.xlsx`
      }, 'application/vnd.ms-excel').then(res => {
        this.exportLoading = false
      }).catch(() => {
        this.exportLoading = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.flow-container {
  background-color: unset;
  margin: 0;
  padding: 0;

  .flow-fr {
    margin: 20px;
    padding: 20px;
    background-color: rgba(9, 72, 148, 1); //改颜色
  }

  .flow-fr.pd-0 {
    padding-bottom: 0;
  }
}

.header {
  background-color: rgba(9, 72, 148, 1); //改颜色
  border-radius: 4px;
}

.header-item {
  border: 1px solid #ccc;
  border-radius: 10px;
  height: 100px;
  padding: 20px;
  margin-bottom: 20px;
}

.header-item-left {
  float: left;
  width: 28%;
  height: 60px;
}

.header-text {
  height: 36px;
  line-height: 44px;
  font-size: 28px;
  font-weight: bolder;
  color: #e8e7f0; //改颜色
}

.header-title {
  color: #999999
}

.header-item-right {
  float: left;
  margin-left: 15%;
}

.icon {
  display: inline-block;
  width: 100%;
  height: 100%;
  background-size: 100% 100%;
  margin: 0 auto;
}

.icon-total {
  background-image: url('/static/img/flow/total.svg')
}

.icon-hang {
  background-image: url('/static/img/flow/hang.svg')
}

.icon-reply {
  background-image: url('/static/img/flow/reply.svg')
}

.icon-send {
  background-image: url('/static/img/flow/send.svg')
}

.icon-sign {
  background-image: url('/static/img/flow/sign.svg')
}

.icon-complete {
  background-image: url('/static/img/flow/complete.svg')
}

.table-button {
  margin-top: 10px;
  margin-bottom: 10px;
  float: right;
}
</style>
