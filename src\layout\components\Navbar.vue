<template>
  <div class="navbar">
    <div class="logo">
      <img src="/static/img/tx_logo.png" style="height: 60%;">
    </div>
    <!-- <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" /> -->
    <el-menu v-if="!noMenu && chooseIndex > 0" :default-active="getIndex(routes[chooseIndex - 1])" :router="true"
      class="left-menu" mode="horizontal">
      <el-menu-item v-for="(route, menuIndex) in routes" :key="route.id" v-popover="'subMenuPop' + menuIndex"
        :index="getIndex(route)" class="navBarMenu active-text-color" @click.native="chooseRouters(route, menuIndex)">
        <a v-if="route.type === '1'" class="active-text-color is-active navA" :href="route.path">
          {{ route.name }}
        </a>
        <template v-else>
          {{ showName(route) }}
        </template>
      </el-menu-item>
    </el-menu>
    <div class="right-menu">
      <div class="avatar-container right-menu-item">
        <div v-if="$store.getters.firstMenu.dashboard && !noMenu" class="avatar-wrapper-user"
          @click="$router.push({ name: $store.getters.firstMenu.dashboard })">
          <el-tooltip class="item" content="进入大屏" placement="bottom" effect="light">
            <svg-icon style="font-size: 2.2224vh;" icon-class="houtai" />
          </el-tooltip>
        </div>
        <div class="avatar-wrapper-user">
          <el-dropdown>
            <span class="el-dropdown-link">
              <el-badge :value="total" class="item">
                <!-- 点我查看<i class="el-icon-caret-bottom el-icon--right"></i> -->
                <!-- <svg-icon icon-class="jjgj" style="font-size: 1vw;margin-left:0.5vw;" /> -->
                <i style="font-size: 2.2224vh;color:#ffffff;" class="el-icon-chat-dot-round" />
                <!-- <span><img src="/static/img/gjdt.gif" alt=""> </span> -->
              </el-badge>
            </span>
            <el-dropdown-menu slot="dropdown" class="elDropdownMenu">
              <el-dropdown-item class="clearfix" @click.native="noDealAlarmClick">
                <svg-icon icon-class="wclgjs" style="font-size: 1vw;" />
                实时告警
                <el-badge class="mark" :value="noDealAlarm" />
              </el-dropdown-item>
              <el-dropdown-item class="clearfix" @click.native="openToDoListTable">
                <svg-icon icon-class="dclgd" style="font-size: 1vw;" />
                待处理工单
                <el-badge class="mark" :value="noDealOrder" />
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <div class="avatar-wrapper-user" @click="modifyUserInfo">
          <el-tooltip class="item" :content="'用户信息:' + userName" placement="bottom" effect="light">
            <svg-icon style="font-size: 2.2224vh;" icon-class="user" />
          </el-tooltip>
        </div>
        <!-- <div class="avatar-wrapper-name">{{userName}}</div> -->
        <div class="avatar-wrapper" @click="logout">
          <el-tooltip class="item" content="退出" placement="bottom" effect="light">
            <svg-icon style="font-size: 2.2224vh;margin-right: 2.7780vh;" icon-class="logout" />
          </el-tooltip>
        </div>
        <!-- <div v-if="!creenClose" class="Close" style="cursor: pointer;" @click="getFullCreeen">
                    <svg-icon icon-class="fangda" style="color:#ffffff;font-size: 1vw;margin-left:0.5vw;margin-top: 1.6vh;" />
                    <span style="font-size: 0.6vw;color: #7AB9FF;margin-right:8px;position: relative;bottom: 2px;">全屏</span>
                </div>
                <div v-if="creenClose" class="Close" style="cursor: pointer;" @click="getFullCreeen">
                    <svg-icon icon-class="suoxiao" style="color:#ffffff;font-size: 0.8vw;margin-left:0.5vw;margin-top: 1.8vh;" />
                    <span style="font-size: 0.6vw;color: #7AB9FF;margin-right:8px;position: relative;bottom: 2px;">缩小</span>
                </div> -->
      </div>
    </div>
    <el-dialog ref="dialog" v-dialogDrag class="alarmDialogDetail" title="实时告警信息列表" :visible.sync="warnBoxVisible"
      width="80vw" @close="hideWarnBox">
      <dealAlarmList :show-dialog="warnBoxVisible" :query-type="1" :is-layout-nav="true" />
    </el-dialog>
    <to-do-list-table v-if="v === 'to-do-list-table'" :is-dialog="true" />
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import dealAlarmList from '@/views/dashboard/admin/alarmOverview/noDealAlarmList.vue'

import { getBasisSpotToDo } from '@/api/main/admin'
import { toDoListTable } from '@/views/otherResource/soc/flow/components'
// import config from '/static/js/config'
export default {
  components: {
    toDoListTable,
    dealAlarmList
  },
  computed: {
    total() {
      return parseInt(this.noDealOrder) + parseInt(this.noDealAlarm)
    },
    noMenu() {
      return this.$store.getters.menuTree.find(ele => ele.path === '/welcome')
    },
    ...mapGetters(['sidebar', 'avatar', 'userId']),
    ...mapState({
      choosen_router: (state) => state.permission.choosenRouters,
      encrypt_userId: (state) => state.user.encryptUserId,
      permission_routers: (state) => state.permission.routers
    })
  },
  data() {
    return {
      isLogOut: false,
      chooseIndex: 0,
      data: {},
      routes: [],
      showPover: false,
      userName: '',
      fullscreen: false,
      creenClose: false,
      noDealAlarm: 0, // 未处理告警数
      noDealOrder: 0, // 未处理工单数
      warnBoxVisible: false,
      interval: null
    }
  },
  watch: {
    permission_routers: function (n, o) {
      // this.initRouters()
    }
  },
  created() {
    this.findBasisSpotToDo()
    this.getOrderCount()
    this.interval = setInterval(() => {
      this.findBasisSpotToDo()
      this.getOrderCount()
    }, 60000)
  },
  mounted() {
    this.userName = this.$store.getters.name
    // 自动退出
    this.isLogOut = this.$route.query.logout
    if (this.isLogOut) {
      this.$store.dispatch('LogOut').then(() => {
        this.$router.push(`/login?redirect=${this.$route.fullPath}`)
      })
    }
    this.initRouters()
  },
  beforeDestroy() {
    clearInterval(this.interval)
  },
  methods: {
    // 获取代办的内容
    findBasisSpotToDo() {
      getBasisSpotToDo()
        .then((res) => {
          if (res.data.code === 200) {
            this.noDealAlarm = res.data.data.noDealAlarm
          }
        })
        .catch((err) => {
          console.log('err', err)
        })
    },
    getOrderCount() {
      this.getData({
        url: '/flowService/act-query/getTodoCount',
        success: (r) => {
          this.noDealOrder = r.data.data
        }
      })
    },
    getFullCreeen() {
      var element = document.documentElement
      if (this.fullscreen) {
        this.creenClose = false
        if (document.exitFullscreen) {
          document.exitFullscreen()
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen()
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen()
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen()
        }
      } else {
        this.creenClose = true
        if (element.requestFullscreen) {
          element.requestFullscreen()
        } else if (element.webkitRequestFullScreen) {
          element.webkitRequestFullScreen()
        } else if (element.mozRequestFullScreen) {
          element.mozRequestFullScreen()
        } else if (element.msRequestFullscreen) {
          // IE11
          element.msRequestFullscreen()
        }
      }
      this.fullscreen = !this.fullscreen
    },
    handSelect(indexPath, index) {
      if (this.chooseIndex > 0) {
        document.getElementsByClassName('navBarMenu')[this.chooseIndex - 1].classList.remove('is-active')
      }
      // if (indexPath === '/') { // 点击首页
      //     this.handleClickOutside()
      // } else if (index[0] === null && indexPath === null & (this.$route.name === 'Dashboard')) { // 点击顶层父级
      //     this.handleClickOutside()
      // } else {
      //     this.toggleSideBar()
      // }
      // if (indexPath !== null) {
      //   this.$store
      //     .dispatch('ChooseRouters', null)
      //     .then(() => { })
      //     .catch(() => {
      //       console.log('修改左侧菜单失败！')
      //     })
      // }
    },
    handCancle() { },
    modifyUserInfo() { },
    toggleSideBar() {
      this.$store.dispatch('app/openSideBar', { withoutAnimation: false })
    },
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    },
    async logout() {
      this.$confirm('准备退出系统, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await this.$store.dispatch('LogOut')
          if (this.$store.getters.isNewLogin === 'true') {
            window.location.href = process.env.NODE_ENV === 'production' ? window.config.VUE_APP_REDIRECT_LOGIN : 'http://**************/#/login?client_id=DeviceManagement&redirect_uri=http://localhost:9528/newLogin&response_type=code&state=ejDeIX'
          } else {
            this.$router.push(`/login?redirect=${this.$route.fullPath}`)
          }
        })
        .catch(() => { })
    },
    initRouters() {
      // 初始化读取的权限菜单
      this.routes = []
      let router = null
      let index = 0
      this.chooseIndex = 0
      if (this.permission_routers !== null && this.permission_routers !== undefined) {
        this.permission_routers.forEach((item) => {
          // index++
          // this.routes.push(item)
          if (!item.hidden && !['/', '/welcome'].includes(item.path)) {
            // 如果是隐藏目录则不显示
            this.routes.push(item)
          }
        });
        // 使用 for...of 循环替代 forEach，这样可以使用 break 语句
        for (const item of this.permission_routers) {
          if (!item.hidden && !['/', '/welcome'].includes(item.path)) {
            // 如果是隐藏目录则不显示
            index++;
            var result = this.getRouter(item, 0)
            if (result) {
              router = result;
              this.chooseIndex = index;
              console.log('当前菜单', router);
              break; // 找到第一个匹配的路由后立即退出循环
            }
          }
        }
      }
      this.$store
        .dispatch('ChooseRouters', router)
        .then(() => {
          //document.getElementsByClassName('navBarMenu')[this.chooseIndex - 1].classList.add('is-active')
        })
        .catch(error => {
          console.log('修改左侧菜单失败:', error)
        })
    },
    getRouter(router, type) {
      if (router.children) {
        var result = null
        router.children.forEach((item) => {
          if (!result) {
            result = this.getRouter(item, type + 1)
          }
        })
        if (result) {
          if (type === 0) {
            return router
          } else {
            return result
          }
        }
      } else {
        if (router.path !== '/') {
          if (router.name === this.$route.name || `${this.$route.path}?sysType=${this.$route.query.sysType}`.indexOf(router.path) !== -1) {
            return router
          }
        }
      }
    },
    hasOneShowingChildren(children) {
      // 是否直接显示只有一个子菜单的对象
      if (children) {
        const showingChildren = children.filter((item) => {
          return !item.hidden
        })
        if (showingChildren.length === 1) {
          return true
        }
      }
      return false
    },
    showName(item) {
      if (!item.hidden) {
        if (!item.meta && item.children !== undefined) {
          return item.children[0].meta.title
        }
        return item.meta.title
      }
    },
    hiddenProver(router) {
      // 隐藏对应的值
      this.routes.forEach((item) => {
        if ((item.menuId = router.menuId)) {
          item.isShowMenu = false
        }
      })
      this.toggleSideBar()
    },
    chooseRouters(router, index) {
      // 修改选中的左侧菜单
      if (router.children && router.children[0].path === 'dashboard') {
        return
      }
      this.$store
        .dispatch('ChooseRouters', router)
        .then(() => { })
        .catch(() => {
          console.log('修改左侧菜单失败！')
        })
    },
    getIndex(item) {
      // 获取应该跳转的地址
      if (item.children !== null && item.children !== undefined && item.children.length !== 0) {
        if (item.children[0].componentType === 'layout') {
          if (item.children[0].children[0].componentType === 'layout') {
            return item.children[0].children[0].path + '/' + item.children[0].children[0].children[0].path
          } else {
            return item.children[0].path + '/' + item.children[0].children[0].path
          }
        } else {
          return item.path + '/' + item.children[0].path
        }
      } else {
        return item.path
      }
    },
    hideWarnBox() {
      this.warnBoxVisible = false
    },
    noDealAlarmClick() {
      this.warnBoxVisible = true
    },
    openToDoListTable() {
      this.openBase('to-do-list-table', null)
    }
  }
}
</script>

<style>
.elDropdownMenu[x-placement^='bottom'] .popper__arrow {
  border-bottom-color: #21519d;
}

.elDropdownMenu[x-placement^='bottom'] .popper__arrow::after {
  border-bottom-color: #314479;
}
</style>

<style lang="scss" scoped>
.item {
  width: 25px;
}

/deep/ .el-badge__content {
  height: 1.3890vh;
  line-height: 1.3890vh;
  padding: 0 0.2778vh;
  margin-top: 1.8520vh;
}

/deep/ li.el-dropdown-menu__item.clearfix {
  /* border-top: 0.2778vh solid #ffffff; */
  color: #fff;
}

/deep/ li.el-dropdown-menu__item.clearfix:hover {
  background-color: #3774d6;
}

.el-popper {
  border: 1px solid #21519d;
  background-color: #314479;
}

.el-popper li {
  background-color: #314479;
}

.navbar {
  height: 6.4820vh;
  position: relative;
  // background: url(/static/img/top.png) round !important;
  background-color: rgba(12, 52, 117, 1);
  clear: both;
  max-width: 100%;

  .logo {
    padding-left: 1.8520vh;
    display: flex;
    align-items: center;
  }

  .navA {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    width: 100%;
  }

  .hamburger-container {
    background: linear-gradient(to right, #2fb69c, #3ac5a8);
    line-height: 1.4816vh;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(12, 52, 117, 1);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .left-menu {
    left: 25.5vw;
    height: 6.4820vh;
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 7.4080vh;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 0.7408vh;
      height: 100%;
      font-size: 1.2964vh;
      color: #ffffff;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      display: flex;
      margin-right: 2.7780vh;

      .avatar-wrapper-user {
        cursor: pointer;
        position: relative;
        margin-right: 3.7040vh;
      }

      .avatar-wrapper-name {
        position: relative;
        margin-right: 3.7040vh;
      }

      .avatar-wrapper {
        position: relative;
        cursor: pointer;

        .user-avatar {
          cursor: pointer;
          width: 3.7040vh;
          height: 3.7040vh;
          border-radius: 0.9260vh;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -1.8520vh;
          top: 2.3150vh;
          font-size: 1.1112vh;
        }
      }
    }
  }
}
</style>
