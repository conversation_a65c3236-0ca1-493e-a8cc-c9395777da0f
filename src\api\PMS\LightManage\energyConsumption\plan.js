import request from '@/utils/request'
// 删除
export function fetchDelete(data) {
  return request({
    url: '/energyService/api/lampEnergyPlan/deleteLampEnergyPlan',
    method: 'post',
    data
  })
}

// 列表
export function fetchList(data) {
  return request({
    url: '/energyService/api/lampEnergyPlan/LampEnergyPlanList',
    method: 'post',
    data
  })
}
// 添加
export function fetchCreate(data) {
  return request({
    url: '/energyService/api/lampEnergyPlan/insertLampEnergyPlan',
    method: 'post',
    data
  })
}
// 详情
export function fetchDetail(data) {
  return request({
    url: '/energyService/api/lampEnergyPlan/getLampEnergyPlanInfo',
    method: 'get',
    params: data
  })
}

// 编辑
export function fetchUpdate(data) {
  return request({
    url: '/energyService/api/lampEnergyPlan/updateLampEnergyPlan',
    method: 'post',
    data
  })
}
// 月能耗
export function fetchMonthList(data) {
  return request({
    url: '/energyService/api/lampAnalyse/getLampMonthAnalyseList',
    method: 'post',
    data
  })
}
// 年能耗
export function fetchYearList(data) {
  return request({
    url: '/energyService/api/lampAnalyse/getLampYearAnalyseList',
    method: 'post',
    data
  })
}
