import request from '@/utils/request'
const prefix = '/programService'
// 素材管理-分页查询
export function fetchList(data) {
  return request({
    url: prefix + '/api/v1/material/page',
    method: 'post',
    data
  })
}
// 添加素材
export function fetchCreate(data) {
  return request({
    url: prefix + '/api/v1/material/add',
    method: 'post',
    data
  })
}
// 详情素材
export function fetchDetail(id) {
  const data = { materialId: id }
  return request({
    url: prefix + '/api/v1/material/getInfo',
    method: 'post',
    data
  })
}

// 删除素材
export function fetchDelete(ids) {
  var data = { materialIds: ids }
  return request({
    url: prefix + '/api/v1/material/delete',
    method: 'post',
    data
  })
}
// 编辑素材
export function fetchUpdate(data) {
  return request({
    url: prefix + '/api/v1/material/edit',
    method: 'post',
    data
  })
}
