import request from '@/utils/request'
export function fetchList(data) {
  return request({
    url: '/pmslinkage/api/linkageConfig/list',
    method: 'post',
    data
  })
}

export function fetchDetail(queryData) {
  return request({
    url: '/pmslinkage/api/linkageConfig/detail',
    method: 'get',
    params: queryData
  })
}

export function fetchDelete(linkageIds) {
  var data = { linkageIds }
  return request({
    url: '/pmslinkage/api/linkageConfig/delete',
    method: 'post',
    data
  })
}

export function fetchCreate(data) {
  return request({
    url: '/pmslinkage/api/linkageConfig/create',
    method: 'post',
    data
  })
}

export function fetchUpdate(data) {
  return request({
    url: '/pmslinkage/api/linkageConfig/update',
    method: 'post',
    data
  })
}
