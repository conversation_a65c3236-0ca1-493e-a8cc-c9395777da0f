import request from '@/utils/request'

// 报修列表
export function fetchList(data) {
  return request({
    url: '/szdlService/api/repairManageSzdl/list',
    method: 'post',
    data
  })
}
// 添加
export function fetchCreate(data) {
  return request({
    url: '/szdlService/api/repairManageSzdl/insert',
    method: 'post',
    data
  })
}
// 详情
export function fetchDetail(data) {
  return request({
    url: '/szdlService/api/repairManageSzdl/get',
    method: 'post',
    data
  })
}

// 编辑
export function fetchUpdate(data) {
  return request({
    url: '/szdlService/api/repairManageSzdl/update',
    method: 'post',
    data
  })
}

// 设备列表
export function fetchEquipment(data) {
  return request({
    url: '/szdlService/api/deviceManage/selectDeviceDown',
    method: 'post',
    data
  })
}
