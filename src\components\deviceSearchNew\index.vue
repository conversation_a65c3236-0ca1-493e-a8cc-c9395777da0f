<template>
  <div class="deviceSearchDiv">
    <div class="repair-container">
      <slot name="before" />
      <el-input v-model="listQuery.shareDeviceName" clearable style="width:21.2980vh;margin-bottom:1vh;"
        :placeholder="nameSearchPlaceholder" @change="changeSelectSearch(false, false, false, false)" />
      <!--       <el-select
        v-if="isTag"
        placeholder="标签"
        style="width:14.8160vh;"
        v-model="listQuery.tagId"
        clearable
        @change="changeSelectSearch(false, false, false, false)"
      >
        <el-option v-for="item in tagOptions" :key="item.tagId" :value="item.tagId" :label="item.tagName"></el-option>
      </el-select> -->
      <el-cascader v-if="isArea" ref="selectAreaRef" v-model="listQuery.shareAreaCode" style="width:14.8160vh;" filterable
        clearable :options="areas" :props="areaPropstree" placeholder="区域" change-on-select
        @change="changeSelectSearch(false, false, false, true)" />

      <!-- <el-select
        v-show="isArea"
        v-model="listQuery.shareAreaCode"
        style="width:14.8160vh;"
        clearable
        placeholder="区域"
        class="form-item-width"
        @change="changeSelectSearch(false, false, false, true)"
      >
        <el-option v-for="item in areas" :key="item.dicValue" :label="item.dicName" :value="item.dicValue" />
      </el-select> -->
      <!-- <el-select
        v-if="isArea"
        v-model="listQuery.streetCode"
        clearable
        filterable
        remote
        :remote-method="getStreets"
        :loading="selectLoading"
        placeholder="街道"
        class="form-item-width"
        style="width:14.8160vh;"
        @change="changeSelectSearch(false, true, false, false)"
        @blur="onBlur('streetCode', 'getStreets')"
        @clear="getStreets"
      >
        <el-option v-for="item in streets" :key="item.key" :label="item.value" :value="item.key" />
      </el-select> -->
      <el-select v-if="isArea" v-model="listQuery.intersectionCode" clearable filterable remote
        :remote-method="getIntersetions" :loading="selectLoading" placeholder="所属路口" class="form-item-width"
        style="width:14.8160vh;" @change="changeSelectSearch(false, false, false, false)"
        @blur="onBlur('intersectionCode', 'getIntersetions')" @clear="getIntersetions">
        <el-option v-for="item in intersetions" :key="item.key" :label="item.value" :value="item.key" />
      </el-select>
      <el-cascader v-if="shareSysTypeShow && isDeviceType" ref="selectDeptRef" v-model="listQuery.shareDeviceTypes"
        :show-all-levels="false" style="width:16.6680vh;" clearable :options="deviceTypes" :props="propstree"
        placeholder="设备类型" :collapse-tags="true" @change="changeSelectSearch(true, false, false, false)" />
      <el-select v-if="isManufacturerCode" v-model="listQuery.shareManufacturerCode" style="width:14.8160vh;" clearable
        placeholder="设备厂商" class="form-item-width" @change="changeSelectSearch(false, true, false, false)">
        <el-option v-for="item in shareManufacturerCodeOption" :key="item.key" :label="item.value" :value="item.key" />
      </el-select>
      <el-select v-if="isManufacturerCode" v-model="listQuery.shareEquipType" style="width:14.8160vh;" clearable
        placeholder="型号" class="form-item-width" @change="handCreate()">
        <el-option v-for="item in shareEquipTypeOption" :key="item.key" :label="item.value" :value="item.key" />
      </el-select>
      <slot name="after" />
    </div>
  </div>
</template>

<script>
import { queryManufacturer, queryModel, getIntersetions, getDeviceType } from '@/api/system/dic/dic'
import { geAreaList } from '@/api/otherResource/cmdb/area'
import { deviceTagList } from '@/api/otherSystem/equipmentManagement/tag'

export default {
  props: {
    isMutiSearch: { // 是否显示多项选择器
      type: Boolean,
      default: true
    },
    isTag: { // 是否显示标签选择器
      type: Boolean,
      default: false
    },
    isArea: { // 是否显示区域查询
      type: Boolean,
      default: true
    },
    isDeviceType: { // 是否显示设备类型查询
      type: Boolean,
      default: true
    },
    isManufacturerCode: { // 是否显示设备厂商和型号联动查询
      type: Boolean,
      default: true
    },
    shareDeviceName: {
      type: String,
      default: ''
    },
    shareSysType: { // 专题类型
      type: String,
      default: ''
    },
    nameSearchPlaceholder: {
      type: String,
      default: '请输入搜索内容'
    }
  },
  data() {
    return {
      searchDisabled: false,
      xzsbdialogFormVisible: false,
      listQuery: {
        shareDeviceName: null,
        tagId: undefined,
        shareAreaCode: '', // 区域
        streetCode: null,
        intersectionCode: null,
        shareDeviceTypes: null, // 设备类型
        shareManufacturerCode: null, // 设备厂商
        shareEquipType: null // 型号
      },
      tagOptions: [],
      shareSysTypeOption: [],
      areas: [],
      streets: [],
      intersetions: [],
      deviceTypes: [],
      selectLoading: false,
      propstree: {
        label: 'value',
        value: 'key',
        children: 'deviceTypeList',
        checkStrictly: true,
        multiple: true
      },
      areaPropstree: {
        value: 'markerDataId',
        label: 'markerName',
        children: 'childLayerMessage',
        checkStrictly: true,
        expandTrigger: 'hover',
        emitPath: false
      },
      deviceTypeValue: [],
      shareManufacturerCodeOption: [],
      shareEquipTypeOption: []
    }
  },
  computed: {
    shareSysTypeShow() {
      const isLight = this.$route.path === '/PMS/LightManage/management'
      const isDeviceMage = this.$route.path.indexOf('/equipmentQuery')
      return !(isLight || isDeviceMage && this.$route.query.sysType)
    },
    selectDeviceTypes() {
      let deviceTypes = []
      if (!this.listQuery.shareDeviceTypes) return null
      for (let index = 0; index < this.listQuery.shareDeviceTypes.length; index++) {
        const element = this.listQuery.shareDeviceTypes[index]
        if (!element[element.length - 1].includes('noChild')) deviceTypes = deviceTypes.concat(element[element.length - 1].split(','))
      }
      const result = [...new Set(deviceTypes)]
      return result.length > 0 ? result : null
    }
  },
  created() {
    if (this.shareDeviceName !== '') {
      this.listQuery.shareDeviceName = this.shareDeviceName
      this.deviceNameSearch()
    }
    // if (this.shareSysType > 0) {
    //   this.listQuery.shareSysType = this.shareSysType
    // } else {
    //   this.listQuery.shareSysType = ''
    // }
    this.init()
  },
  methods: {
    deviceNameSearch() {
      this.$emit('handleData', this.listQuery)
    },
    clearSearch() {
      this.listQuery = {
        shareDeviceName: null,
        shareAreaCode: null, // 区域
        intersectionCode: null,
        shareDeviceTypes: null, // 设备类型
        shareManufacturerCode: null, // 设备厂商
        shareEquipType: null // 型号
      }
      this.handCreate()
      this.$message({
        message: '已清除选择！',
        type: 'success'
      })
      this.init()
      this.$emit('handleData', this.listQuery)
    },
    clear(query) {
      for (const key in query) {
        switch (key) {
          case 'page':
            query[key] = 1
            break
          case 'limit':
            break
          case 'isSpot':
          case 'order':
          case 'sortBy':
            if (this.$route.path !== '/dashboard') {
              query[key] = null
            }
            break
          default:
            query[key] = null
            break
        }
      }
      this.clearSearch()
    },
    onBlur(val, method) {
      setTimeout(() => {
        console.log(!this.listQuery[val])
        if (!this.listQuery[val]) {
          this[method]()
        }
      }, 500)
    },
    async changeSelectSearch(isDeviceType, isManufacturerCode, isShareEquipType, isAreaCodeClear) {
      if (isDeviceType) { // 设备类型联动
        this.listQuery.shareEquipType = ''
        await this.loadShareManufacturerCodeOptions()
        if (!this.shareManufacturerCodeOption.find(ele => ele.key === this.listQuery.shareManufacturerCode)) {
          this.listQuery.shareManufacturerCode = ''
        }
        this.loadEquipTypeOptions()
      }
      if (isManufacturerCode) { // 设备厂商联动
        this.loadEquipTypeOptions()
        this.listQuery.shareEquipType = ''
      }
      if (isShareEquipType) { // 型号联动
        this.listQuery.shareEquipType = ''
      }
      if (isAreaCodeClear) {
        this.$refs.selectAreaRef.dropDownVisible = false
      }
      this.handCreate()
    },
    init() {
      // if (this.shareSysType > 0) {
      //   this.listQuery.shareSysType = this.shareSysType
      //   this.changeSelectSearch(true, false, false, false)
      // }
      this.getTagOptions()
      this.getDeviceTypes()
      this.loadShareAreaCodeOptions()
      this.getIntersetions()
      // this.loadShareSysTypeOptions()
      this.loadShareManufacturerCodeOptions()
      this.loadEquipTypeOptions()
    },
    getTagOptions() {
      if (!this.isTag) return
      deviceTagList({ type: 0 }).then(res => {
        if (res.data.code === 200) {
          this.tagOptions = res.data.data
        }
      })
    },
    // 获取区域
    loadShareAreaCodeOptions() {
      if (this.isArea) {
        geAreaList({
          page: 1,
          limit: 999
        }).then((r) => {
          this.areas = r.data.data
          this.$nextTick(() => {
            // 添加这段代码
            const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]')
            Array.from($el).map((item) => item.removeAttribute('aria-owns'))
          })
        })
      }
    },
    // 获取路口列表
    getIntersetions(intersection = '') {
      if (this.isArea) {
        this.selectLoading = true
        getIntersetions({
          intersection
        }).then((r) => {
          this.selectLoading = false
          this.intersetions = r.data.data
        }).catch(() => {
          this.selectLoading = false
        })
      }
    },
    // 获取专题类型
    getDeviceTypes() {
      getDeviceType().then(res => {
        this.deviceTypes = this.getTreeData(res.data.data.filter(ele => !ele.hidden))
      })
    },
    getTreeData(data, isChild) {
      return data.map(ele => {
        if (ele.deviceTypeList.length > 0) {
          ele.deviceTypeList = this.getTreeData(ele.deviceTypeList, true)
          ele.key = ele.deviceTypeList.map(ele => ele.key).join(',')
        } else {
          ele.deviceTypeList = null
          if (!isChild) ele.key = 'noChild,' + ele.key
        }
        return ele
      })
    },
    // 获取设备厂商
    loadShareManufacturerCodeOptions() {
      return new Promise(resolve => {
        if (this.isManufacturerCode) {
          queryManufacturer({
            deviceTypes: this.selectDeviceTypes // 设备类型
          }).then((r) => {
            this.shareManufacturerCodeOption = r.data.data
            resolve()
          }).catch(() => {
            resolve()
          })
        } else {
          resolve()
        }
      })
    },
    // 获取型号
    loadEquipTypeOptions() {
      return new Promise(resolve => {
        if (this.isManufacturerCode) {
          queryModel({
            deviceTypes: this.selectDeviceTypes, // 设备类型
            manufacturerCode: this.listQuery.shareManufacturerCode
          }).then((r) => {
            this.shareEquipTypeOption = r.data.data
            this.listQuery.shareEquipType = ''
            resolve()
          }).catch(() => {
            resolve()
          })
        } else {
          resolve()
        }
      })
    },
    handCreate() {
      const params = { ...this.listQuery }
      params.shareDeviceTypes = this.selectDeviceTypes
      this.$emit('handleData', params)
    }
  }
}
</script>

<style>
.deviceSearchDiv .el-input-group__append {
  background-color: #409EFF;
  color: #ffffff;
  cursor: pointer;
  padding: 0 0.4630vh;
}

.deviceSearchd {
  margin-left: 0.4630vh;
  padding: 0.1852vh;
  border-radius: 0.4630vh;
}

.deviceSearchFg {
  width: 0.1vw;
  background-color: #e8d9d94d;
  margin-left: 0.2vw;
}
</style>
