import request from '@/utils/request'

const url = '/cmdbService'

export function queryAttriFromRoute(params) {
  return request({
    url: url + '/newConfCiTypeAttri/queryAttriFromRoute',
    method: 'get',
    params: params
  })
}

export function getAllAttributeList(params) {
  return request({
    url: url + '/confCiTypeAttri/getAllAttributeList',
    method: 'get',
    params: params
  })
}

export function getCmdbAttributeDict() {
  return request({
    url: url + '/confCiTypeAttri/getCmdbAttributeDict',
    method: 'get'
  })
}

export function saveAttribute(params) {
  return request({
    url: url + '/newConfCiTypeAttri',
    method: 'post',
    data: params
  })
}
export function saveEditConfCiTypeAttri(params) {
  return request({
    url: url + '/newConfCiTypeAttri',
    method: 'put',
    data: params
  })
}

export function judgeAttriUnique(params) {
  return request({
    url: url + '/confCiTypeAttri/judgeAttriUnique',
    method: 'get',
    params: params
  })
}

export function getCommonlyAttributeDict() {
  return request({
    url: url + '/confCiTypeAttri/getCommonlyAttributeDict',
    method: 'get'
  })
}

export function queryAttriByParams(params) {
  return request({
    url: url + '/newConfCiTypeAttri/newQueryAttriByParams',
    method: 'post',
    data: params
  })
}

export function queryCustomAttriByParams(params) {
  return request({
    url: url + '/newConfCiTypeAttri/newQueryCustomAttriByParams',
    method: 'post',
    data: params
  })
}

export function getConfCiTypeAttriByTypeCode(params) {
  return request({
    url: url + '/confCiTypeAttri/getConfCiTypeAttriByTypeCode',
    method: 'post',
    data: params
  })
}

export function removeById(params) {
  return request({
    url: url + '/newConfCiTypeAttri/' + params,
    method: 'delete'
  })
}

export function changeAttriOfRelation(params) {
  return request({
    url: url + '/confCiTypeAttri/changeAttriOfRelation',
    method: 'post',
    data: params
  })
}

export function autoSystemTopo(params) {
  return request({
    url: url + '/topo/autoSystemTopo',
    method: 'post',
    data: params
  })
}

export function resAlarm(params) {
  return request({
    url: url + '/topo/resAlarm',
    method: 'post',
    data: params
  })
}
