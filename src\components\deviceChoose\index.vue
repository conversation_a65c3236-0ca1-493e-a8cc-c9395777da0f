<template>
  <div class="deviceChooseDiv">
    <div style="display: flex" class="filter-container">
      <!-- 引入公共查询设备组件 -->
      <deviceSearch ref="deviceSearch" :is-clear="isClear" :is-muti-search="isMutiSearch" :is-area="isArea"
        :is-sys-type="isSysType" :is-device-type="isDeviceType" :is-device-type-source="isDeviceTypeSource"
        :is-manufacturer-code="isManufacturerCode" @handleData="handleData">
        <template v-slot:after>
          <!--           <el-select
            v-model="listQuery.alarmState"
            clearable
            placeholder="请选择告警信息"
            style="margin-right: 0.9260vh"
            class="form-item-width"
          >
            <el-option label="无" :value="0" />
            <el-option label="有" :value="1" />
          </el-select> -->
          <el-select v-model="listQuery.onlineState" clearable placeholder="请选择在线状态" style="margin-right: 0.9260vh"
            class="form-item-width">
            <el-option label="在线" :value="1" />
            <el-option label="离线" :value="0" />
          </el-select>
          <el-button style="height: 3.2410vh" type="primary" icon="el-icon-search" @click="handleFilter">{{
            $t("button.search") }}</el-button>
          <el-button v-waves type="primary" style="height: 3.2410vh" @click="$refs.deviceSearch.clear(listQuery)">{{
            $t("button.clear") }}</el-button>
        </template>
      </deviceSearch>
    </div>

    <el-table ref="multipleTable" v-loading="listLoading" :data="list" border stripe fit highlight-current-row
      style="width: 100%" @row-click="handleRowClick" @selection-change="handleSelectionChange">
      <el-table-column v-if="isMutiChoose" type="selection" width="5.0930vh" />
      <el-table-column type="index" align="center" :label="$t('table.id')" width="5.0930vh" />
      <el-table-column label="设备编号" show-overflow-tooltip>
        <template slot-scope="scope">{{ scope.row.deviceSerno }}</template>
      </el-table-column>

      <el-table-column label="设备名称" show-overflow-tooltip>
        <template slot-scope="scope">{{ scope.row.deviceName }}</template>
      </el-table-column>

      <el-table-column label="所属资源" show-overflow-tooltip>
        <template slot-scope="scope">{{ scope.row.areaName }}</template>
      </el-table-column>

      <el-table-column label="型号" show-overflow-tooltip>
        <template slot-scope="scope">{{ scope.row.deviceTypeName }}</template>
      </el-table-column>

      <el-table-column label="厂商" show-overflow-tooltip>
        <template slot-scope="scope">{{ scope.row.manufacturerName }}</template>
      </el-table-column>

      <!--       <el-table-column label="告警信息" min-width="9.2600vh">
        <template slot-scope="scope">
          <el-tag :type="scope.row.alarmState === 1 ? 'danger' : 'success'">
            {{ scope.row.alarmState === 1 ? '有' : '无' }}
          </el-tag>
        </template>
      </el-table-column> -->

      <el-table-column label="在线状态" min-width="9.2600vh">
        <template slot-scope="scope">
          <el-tag :type="scope.row.onlineState === 0 ? 'info' : 'success'">
            {{ scope.row.onlineState === 0 ? '离线' : '在线' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态更新时间" width="12.5010vh">
        <template slot-scope="scope">{{ scope.row.updateTime }}</template>
      </el-table-column>
    </el-table>
    <div class="pagination-container">
      <el-pagination :current-page="listQuery.page" :page-sizes="[10, 20, 30, 50, 100, 200]"
        :page-size="listQuery.limit" layout="total, sizes, prev, pager, next, jumper" :total="total"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>
    <div slot="footer" class="dialog-footer" style="margin-top:0.9260vh;">
      <el-button v-if="isMutiChoose" type="primary" @click="handCreate">确定</el-button>
      <el-button type="primary" @click="handClear">清除</el-button>
      <el-button @click="handCancle">{{ $t('button.cancel') }}</el-button>
    </div>
  </div>
</template>

<script>
import deviceSearch from '@/components/deviceSearch/index'
import {
  fetchList
} from '@/api/otherSystem/equipmentManagement/equipmentQuery'

export default {
  components: {
    deviceSearch
  },
  props: {
    isMutiChoose: { // 是否多选
      type: Boolean,
      default: true
    },
    showDialog: {
      type: Boolean,
      default: false
    },
    selectedList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isClear: true, // 是否清空选项(用于组件放在弹窗的时候，重新打开弹窗的时候清空选项)
      isMutiSearch: true, // 是否显示多项选择器(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
      isArea: true, // 是否显示区域查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
      isSysType: true, // 是否显示专题类型查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
      isDeviceType: true, // 是否显示设备类型查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
      isDeviceTypeSource: true, // 是否只显示有源设备(true:只显示有源设备 false:显示全部设备类型)
      isManufacturerCode: true, // 是否显示设备厂商和型号联动查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
      listQuery: {
        page: 1,
        limit: 10,
        alarmState: null,
        onlineState: null,
        isDevice: null // 是否设备(0:无源设施,1:有源设备)
      },
      total: 0,
      list: [],
      listLoading: false,
      multipleSelection: [],
      changeSelectedList: []
    }
  },
  watch: {
    showDialog(n, o) {
      if (n) {
        this.isClear = true
        this.listQuery = {
          page: 1,
          limit: 10,
          alarmState: null,
          onlineState: null,
          isDevice: null // 是否设备(0:无源设施,1:有源设备)
        }
        this.changeSelectedList = this.selectedList
        this.getList()
      } else {
        this.isClear = false
      }
    }
  },
  created() {
    this.isClear = true
    this.changeSelectedList = this.selectedList
    this.getList()
  },
  methods: {
    init() {
      this.multipleSelection = []
      var that = this
      if (that.isMutiChoose) { // 多选设备
        if (that.changeSelectedList !== null && that.changeSelectedList.length > 0) {
          that.changeSelectedList.forEach(item => {
            var row = null
            var r = false
            that.list.forEach(element => {
              if (item.deviceId === element.deviceId) {
                row = element
                r = true
              }
              if (r) return
            })
            that.$nextTick(() => {
              if (row !== null) {
                that.$refs.multipleTable.toggleRowSelection(row, true)
              }
            })
          })
        }
      } else { // 单选设备
        this.changeSelectedList.forEach(item => {
          var row = null
          var r = false
          this.list.forEach(element => {
            if (item.deviceId === element.deviceId) {
              row = element
              r = true
            }
            if (r) return
          })
          this.$nextTick(() => {
            if (row !== null) { this.$refs.multipleTable.setCurrentRow(row) }
          })
        })
      }
    },
    handleData(data) {
      this.listQuery = Object.assign(this.listQuery, data) // 接收并合并公共组件查询条件
    },
    getList() {
      this.listLoading = true
      fetchList(this.listQuery).then((res) => {
        this.list = res.data.data
        this.total = res.data.total
        this.init()
        this.listLoading = false
      })
    },
    handleSizeChange(val) {
      this.listQuery.limit = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.getList()
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleSelectionChange(val) {
      // 取消
      if (this.multipleSelection.length > val.length && val.length > 0) {
        var objs = []
        this.multipleSelection.forEach(element => {
          var r = false
          val.forEach(item => {
            if (item.deviceId === element.deviceId) {
              r = true
            }
          })
          if (!r) {
            objs.push(element)
          }
        })
        if (objs.length > 0) {
          objs.forEach(u => {
            this.changeSelectedList.forEach((s, index) => {
              if (s.deviceId === u.deviceId) {
                this.changeSelectedList.splice(index, 1)
              }
            })
          })
        }
        this.multipleSelection = val
      } else if (val.length === 0) {
        this.multipleSelection.forEach(u => {
          this.changeSelectedList.forEach((s, index) => {
            if (s.deviceId === u.deviceId) {
              this.changeSelectedList.splice(index, 1)
            }
          })
        })
      } else {
        // 勾选
        var objs = []
        val.forEach(element => {
          if (this.changeSelectedList !== undefined && this.changeSelectedList !== null && this.changeSelectedList.length > 0) {
            var r = false
            this.changeSelectedList.forEach(item => {
              if (item.deviceId == element.deviceId) {
                r = true
              }
            })
            if (!r) {
              objs.push(element)
            }
          } else {
            objs.push(element)
          }
        })
        if (objs.length > 0) {
          objs.forEach(u => {
            this.changeSelectedList.push(u)
          })
        }
        this.multipleSelection = val
      }
    },
    handleRowClick(row, column, event) {
      if (!this.isMutiChoose) {
        this.$emit('handCancle', [row], true)
      }
    },
    handCreate() {
      this.$emit('handCancle', this.changeSelectedList, true)
    },
    handCancle() {
      this.$emit('handCancle', this.changeSelectedList, false)
    },
    handClear() {
      if (this.isMutiChoose) {
        this.changeSelectedList = []
        this.$refs.multipleTable.clearSelection()
      } else {
        this.changeSelectedList = []
        this.$refs.multipleTable.setCurrentRow()
      }
      this.$message({
        message: '已清除所选设备！',
        type: 'success'
      })
    }
  }
}
</script>
<style>
.deviceChooseDiv .el-table tr {
  cursor: pointer;
}
</style>
