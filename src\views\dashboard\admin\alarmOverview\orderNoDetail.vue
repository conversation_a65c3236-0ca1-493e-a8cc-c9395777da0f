<template>
  <div>
    <div class="filter-container" style="display: flex">
      <!-- 引入公共查询设备组件 -->
      <deviceSearch ref="deviceSearch" v-bind="deviceSearchConfig" @handleData="handleData">
        <template v-slot:after>
          <el-select v-model="listQuery.orderStatus" style="margin-right: 0.9259vh" clearable placeholder="请选择工单状态"
            class="form-item-width">
            <el-option label="未处理" :value="0" />
            <el-option label="处理中" :value="1" />
            <el-option label="完成" :value="2" />
            <el-option label="挂起" :value="3" />
          </el-select>
          <el-button v-waves type="primary" icon="el-icon-search" style="height: 3.24vh" @click="handleFilter">{{
            $t("button.search") }}</el-button>
          <el-button v-waves type="primary" style="height: 3.24vh" @click="$refs.deviceSearch.clear(listQuery)">{{
            $t("button.clear") }}</el-button>
        </template>
      </deviceSearch>

    </div>
    <zyk-table :list="dataList" :list-loading="listLoading" v-bind="tableConfig" :total="total" :page="listQuery.page"
      :limit="listQuery.limit" @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
  </div>
</template>
<script>
import ZykTable from '@/components/table/index.vue'
import deviceSearch from '@/components/deviceSearch/index'
import { fetchList } from '@/api/otherSystem/alarmManagement/alarmWorkOrder'
export default {
  name: 'OrderNoDetail',
  components: {
    ZykTable,
    deviceSearch
  },
  props: {
    orderNo: {
      type: String,
      default: ''
    },
    showDialog: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dataList: [],
      total: null,
      listLoading: false,
      deviceSearchConfig: {
        isMutiSearch: true, // 是否显示多项选择器(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
        isArea: true, // 是否显示区域查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
        isSysType: true, // 是否显示专题类型查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
        isDeviceType: true, // 是否显示设备类型查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
        isManufacturerCode: true, // 是否显示设备厂商和型号联动查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
        isClear: false
      },
      tableConfig: {
        propList: [
          { prop: 'orderBusinessKey', label: '工单号', minWidth: '200', useTable: false, showOverflowTooltip: true },
          { prop: 'deviceName', label: '告警设备', minWidth: '170', useTable: false },
          { prop: 'belongerResource', label: '所属资源', minWidth: '150', useTable: false, showOverflowTooltip: true },
          { prop: 'msgText', label: '告警内容', minWidth: '100', useTable: false, showOverflowTooltip: true }, // showOverflowTooltip：当内容过长时，用省略号替代
          { prop: 'priorityName', label: '告警级别', minWidth: '100', useTable: false },
          { prop: 'orderStatusName', label: '工单状态', minWidth: '100', useTable: false, slotName: 'status' }, // slotName: 插槽名字
          { prop: 'createDate', label: '工单转派时间', minWidth: '200', useTable: false },
          { prop: 'dealTime', label: '工单处理时间', minWidth: '200', useTable: false }
        ],
        showIndexColumn: true, // 是否展示序号
        showSelectColumn: false, // 是否启动多选
        showPagination: false // 是否展示分页
      },
      listQuery: {
        page: 1,
        limit: 10,
        orderStatus: null
      }
    }
  },
  watch: {
    showDialog(newValue, OldValue) {
      if (newValue) {
        this.init()
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.listQuery = {
        page: 1,
        limit: 10,
        orderStatus: null
      }
      this.deviceSearchConfig.isClear = !this.deviceSearchConfig.isClear
      this.listQuery.orderId = this.orderNo
      if (this.$refs.deviceSearch) this.$refs.deviceSearch.clear(this.listQuery, true)
      this.getList()
    },
    getList() {
      this.listLoading = true
      fetchList(this.listQuery).then(res => {
        if (res.data.code === 200) {
          this.total = res.data.total
          this.dataList = res.data.data.alarmRecordOrderList
          this.tableConfig.showPagination = !(this.total < 10)
        }
        this.listLoading = false
      }).catch(err => {
        console.log('err', err)
        this.listLoading = false
      })
      // fetchList(this.listQuery).then((response) => {
      //   console.log('suc', response);
      //   this.listLoading = false;
      // });
    },
    handleData(data) {
      this.listQuery = Object.assign(this.listQuery, data) // 接收并合并公共组件查询条件
    },
    handleFilter() {
      this.listQuery.page = 1
      this.listQuery.limit = 10
      this.getList()
    },
    handleSizeChange(val) {
      this.listQuery.limit = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.getList()
    }
  }
}
</script>
<style scoped></style>
