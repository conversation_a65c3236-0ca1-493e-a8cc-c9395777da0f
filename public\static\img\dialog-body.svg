<svg width="1115" height="623" viewBox="0 0 1115 623" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_b_126_115)">
<rect x="1113.51" y="0.553223" width="1.48667" height="622.447" fill="url(#paint0_linear_126_115)"/>
<rect y="0.553223" width="1.48667" height="622.447" fill="url(#paint1_linear_126_115)"/>
<rect x="1.48669" y="620.423" width="1112.03" height="2.57742" fill="url(#paint2_linear_126_115)"/>
<rect y="613.979" width="43.1133" height="9.02097" fill="#01FFFF"/>
<rect x="1071.89" y="613.979" width="43.1133" height="9.02097" fill="#01FFFF"/>
</g>
<defs>
<filter id="filter0_b_126_115" x="-16" y="-15.4468" width="1147" height="654.447" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImage" stdDeviation="8"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_126_115"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_126_115" result="shape"/>
</filter>
<linearGradient id="paint0_linear_126_115" x1="1114.26" y1="0.553223" x2="1114.26" y2="623" gradientUnits="userSpaceOnUse">
<stop stop-color="#76AFF9" stop-opacity="0"/>
<stop offset="0.4875" stop-color="#76AFF9"/>
<stop offset="1" stop-color="#76AFF9" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_126_115" x1="0.743333" y1="0.553223" x2="0.743333" y2="623" gradientUnits="userSpaceOnUse">
<stop stop-color="#76AFF9" stop-opacity="0"/>
<stop offset="0.4875" stop-color="#76AFF9"/>
<stop offset="1" stop-color="#76AFF9" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_126_115" x1="1.48669" y1="623.002" x2="1113.51" y2="623.002" gradientUnits="userSpaceOnUse">
<stop stop-color="#76AFF9" stop-opacity="0"/>
<stop offset="0.492708" stop-color="#76AFF9"/>
<stop offset="1" stop-color="#76AFF9" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
