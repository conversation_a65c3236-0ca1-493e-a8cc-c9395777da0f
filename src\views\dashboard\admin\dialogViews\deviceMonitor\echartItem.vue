<template>
  <div>
    <div ref="myEchart" style="min-width:44.4445vh;min-height:55vh;" />
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { debounce } from '@/utils'
require('echarts/theme/macarons') // echarts theme

export default {
  props: ['keyModel'],
  data() {
    return {
      chart: null,
      legendData: [], // 图例
      xData: [],
      data: [],
      unit: '无'
    }
  },
  watch: {
    keyModel: {
      deep: true,
      handler(val) {
        this.changeData(val)
      }
    }
  },
  mounted() {
    this.changeData(this.keyModel)
    this.loadData()
    this.__resizeHanlder = debounce(() => {
      if (this.chart) {
        this.chart.resize()
      }
    }, 100)
    window.addEventListener('resize', this.__resizeHanlder)
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }

    this.chart.dispose()
    this.chart = null
  },
  methods: {
    changeData(val) {
      if (val.statisticsReport !== null && val.statisticsReport.length > 0) {
        this.legendData = []
        this.xData = []
        this.data = []
        this.unit = val.unit ? val.unit : '无'
        val.statisticsReport.forEach((element, index) => {
          var itemData = []
          element.reportData.forEach((item, k) => {
            itemData.push(item.yValue)
            if (index == 0) {
              this.xData.push(item.xValue)
            }
          })

          this.data.push({
            type: 'line',
            name: element.deviceName,
            data: itemData
          })

          this.legendData.push(element.deviceName)
        })
      } else {
        this.legendData = []
        this.xData = ['暂无数据']
        this.data = [0]
        this.unit = val.unit ? val.unit : '无'
        this.chart.dispose()
        this.chart = null
      }

      if (this.chart != null) {
        this.chart.dispose()
        this.chart = null
      }
      this.loadData()
    },
    getAutoSize(val) {
      // 当前视口宽度
      var nowClientHeight = document.documentElement.clientHeight
      var initHeight = 1080
      return val * (nowClientHeight / initHeight)
    },
    setOptions(xData, data, legendData, unit) {
      this.chart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              color: '#15ecf4'
            }
          },
          textStyle: {
            fontSize: this.getAutoSize(12),
            color: '#fff'
          },
          // backgroundColor: 'rgba(0,0,0,.8)',
          // extraCssText: 'box-shadow: 4px 4px 10px rgba(21, 250, 255,.6);',
          formatter: function (params) {
            var result = params[0].name + '<br>'
            params.forEach(function (item) {
              result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:' + item.color + '"></span>'
              result += item.seriesName + '：' + item.value + (unit == '无' ? '' : unit) + '</span><br>'
            })
            return result
          }
        },
        legend: {
          selectedMode: false,
          data: legendData,
          textStyle: {
            fontSize: this.getAutoSize(12),
            color: '#A6A6A6'
          },
          icon: 'stack',
          itemHeight: this.getAutoSize(8),
          itemGap: this.getAutoSize(10),
          itemWidth: this.getAutoSize(20),
          type: 'plain',
          // left: '2%',
          top: '5%'
          // right: '1%'
        },
        xAxis: {
          splitLine: {
            show: false
          },
          axisLabel: {
            color: '#A6A6A6',
            interval: 0,
            rotate: 40,
            textStyle: {
              fontSize: this.getAutoSize(12)
            }
          },
          data: xData
        },
        grid: {
          top: '10%',
          left: '4%',
          right: '4%',
          bottom: '0%',
          containLabel: true
        },
        yAxis: {
          name: '单位：' + unit,
          nameTextStyle: {
            color: '#A6A6A6',
            fontSize: this.getAutoSize(12)
            // lineHeight: 40
          },
          splitLine: {
            show: false
          },
          // minInterval: 1,
          axisLabel: {
            color: '#A6A6A6',
            textStyle: {
              fontSize: this.getAutoSize(12)
            }
          }
        },
        series: data
      })
    },
    initChart() {
      this.chart = echarts.init(this.$refs.myEchart, 'macarons')
      this.setOptions(this.xData, this.data, this.legendData, this.unit)
    },
    loadData() {
      this.initChart()
    }
  }
}
</script>
