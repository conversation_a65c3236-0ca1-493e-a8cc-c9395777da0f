import Layout from '@/layout'

export default [
  {
    path: '/resource',
    component: Layout,
    hidden: true,
    children: [{
      path: '/resource/attribute/index',
      name: '资源配置',
      component: () =>
        import('@/views/otherResource/cmdb/resource/attribute/index'),
      meta: { isAuth: false, keepAlive: true }
    },
    {
      path: '/resource/detail/index',
      name: '资源详情展示',
      component: () =>
        import('@/views/otherResource/cmdb/resource/detail/index'),
      meta: {
        isAuth: false,
        keepAlive: true
      }
    }]
  },
  {
    path: '/soc/flow/search/todo-list/index',
    name: '待办工单',
    component: () => import('@/views/otherResource/soc/flow/search/todo-list/index'),
    meta: { keepAlive: true }
  },
  {
    path: '/resource/export/records',
    name: '导出记录',
    component: () => import('@/views/otherResource/cmdb/resource/export/records'),
    meta: { isTab: true, isAuth: false }
  },
  {
    path: '/search/topo/:id/:instanceId',
    name: '自定义拓扑',
    component: () => import('@/views/otherResource/cmdb/resource/search/topo'),
    meta: { isAuth: false }
  },
  {
    path: '/topo/preview/:type/:id',
    name: '拓扑预览',
    component: () => import('@/views/otherResource/cmdb/resource/topo/preview'),
    meta: { isAuth: false }
  }
]
