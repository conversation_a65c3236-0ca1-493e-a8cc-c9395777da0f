export default {
  route: {
    dashboard: '首页',
    dept: '部门管理',
    system: '系统管理',
    pos: '岗位管理',
    role: '角色管理',
    menu: '菜单管理',
    user: '用户管理',
    deviceMgt: '设备管理',
    device: '设备维护',
    alarm: '告警管理',
    collection: '采集点管理',
    linkageMgt: '联动配置管理',
    linkage: '联动配置',
    linkageLog: '联动日志',
    accessControl: '门禁系统',
    InformationDelivery: '信息发布系统',
    oneCard: '一卡通系统',
    parkingLot: '停车场系统',
    ba: '楼宇自控',
    hvacSystem: '空调系统',
    lightSystem: '照明系统',
    waterSystem: '给排水系统',
    energy: '能耗系统',
    energySystem: '能耗系统',
    log: '日志管理',
    informationManager: '基础信息管理'
  },
  navbar: {
    logOut: '退出登录',
    dashboard: '首页',
    screenfull: '全屏',
    theme: '换肤'
  },
  login: {
    title: '系统登录',
    logIn: '登录',
    username: '账号',
    password: '密码'
  },
  table: {
    dynamicTips1: '固定表头, 按照表头顺序排序',
    dynamicTips2: '不固定表头, 按照点击顺序排序',
    dragTips1: '默认顺序',
    dragTips2: '拖拽后顺序',
    id: '序号',
    actions: '操作',
    // 部门 start
    deptName: '部门名称',
    deptId: '部门ID',
    upDeptName: '上级部门名称',
    deptOrder: '部门排序',
    deptDesc: '部门描述',
    // 部门 end
    // 角色 strart
    roleName: '角色名称',
    roleUsers: '角色成员',
    roleRemark: '角色描述',
    opDate: '创建时间',
    roleType: '角色类型',
    // 角色 end
    // 职位 strart
    posDesc: '岗位描述',
    // 职位 end
    // 日志 strart
    menuName: '系统功能名称',
    operateTypeName: '操作类型名称',
    userName: '用户名',
    userAcc: '用户账号',
    ipAddress: 'ip地址',
    executeResultJson: '执行结果描述',
    executeResult: '执行结果',
    // 日志 end
    // 采集点管理
    deviceName: '设备名称',
    providerCode: '厂商',
    deviceNo: '设备编号',
    deviceTypeName: '设备类型',
    subDeviceTypeName: '子设备类型',
    parentDeviceName: '父设备',
    deviceSerno: '设备编号',
    collectionTypeName: '采集点类型',
    collectionUnitName: '采集点单位',
    collectionName: '采集点名称',
    collectionSerno: '采集点编码',
    collectionOrder: '采集点排序',
    isEnable: '是否启用',
    collectionDesc: '采集点描述',
    collectionUrl: '采集点地址',
    isState: '是否状态',
    // 采集点管理
    // 用户 start
    posName: '职位',
    mobilephone: '手机号',
    userState: '状态',
    aspRemark: '账号描述',
    editPwd: '密码',
    userPwd: '用户密码',
    // 用户 end
    // 一卡通 start
    cardSerno: '卡号',
    cardUsername: '持卡人',
    cardUserphone: '持卡人电话',
    cardType: '卡类型',
    cardState: '卡状态',
    IsEnable: '是否挂失',
    // 一卡通 end
    // 门禁系统 start
    floor: '楼层',
    floorLocation: '所在楼层',
    belongFloor: '楼层名称',
    floorAlias: '楼层别名',
    floorSort: '楼层排序',
    openType: '开门类型',
    cardDate: '刷卡时间',
    // 门禁系统 end
    // 停车场 start
    cardHolder: '持卡人姓名',
    // cardType: '',
    plateNumber: '车牌',
    inDate: '入场时间',
    outDate: '出场时间',
    inState: '入场状态',
    outState: '出场状态',
    charge: '收费',
    charegName: '收费人员',
    whenLong: '停车时长',
    inPicture: '入场图片',
    outPicture: '出场图片',
    // 停车场 end
    // 信息发布 start
    messageTitle: '标题',
    messageContent: '内容',
    messageLevel: '消息级别',
    releaseFlag: '发布标识',
    // 信息发布 end
    // 菜单管理 start
    menuId: '菜单ID',
    menuMenuName: '菜单名称',
    menuType: '菜单类型',
    menuUrl: '菜单地址',
    menuUpidName: '上级菜单名称',
    menuIcon: '菜单图标',
    menuIconOut: '菜单图标_Moveout',
    menuOrder: '菜单排序',
    isDel: '是否删除',
    menuTarget: '菜单目标',
    menuDesc: '菜单描述',
    menuVueUrl: '菜单地址',
    menuVirtualUrl: '虚拟菜单地址',
    menuVueTarget: '菜单目标',
    logOpDate: '操作时间',
    // 菜单管理 end
    // 楼层管理 start
    partitionName: '分区',
    floorName: '楼层名称',
    floorAliasName: '楼层别名',
    // 楼层管理 end
    // 设备管理 start
    brandName: '品牌名称',
    brandNameValue: '品牌名称',
    deviceAddress: '安装位置',
    deviceDesc: '相关描述',
    deviceFlag: '设备标识',
    deviceId: '设备ID',
    deviceIp: '设备IP',
    alarmState: '设备状态',
    onlineState: '在线状态',
    entSerno: '企业编码',
    equipType: '型号',
    floorId: '所属楼层ID',
    sysType: '归属系统',
    sysTypeName: '所属系统',
    targetIds: '设备标签',
    devupdateTime: '最后更新时间',
    estateDeviceId: '物管设备ID',
    locationTypeName: '位置类型',
    locationName: '详细位置',
    dvrAccount: '登录账号',
    dvrPwd: '密码',
    dvrIp: 'Ip',
    dvrPort: '端口',
    dvrChannel: '通道号',
    // 设备管理 end
    // 采集点管理  start
    collectionType: '采集点类型',
    collectionPosition: '采集点位置',
    collectionUnit: '采集点单位',
    // 采集点管理  end
    // 设备联动 start
    isDelete: '是否删除',
    linkageContent: '联动内容',
    linkageDesc: '联动描述',
    linkageDeviceId: '联动设备',
    linkageDeviceName: '联动设备名称',
    linkageEvent: '联动事件',
    linkageMethod: '方法编码',
    linkageName: '联动名称',
    linkageSysType: '联动系统',
    linkageSysTypeName: '联动系统名称',
    // 设备联动 end
    // 设备标签管理 start
    targetName: '标签名称',
    targetId: '标签ID',
    targetNames: '标签名称',
    deviceNames: '设备名称',
    targetIcon: '标签图标',
    // 设备标签管理 end
    // 告警管理 start
    ackedCount: '确认总数',
    alarmContent: '告警内容',
    alarmLevel: '告警级别',
    alarmTime: '告警时间',
    collectionId: '采集点ID',
    alarmPosition: '告警源',
    collectionStatus: '告警状态',
    unAckedCount: '未应答数',
    ackedCountOrUnAckedCount: '未应答数/确认总数',
    ackContent: '处理内容',
    ackDate: '应答时间',
    ackState: '应答状态',
    ackUser: '应答用户',
    alarmClass: '告警分类',
    alarmDate: '告警时间',
    alarmId: '告警ID',
    msgText: '告警内容',
    op: '操作人',
    presentValue: '现值',
    priority: '告警级别',
    sourceName: '告警源',
    status: '状态',
    fromState: '上一状态',
    toState: '当前状态',
    lowLimit: '低位值 ',
    offNormalValue: '不正常值',
    highLimit: '高位值',
    // 告警管理 end
    // 能耗历史记录 start
    collectionDate: '采集时间',
    collectionDay: '日',
    collectionMonth: '月',
    collectionYear: '日',
    energy: '累计冷量(KW.h)',
    energyH: '累计冷量（高位）',
    energyL: '累计冷量（低位）',
    flow: '瞬时流量(L/h)',
    flowH: '瞬时流量（高位）',
    flowL: '瞬时流量（低位）',
    power: '功率(W)',
    powerH: '功率（高位）',
    powerL: '功率（低位）',
    state: '',
    tempIn: '进水温度(℃)',
    tempOut: '出水温度(℃)',
    totalFlow: '累计流量(m³)',
    totalFlowH: '累计流量（高位）',
    totalFlowL: '累计流量（低位）',
    // 能耗历史记录 end
    // 联动日志  START
    alarm: '告警ID',
    execDate: '执行时间',
    execDesc: '执行描述',
    execResult: '执行结果',
    logId: '联动日志ID',
    // 联动日志 END
    // porisAccessRecord
    accessTime: '通行时间',
    cardId: '卡内码',
    porisCardState: '通行状态',
    // cardSerno: '卡编号',
    // cardState: '卡状态',
    // cardType: '卡类型',
    // deptName: '部门名称',
    deptNo: '部门编号',
    doorId: '门点标识',
    doorName: '门点名称',
    // id: ' 一卡通ID ',
    // isDel: '是否删除',
    // isEnable: '是否可用',
    // op: '',
    // opDate: '',
    userId: '人员ID',
    // userName: '人员名称',
    userNo: '人员编号',
    // porisAccessRecord
    // 楼层统计能量
    ahu1H: '1#空调(热)',
    ahu1C: '1#空调(冷)',
    ahu2C: '2#空调(冷)',
    ahu2H: '2#空调(热)',
    fcuh: '风机盘管(热)',
    fcuc: '风机盘管(冷)',
    // poris门设备状态
    description: '描述',
    deviceType: '设备类型',
    doorState: '门状态',
    lastStateTime: '更新时间',
    upDoorId: '父级门点标识',
    // poris门设备流水
    mngCode: '门点管理码',
    updateTime: '状态更新时间',
    // 工单情况
    recordId: '告警ID',
    orderId: '订单ID',
    externalId: '外部工单ID',
    orderStatus: '工单状态',
    dealTime: '处理时间',
    currentOrderStatus: '工单当前状态',
    // 联动逻辑字典表
    methodId: '联动方法ID',
    logicDesc: '逻辑描述',
    paramJson: '方法参数',
    // 联动方法字典表
    // linkageMethod: '方法编码',
    linkageMethodName: '方法名称',
    methodType: '方法类型',
    methodFlag: '是否启用',
    methodName: '方法名称',
    // description: '描述'
    remark: '备注',
    generateTime: '生成时间',
    relation: '逻辑关系',
    // 流程管理
    modelId: '模型id',
    modelName: '模型名称',
    modelKey: '模型key',
    type: '类型',
    commonUsed: '常用',
    opTime: '操作时间',
    isCommonUsed: '是否常用',
    processModel: '流程模型',
    formName: '表单名称',
    formTitle: '表单标题',
    creator: '创建者',
    deletedMark: '删除标志',
    instId: '实例编号',
    billName: '单据名',
    starterName: '发起人',
    startTime: '发起时间',
    nextUserName: '下节点操作人',
    duration: '持续时间',
    groupName: '班组名称',
    groupExplain: '班组说明',
    persons: '人员',
    inspectionName: '巡检名称',
    inspectionUser: '巡检人员',
    inspectionTime: '巡检时刻',
    inspectionExplain: '巡检说明',
    inspectionEndTime: '巡检结束时间',
    groupDetail: '班组详情',
    deviceDetail: '设备详情',
    isPublish: '是否发布',
    devicePictures: '设备图片',
    chooseDevice: '选择设备',
    taskNo: '任务编号',
    taskName: '任务名',
    track: '跟踪图',
    stateName: '任务状态',
    // 能耗类型
    energyTypeName: '类型名称',
    energySerno: '类型编码',
    formula: '转换煤公式',
    changePriceLevel: '请选择价格档位',
    // 能耗价格
    priceLevelName: '档位名称',
    lvalue: '下限值',
    mvalue: '上限值',
    levelPrice: '档位价格（元）',
    // 能耗分类报表
    time: '时间',
    electric: '电',
    consumption: '用量(度)',
    cost: '费用(十元)',
    water: '水',
    gas: '气',
    centralHeating: '集中供热',
    centralCooling: '集中供热冷',
    otherEnergy: '其他能源',
    // 仪表类型
    meterName: '仪表名',
    meterUnint: '计量单位',
    // 管理区管理
    precinctName: '管理区名称',
    precincGrouptName: '管理区分组名称',
    precinctPhone: '电话',
    ownerPhone: '业主电话',
    precinctAddr: '地址',
    area: '面积(m²)',
    greenArea: '绿化面积(m²)',
    usableArea: '使用面积(m²)',
    shareArea: '公摊面积(m²)',
    greeningRate: '绿化率(%)',
    plotRatio: '容积率(%)',
    buildTime: '建筑时间',
    completionTime: '交房时间',
    // 楼栋管理
    superior: '上级机构',
    buildingName: '名称',
    buildingSeq: '序号',
    buildingFloor: '层数',
    buildingType: '类型',
    buildingUse: '用途',
    orientation: '朝向',
    building: '楼栋',
    buildingAlias: '楼宇',
    directorialArea: '管理区域',
    explain: '说明',
    areaGroupIdName: '上级机构',
    roomCode: '房间代码',
    roomState: '房间状态',
    curCustom: '当前业主',
    customInfo: '业主信息',
    moveBack: '迁入迁出',
    moveDate: '迁出日期',
    isMoveCost: '是否迁移费用',
    newCustomer: '新业主',
    roomType: '房间类型',
    customer: '客户',
    contactNumber: '联系电话',
    buildArea: '建筑面积(m²)',
    useArea: '使用面积(m²)',
    roomSeq: '房间序号',
    unitName: '单元名称',
    initialAcceptanceDate: '初次验收日期',
    secondAcceptanceDate: '二次验收日期',
    houseIsRentable: '能否出租',
    memberRelationship: '家庭成员',
    irregularitiesRectification: '违规整改',
    garageName: '车库名称',
    undergroundParking: '地下停车场',
    parkingCode: '车位号',
    subsidiaryGarage: '所属车库',
    parkingOwner: '车位业主',
    parkingArea: '车位面积(m²)',
    leaseControl: '租赁控制',
    curState: '当前状态',
    saleDate: '出售日期',
    carNumberOne: '车牌号1',
    carNumberTwo: '车牌号2',
    parkingType: '车位类别',
    advCode: '广告位号',
    chargeProject: '收费项目',
    timeFrame: '应收时段',
    outTime: '迁出时间',
    // 报修
    repairTotal: '报修总数',
    waitDistribute: '待分配',
    untreated: '未处理',
    processed: '处理中',
    completed: '已完成',
    filed: '已归档',
    satisfactionNumber: '满意数',
    satisfaction: '满意度',
    repairPerson: '报修人',
    repairMan: '报修人员',
    repairDevice: '报修设备',
    positionInfo: '位置信息',
    houseBelong: '所属房间',
    appointTo: '指派给',
    redeployTo: '转派给',
    sendExtandAddr: '派工地址',
    sendExtandTime: '派工时间',
    sendExtandRemark: '派工备注',
    repairContact: '报修人员联系方式',
    addPeople: '添加人',
    addPeopleContact: '联系方式',
    addTime: '添加时间',
    appointmentTime: '预约时间',
    source: '来源',
    house: '房间',
    maintainPerson: '维修人员',
    finishTime: '完成时间',
    performance: '完成情况',
    orderStatue: '工单状态',
    repairContent: '报事报修内容',
    customerType: '客户类型',
    listNumber: '单号',
    repairNum: '报修单号',
    processTime: '受理时间',
    processMan: '受理人',
    contactMan: '联系人',
    repairType: '报修类型',
    repairDetail: '报修详情',
    position: '位置',
    installPosition: '安装位置',
    pecinct: '管理区',
    urgencyDegree: '紧急程度',
    workOrderType: '工单大类',
    classWorkList: '工单子类',
    evaluate: '评价',
    valuateContent: '评价内容',
    grade: '等级',
    houseCode: '房间代码',
    reservation: '预约时间',
    place: '地点',
    handler: '处理人',
    data: '日期',
    detailInfo: '详情情况',
    processRecord: '处理记录',
    // 人口管理
    name: '名称',
    customerName: '客户名称',
    customerNumber: '编号',
    customerPhone: '电话',
    fax: '传真',
    phoneOne: '联系电话1',
    phoneTwo: '联系电话2',
    phoneThree: '联系电话3',
    email: '电子邮箱',
    mailbox: '邮箱',
    documentType: '证件类型',
    documentNumber: '证件号码',
    sex: '性别',
    birthday: '出生日期',
    nativePlace: '籍贯',
    currentAddress: '现住地址',
    companyType: '公司类型',
    IDNumber: '身份证号码',
    QQ: 'QQ',
    weiNumber: '微信号',
    profession: '职业',
    hobby: '兴趣爱好',
    censusAddr: '户籍地址',
    companyNature: '公司性质',
    companyNum: '公司人数',
    companyAccountName: '公司开户名称',
    companyBankAccount: '银行账号',
    companyCreditCode: '统一社会信用代码',
    companyLicence: '执照有效期',
    companyLegal: '法人代表',
    phoneNumber: '电话号码',
    companyPhoneFax: '单位传真',
    managementArea: '所属管理区',
    areaBelong: '所属区域',
    areaGroup: '所属管理区分组',
    buildingBelong: '所属楼栋',
    wechatNum: '微信号',
    loginAccount: '登录账户',
    bindingHouse: '绑定房屋',
    auditStatus: '审核状态',
    personName: '姓名',
    visitUserName: '访问对象',
    visitUserMobile: '访问对象手机号',
    arrangePerson: '安排人员',
    arrangeRemark: '安排说明',
    handSuggestion: '处理意见',
    upLoadRepairImg: '报修图片',
    cifrmUserPwd: '确认密码',
    // 物业管理
    // 固定资源管理
    parkName: '园区名称',
    assetCoding: '资产编码',
    assetName: '资产名称',
    purchaseTime: '购入时间',
    userDept: '使用部门',
    number: '数量',
    originalValue: '资产原值(元)',
    accumulatedDiscount: '累计折旧',
    capitalAccount: '资产净值(元)',
    profitAndLoss: '是否盈亏',
    classify: '分类',
    actualCapitalAccount: '实际资产净值(元)',
    personLiable: '责任人',
    reason: '原因',
    depositPlace: '存入地点',
    capitalState: '状态',
    uploadImg: '上传图片',
    // 物料管理
    materialName: '材料名称',
    brand: '品牌',
    specification: '规格型号',
    unit: '单位',
    supplierName: '名称',
    fixedPhone: '固定电话',
    mobilePhone: '手机',
    warehousename: '仓库名称',
    chargePerson: '负责人',
    additionalTime: '新增时间',
    actionsState: '操作状态',
    actionDate: '操作日期',
    supplier: '供应商',
    receipt: '入库',
    retrieval: '出库',
    retrievalNum: '出库单号',
    receiptOrderNum: '入库单号',
    billDate: '单据日期',
    warehouse: '仓库',
    makingPeople: '制单人',
    costPrice: '成本价(元)',
    amount: '金额(元)',
    totalAmount: '合计金额(元)',
    receiptListDetail: '详细入库列表',
    retrievalListDetail: '详细出库列表',
    goodsName: '商品名称',
    summation: '合计(元)',
    unitPrice: '单价(元)',
    isReturnNum: '可退数量',
    returnQuantity: '退货数量',
    returnAmount: '退货金额',
    document: '单据',
    documentNum: '单据号',
    importance: '重要程度',
    currentInventory: '当前库存',
    inventoryNum: '库存数量',
    referencePrice: '参考价格',
    unitCost: '单位成本',
    primeCost: '成本(元)',
    modelNum: '规格型号',
    requirements: '要求处理事项',
    consumptionDepartment: '领用部门',
    departmentName: '部门名',
    goAndGet: '本次拟领取数量',
    retrievalPrice: '出库价',
    // 巡更系统
    pointName: '名称',
    pointCode: '编号',
    pointStatus: '状态',
    pointRemark: '备注',
    patrolPointsCount: '巡更点数量',
    searchPoints: '选择巡更点',
    sort: '调整顺序',
    patrolPlant: '日巡更计划',
    lineName: '名称',
    lineCode: '编号',
    lineStatus: '状态',
    lineRemark: '备注',
    // 用车管理
    applyName: '申请人姓名',
    applyPhone: '申请人电话',
    applyTime: '申请时间',
    useNumber: '用车人数',
    carOutTime: '出发时间',
    carBackTime: '返回时间',
    origin: '出发地',
    destination: '目的地',
    attachFile: '文件',
    applyStatus: '状态',
    timeSpan: '时间段',
    useCount: '用车次数',
    // 场所管理
    meetingRoomName: '场所名称',
    meetingName: '会议名称',
    meetingRoomDesc: '场所描述',
    meetingRoom: '场所',
    meetingRoomAddr: '场所地址',
    meetingRoomCapacity: '场所容量(人)',
    isVideo: '是否有投影仪',
    isTv: '是否有电视',
    isSound: '是否有音响',
    isComputer: '是否有电脑',
    applyDate: '申请日期',
    flowNumber: '流程编号',
    department: '所属部门',
    meetingThemes: '会议主题',
    Attendees: '参会人员',
    beginTime: '开始时间',
    endTime: '结束时间',
    contentOverview: '内容概述',
    Minutes: '会议纪要',
    meetingTimeSpan: '会议时间',
    meetingFiles: '会议文件',
    // 专家库期刊库管理
    birthMonthAndDay: '出生年月',
    Nation: '民族',
    unitWhoBelongs: '所在单位',
    deptWhoBelongs: '所在部门',
    professionalTitle: '专业职称',
    administrativePost: '行政职务',
    nameOfPublication: '期刊名称',
    periodicalNo: '刊号',
    issueNumber: '期数',
    sponsorUnit: '主办单位',
    periodicalLevel: '期刊级别',
    content: '内容',
    // 通知公告
    noticeType: '通知类型',
    receiverObjectName: '通知对象',
    publishDate: '发布时间',
    isReceipt: '需要回执',
    noticeTitle: '通知标题',
    noticeContent: '通知内容',
    viewedUsers: '已读用户',
    knowedUsers: '已回执用户',
    // 宿舍管理
    BuildingName: '楼栋名称',
    workUnit: '工作单位',
    stayTime: '住宿时间',
    trainee: '学员姓名',
    studyNum: '学号',
    isPublicServant: '是否公务员',
    isArrangeDorm: '是否安排宿舍',
    messageNotice: '短信通知',
    stop: '停用',
    adjustPersonnel: '调整人员',
    roomNum: '房间号',
    dormitoryType: '宿舍类型',
    dormitoryStatus: '宿舍状态',
    rootType: '几人间',
    checkInDate: '入住时间',
    batchAdd: '批量添加',
    student: '学员',
    liveNum: '可住人数',
    dormitoryTotal: '宿舍总数',
    usageRate: '使用率',
    useNum: '使用数',
    // 物品管理
    applyNum: '申请单号',
    goodsType: '物品类型',
    goodsTypeName: '物品类型名称',
    officeNo: '办公室号',
    companies: '所属企业',
    applyNumber: '申请数',
    receptionOffice: '领用办公室',
    // 报餐管理
    breakfastCount: '早餐次数',
    lunchCount: '午餐次数',
    dinnerCount: '晚餐次数',
    diningCount: '实际就餐次数',
    studentWorkNumber: '学号/工号',
    // 公文管理
    referenceNumber: '文号',
    securityClassification: '密级',
    securityDeadline: '保密期限',
    copies: '份数',
    mainSend: '主送',
    copySend: '抄送',
    postType: '发文类型',
    accessory: '附件',
    themeWord: '主题词',
    title: '标题',
    hurrySlowly: '急缓',
    registrant: '登记人',
    receiptDate: '收文日期',
    currentNode: '当前节点',
    receiptNo: '收文编号',
    documentReceived: '来文单位',
    original: '原文',
    // 门户网站管理
    // 栏目管理
    columnName: '栏目名称',
    moduleType: '模块类型',
    columnUpidName: '上级栏目名称',
    columnNo: '栏目编码',
    columnOrder: '栏目排序',
    columnDesc: '栏目简介',
    // 文章管理
    subordinateColumn: '所属栏目',
    subordinateChildColumn: '所属子栏目',
    articleTitle: '文章标题',
    examineStatu: '审核状态',
    examineRemark: '审核备注',
    isTop: '是否置顶',
    author: '发布者',
    // 短信服务
    smsContent: '短语内容',
    smsType: '常用语类型'
  },
  button: {
    check: '查看',
    checkDetail: '查看明细',
    uninstall: '卸载',
    deployment: '部署',
    search: '搜索',
    clear: '清空',
    add: '添加',
    export: '导出',
    import: '导入',
    importMateriel: '导入物料',
    chooseImportFile: '选择导入文件',
    edit: '编辑',
    publish: '发布',
    draft: '草稿',
    delete: '删除',
    cancel: '取 消',
    confirm: '确 定',
    selectAll: '全 选',
    affirm: '确认',
    print: '打印',
    enable: '启用',
    disable: '禁用',
    editPwd: '修改密码',
    start: '开始',
    build: '生成',
    pickUp: '接警',
    sendExtand: '派单',
    exec: '执行记录',
    log: '日志记录',
    track: '跟踪图',
    claim: '认领',
    revoke: '撤销',
    submit: '提 交',
    relation: '绑定设备',
    contrast: '对比',
    determine: '确定',
    exportExcel: '导出Excel',
    changeProprietor: '绑定业主',
    changeProprietorHistory: '绑定业主历史记录',
    addPakinglotoa: '添加车位',
    bindingOwner: '绑定业主',
    bindingHouse: '绑定房屋',
    finish: '完成',
    redeploy: '转派',
    archive: '归档',
    applyPostpone: '申请延期',
    evaluate: '评价',
    maintainRemark: '维修追加说明',
    auditPass: '通过',
    auditUnPass: '不通过',
    verify: '审核',
    verifyPass: '审核通过',
    verifyUnPass: '审核不通过',
    save: '保存',
    picking: '领料',
    returned: '退料',
    cancellation: '作废',
    recover: '恢复',
    normal: '正常',
    showQRCode: '查看二维码',
    plantDetail: '计划详情',
    UploadMinutes: '上传纪要',
    close: '关 闭',
    sendReceipt: '发送回执'
  }
}
