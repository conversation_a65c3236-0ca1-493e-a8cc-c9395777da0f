// 时间范围组件配置项
const pickerOptions = {
  shortcuts: [{
    text: '今天',
    onClick(picker) {
      const now = new Date()
      const start = new Date(`${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()} 00:00:00`)
      const endDate = new Date(Date.now() + 3600000 * 24)
      const end = new Date(`${endDate.getFullYear()}-${endDate.getMonth() + 1}-${endDate.getDate()} 00:00:00`)
      picker.$emit('pick', [start, end])
    }
  }, {
    text: '昨天',
    onClick(picker) {
      const now = new Date()
      const end = new Date(`${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()} 00:00:00`)
      const startDate = new Date(Date.now() - 3600000 * 24)
      const start = new Date(`${startDate.getFullYear()}-${startDate.getMonth() + 1}-${startDate.getDate()} 00:00:00`)
      picker.$emit('pick', [start, end])
    }
  }, {
    text: '一周以前',
    onClick(picker) {
      const date = new Date()
      const start = new Date('2021-1-1 00:00:00')
      date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
      const end = new Date(`${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()} 00:00:00`)
      picker.$emit('pick', [start, end])
    }
  }]
}
// 告警等级常量
const warnLevel = [
  { label: '紧急', value: 1 },
  { label: '严重', value: 2 },
  { label: '一般', value: 3 },
  { label: '次要', value: 4 }
]

const offlineTableConfig = {
  propList: [
    { prop: 'deviceName', label: '设备名称', minWidth: '100', useTable: false, sort: 'custom', className: 'pointer' },
    { prop: 'deviceSerno', label: '设备编号', minWidth: '100', useTable: false, showOverflowTooltip: true, sort: 'custom', className: 'pointer' },
    { prop: 'areaCode', label: '设备所在地', minWidth: '100', useTable: false, showOverflowTooltip: true },
    { prop: 'intersectionName', label: '所属路口', minWidth: '100', useTable: false, showOverflowTooltip: true },
    { prop: 'sysTypeName', label: '专题类型', minWidth: '100', useTable: false },
    { prop: 'deviceTypeName', label: '设备类型', minWidth: '100', useTable: false },
    { prop: 'manufacturerName', label: '厂商', minWidth: '80', useTable: false },
    { prop: 'equipType', label: '型号', minWidth: '100', useTable: false },
    { prop: 'reachableState', label: 'IP连接', minWidth: '70', useTable: false, slotName: 'reachableState' },
    { prop: 'alarmContent', label: '告警内容', minWidth: '80', useTable: false },
    { prop: 'priority', label: '告警级别', minWidth: '80', useTable: false },
    { prop: 'alarmCount', label: '当日频次', minWidth: '80', useTable: false, slotName: 'alarmCount' },
    { prop: 'offlineTime', label: '离线时间', minWidth: '100', useTable: false, sort: 'custom' }
  ],
  showIndexColumn: true,
  showSelectColumn: false
}

// 故障、性能常量
const breakdownSearchItems = {
  inputItem: [
    {
      type: 'newDeviceSearch',
      label: '',
      deviceSearchConfig: {
        nameSearchPlaceholder: '设备名称/设备编号/告警内容',
        isMutiSearch: true, // 是否显示多项选择器(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
        isArea: true, // 是否显示区域查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
        isSysType: true, // 是否显示专题类型查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
        isManufacturerCode: false
      }
    },
    {
      field: 'priority',
      type: 'select',
      placeholder: '告警级别',
      options: warnLevel
    },
    {
      field: 'timerange',
      type: 'datetimerange',
      label: '告警时间',
      placeholder: '请选择告警时间范围',
      dateType: 'datetime',
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      pickerOptions
    }
  ]
}
const breakdownTableConfig = {
  propList: [
    { prop: 'deviceName', label: '设备名称', minWidth: '100', useTable: false, sort: 'custom', className: 'pointer' },
    { prop: 'deviceSerNo', label: '设备编号', minWidth: '100', useTable: false, showOverflowTooltip: true, sort: 'custom', className: 'pointer' },
    { prop: 'deviceAddress', label: '设备所在地', minWidth: '100', useTable: false, showOverflowTooltip: true },
    { prop: 'intersectionName', label: '所属路口', minWidth: '100', useTable: false, showOverflowTooltip: true },
    { prop: 'sysTypeName', label: '专题类型', minWidth: '100', useTable: false },
    { prop: 'deviceTypeName', label: '设备类型', minWidth: '100', useTable: false },
    { prop: 'msgText', label: '告警内容', minWidth: '150', useTable: false },
    { prop: 'priority', label: '告警级别', minWidth: '100', useTable: false },
    { prop: 'todayAlarmCount', label: '今日频次', minWidth: '100', useTable: false, slotName: 'todayAlarmCount' },
    { prop: 'lastAlarmDate', label: '告警时间', minWidth: '100', useTable: false, sort: 'custom' },
    { prop: 'hander', label: '操作', minWidth: '100', useTable: false, slotName: 'handler' }
  ],
  showIndexColumn: true,
  showSelectColumn: false
}

export const pageConfig = {
  offline: {
    realTime: {
      searchItems: {
        inputItem: [
          {
            type: 'newDeviceSearch',
            label: '',
            deviceSearchConfig: {
              nameSearchPlaceholder: '设备名称/设备编号/告警内容',
              isTag: true,
              isMutiSearch: true, // 是否显示多项选择器(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
              isArea: true, // 是否显示区域查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
              isSysType: true, // 是否显示专题类型查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
              isDeviceType: true, // 是否显示设备类型查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
              isManufacturerCode: true // 是否显示设备厂商和型号联动查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
            }
          },
          /*  {
             field: 'priority',
             type: 'select',
             placeholder: '告警级别',
             options: warnLevel
           },
           {
             field: 'timerange',
             type: 'datetimerange',
             label: '离线时间',
             placeholder: '请选择离线时间范围',
             dateType: 'datetime',
             valueFormat: 'yyyy-MM-dd HH:mm:ss',
             pickerOptions
           } */
        ]
      },
      tableConfig: offlineTableConfig
    },
    history: {
      searchItems: {
        inputItem: [
          {
            type: 'newDeviceSearch',
            label: '',
            deviceSearchConfig: {
              nameSearchPlaceholder: '设备名称/设备编号/告警内容',
              isMutiSearch: true, // 是否显示多项选择器(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
              isArea: true, // 是否显示区域查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
              isSysType: true, // 是否显示专题类型查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
              isDeviceType: true, // 是否显示设备类型查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
              isManufacturerCode: true // 是否显示设备厂商和型号联动查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
            }
          },
          /* {
            field: 'priority',
            type: 'select',
            placeholder: '告警级别',
            options: warnLevel
          }, */
          /* {
            field: 'timerange',
            type: 'datetimerange',
            label: '离线时间',
            placeholder: '请选择离线时间范围',
            dateType: 'datetime',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            pickerOptions
          } */
        ]
      },
      tableConfig: {
        propList: [
          { prop: 'deviceName', label: '设备名称', minWidth: '100', useTable: false, sort: 'custom', className: 'pointer' },
          { prop: 'deviceSerno', label: '设备编号', minWidth: '100', useTable: false, showOverflowTooltip: true, sort: 'custom', className: 'pointer' },
          { prop: 'areaCodeName', label: '设备所在地', minWidth: '100', useTable: false, showOverflowTooltip: true },
          { prop: 'intersectionName', label: '所属路口', minWidth: '100', useTable: false, showOverflowTooltip: true },
          { prop: 'sysTypeName', label: '专题类型', minWidth: '100', useTable: false },
          { prop: 'deviceTypeName', label: '设备类型', minWidth: '100', useTable: false },
          { prop: 'manufacturerName', label: '厂商', minWidth: '100', useTable: false },
          { prop: 'equipType', label: '型号', minWidth: '100', useTable: false },
          { prop: 'alarmContent', label: '告警内容', minWidth: '80', useTable: false },
          { prop: 'priority', label: '告警级别', minWidth: '100', useTable: false },
          { prop: 'offlineTime', label: '离线时间', minWidth: '100', useTable: false, sort: 'custom' },
          { prop: 'recoverTime', label: '恢复时间', minWidth: '100', useTable: false, sort: 'custom' }
        ],
        showIndexColumn: true,
        showSelectColumn: false
      }
    }
  },
  breakdown: {
    realTime: {
      searchItems: breakdownSearchItems,
      tableConfig: breakdownTableConfig
    },
    history: {
      searchItems: breakdownSearchItems,
      tableConfig: {
        propList: [
          { prop: 'deviceName', label: '设备名称', minWidth: '100', useTable: false, sort: 'custom', className: 'pointer' },
          { prop: 'deviceSerNo', label: '设备编号', minWidth: '100', useTable: false, showOverflowTooltip: true, sort: 'custom', className: 'pointer' },
          { prop: 'deviceAddress', label: '设备所在地', minWidth: '100', useTable: false, showOverflowTooltip: true },
          { prop: 'intersectionName', label: '所属路口', minWidth: '100', useTable: false, showOverflowTooltip: true },
          { prop: 'sysTypeName', label: '专题类型', minWidth: '100', useTable: false },
          { prop: 'deviceTypeName', label: '设备类型', minWidth: '100', useTable: false },
          { prop: 'msgText', label: '告警内容', minWidth: '100', useTable: false },
          { prop: 'priority', label: '告警级别', minWidth: '100', useTable: false },
          { prop: 'alarmDate', label: '告警时间', minWidth: '100', useTable: false, sort: 'custom' }
        ],
        showIndexColumn: true,
        showSelectColumn: false
      }
    }
  },
  performance: {
    realTime: {
      searchItems: {
        inputItem: [
          {
            type: 'newDeviceSearch',
            label: '',
            deviceSearchConfig: {
              nameSearchPlaceholder: '设备名称/设备编号/告警内容',
              isTag: true,
              isMutiSearch: true, // 是否显示多项选择器(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
              isArea: true, // 是否显示区域查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
              isSysType: true, // 是否显示专题类型查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
              isManufacturerCode: false
            }
          },
          {
            field: 'priority',
            type: 'select',
            placeholder: '告警级别',
            options: warnLevel
          },
          {
            field: 'timerange',
            type: 'datetimerange',
            label: '告警时间',
            placeholder: '请选择告警时间范围',
            dateType: 'datetime',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            pickerOptions
          }
        ]
      },
      tableConfig: { ...breakdownTableConfig, showSelectColumn: true }
    },
    history: {
      searchItems: {
        inputItem: [
          {
            type: 'newDeviceSearch',
            deviceSearchConfig: {
              nameSearchPlaceholder: '设备名称/设备编号/告警内容',
              isMutiSearch: true, // 是否显示多项选择器(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
              isArea: true, // 是否显示区域查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
              isSysType: true, // 是否显示专题类型查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
              isManufacturerCode: false
            }
          },
          {
            field: 'priority',
            type: 'select',
            placeholder: '告警级别',
            options: warnLevel
          },
          {
            field: 'timerange',
            type: 'datetimerange',
            label: '告警时间',
            placeholder: '请选择告警时间范围',
            dateType: 'datetime',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            pickerOptions
          },
          {
            field: 'states',
            type: 'select',
            placeholder: '告警状态',
            options: [
              { label: '未处理', value: '0,1' },
              { label: '已处理', value: '3' },
              { label: '已关闭', value: '2' }
            ]
          }
        ]
      },
      tableConfig: {
        propList: [
          { prop: 'deviceName', label: '设备名称', minWidth: '100', useTable: false, sort: 'custom', className: 'pointer' },
          { prop: 'deviceSerno', label: '设备编号', minWidth: '100', useTable: false, showOverflowTooltip: true, sort: 'custom', className: 'pointer' },
          { prop: 'deviceAddress', label: '设备所在地', minWidth: '100', useTable: false, showOverflowTooltip: true },
          { prop: 'intersectionName', label: '所属路口', minWidth: '100', useTable: false, showOverflowTooltip: true },
          { prop: 'sysTypeName', label: '专题类型', minWidth: '100', useTable: false },
          { prop: 'deviceTypeName', label: '设备类型', minWidth: '100', useTable: false },
          { prop: 'msgText', label: '告警内容', minWidth: '100', useTable: false },
          { prop: 'priority', label: '告警级别', minWidth: '100', useTable: false },
          { prop: 'lastAlarmDate', label: '告警时间', minWidth: '100', useTable: false, sort: 'custom' },
          { prop: 'alarmEndDate', label: '告警关闭时间', minWidth: '100', useTable: false, sort: 'custom' },
          { prop: 'state', label: '状态', minWidth: '100', useTable: false }
        ],
        showIndexColumn: true,
        showSelectColumn: false
      }
    }
  }
}

