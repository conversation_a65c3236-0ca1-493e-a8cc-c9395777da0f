<template>
  <div>
    <el-form ref="dataForm" :model="childData" :rules="rules" label-width="10.19vh">
      <el-row>
        <el-form-item label="分组名称" prop="groupName">
          <el-input v-model="childData.groupName" class="form-item-width" maxlength="20" />
        </el-form-item>
        <el-form-item label="排序" prop="orderNo">
          <el-input-number v-model="childData.orderNo" :min="0" :max="999" style="width: 27.7800vh;" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="childData.remark" type="textarea" maxlength="100" class="form-item-width" />
        </el-form-item>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handCancle">{{ $t('button.cancel') }}</el-button>
      <el-button type="primary" @click="handCreate">{{ $t('button.confirm') }}</el-button>
    </div>
  </div>
</template>

<script>
import { insertLampGroup, updateLampGroup } from '@/api/PMS/LightManage/LightEquipment/management'

// import { fetchList as fetchLessee } from '@/api/otherSystem/dataManage/lessee.js'
export default {
  props: {
    showDialog: {
      type: Boolean
    },
    checked: {
      type: Object
    },
    type: {
      type: String
    }
    // deviceTypeId: {
    //   type: Number
    // }
  },
  data() {
    return {
      rules: {
        groupName: [{ required: true, message: '请输入分组名称', trigger: 'blur' }],
        orderNo: [{ required: true, message: '请输入分组排序', trigger: 'change' }]

      },
      childData: {
        groupName: '',
        remark: '',
        orderNo: ''
        // upProductId: this.checked.id,
        // deviceTypeId: this.deviceTypeId
      },
      lesseeList: []
    }
  },
  watch: {
    showDialog: function(n, o) {
      if (n) {
        this.$refs.dataForm.resetFields()
        this.init()
      }
    }
  },
  created() {
    // this.getLessee()
    this.init()
  },
  methods: {
    getLessee() {
      fetchLessee({ page: 1, limit: 999999 }).then(res => {
        if (res.data.code === 200) {
          this.lesseeList = res.data.data
        }
      })
    },
    init() {
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      if (this.type === 'edit') {
        console.log('-------')
        console.log(this.checked)
        this.childData.groupName = this.checked.groupName
        this.childData.orderNo = this.checked.orderNo
        this.childData.remark = this.checked.remark
        this.childData.groupId = this.checked.groupId
      } else {
        this.childData = {
          groupName: '',
          orderNo: '',
          remark: ''
          // upProductId: this.checked.id,
          // deviceTypeId: this.deviceTypeId
        }
      }
    },
    handCreate() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.type === 'edit') {
            updateLampGroup(this.childData).then(res => {
              if (res.data.code === 200) {
                this.$emit('handCancle', true)
                this.Alert({ alertType: 'update' })
              }
            })
          } else if (this.type === 'add') {
            console.log('==')
            console.log(this.childData)
            insertLampGroup(this.childData).then(res => {
              if (res.data.code === 200) {
                this.$emit('handCancle', true)
                this.Alert({ alertType: 'add' })
              }
            })
          }
        } else {
          this.Alert({ alertType: 'valid' })
        }
      })
    },
    handCancle() {
      this.$emit('handCancle', false)
    }
  }
}
</script>

<style>
</style>
