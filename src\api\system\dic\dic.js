import request from '@/utils/request'
export function fetchList(data) {
  return request({
    url: '/userService/api/dict/list',
    method: 'post',
    data
  })
}

export function fetchDeviceTypeList() {
  return request({
    url: '/cmdbService/confCiType/list',
    method: 'get'
  })
}

export function fetchListByType(dicType) {
  const data = { dicType }
  return request({
    url: '/userService/api/dict/list',
    method: 'post',
    data
  })
}
// 获取专题、设备类型
export function getDeviceType(params) {
  return request({
    url: '/devService/api/shareSearch/querySysDeviceTreeList',
    method: 'get',
    params
  })
}

// 获取区域
export function queryDicTree(data) {
  return request({
    url: '/userService/api/dict/queryDicTree',
    method: 'post',
    data
  })
}

// 获取街道
export function getStreets(data) {
  return request({
    url: '/devService/api/shareSearch/queryAreaOrStreet',
    method: 'post',
    data
  })
}

// 获取路口列表
export function getIntersetions(data) {
  return request({
    url: '/devService/api/shareSearch/queryIntersection',
    method: 'post',
    data
  })
}

export function queryManufacturer(data) {
  return request({
    url: '/devService/api/shareSearch/queryManufacturer',
    method: 'post',
    data
  })
}

export function queryModel(data) {
  return request({
    url: '/devService/api/shareSearch/queryModel',
    method: 'post',
    data
  })
}

export function queryDeviceType(data) {
  return request({
    url: '/devService/api/shareSearch/queryDeviceType',
    method: 'post',
    data
  })
}

export function queryDicRoad(data) {
  return request({
    url: '/userService/api/dict/queryDicRoad',
    method: 'post',
    data
  })
}

export function queryDicGroup(data) {
  return request({
    url: '/userService/api/dict/queryDicGroup',
    method: 'post',
    data
  })
}
