<!doctype html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>图片图层</title>
    <script type="text/javascript">
        window._AMapSecurityConfig = {
            securityJsCode:'35f5323d3164e826feeac2d91434179d',
        }
    </script>
    <script src="https://webapi.amap.com/maps?v=2.0&key=8b2d406adbfe8388da8d9565fb680596&plugin=AMap.IndexCluster,AMap.Scale,AMap.HawkEye,AMap.ToolBar,AMap.ControlBar,AMap.Polyline"></script>
    <script src="/static/js/config.js"></script>
    <!-- <script src="https://webapi.amap.com/maps?v=2.0&key=510425d9ec1bac421ab35295a1ea4b97&plugin=AMap.MarkerCluster"></script> -->
    <!-- <script src="https://webapi.amap.com/loca?v=1.2.1&key=510425d9ec1bac421ab35295a1ea4b97&plugin=AMap.MarkerCluster"></script> -->
    <style>
        html,
        body {
            width: 100%;
            height: 100%;
            margin: 0;
            font-family: "微软雅黑";
            overflow: hidden;
        }

        .amap-logo, .amap-copyright {
            visibility: hidden;
        }

        #allmap {
            width: 100%;
            height: 100%;
        }

        #r-result {
            width: 100%;
            margin-top: 5px;
        }

        #mapAdress {
            position: absolute;
            background: white;
            display: none;
        }

        #deviceList {
            position: absolute;
            display: none;
        }

        .anchorBL {
            display: none;
        }

        p {
            margin: 5px;
            font-size: 14px;
        }

        .donghua1 {
            height: 32px;
            width: 32px;
            position: relative;
        }

        .donghua1:hover {
            animation: rotate 1s linear infinite;
        }

        .donghua1::before {
            content: '';
            height: 8px;
            width: 32px;
            background: #000;
            opacity: .8;
            border-radius: 50%;
            position: absolute;
            top: 35px;
            left: 0;
            animation: shadow 1s linear infinite;
        }

        .donghua2 {
            width: 32px;
            height: 32px;
            margin: 15px 0;
            animation: breath 0.8s infinite;
            animation-direction: alternate;
            -webkit-animation-direction: alternate;
            /* -webkit-transition: all 1s ease;
			-moz-transition: all 1s ease;
			-o-transition: all 1s ease; */
        }

        .donghua3 {
            height: 32px;
            width: 32px;
            position: relative;
            animation: breath 1.2s infinite;
        }
        .boxDetail {
            background-color: rgba(15, 61, 103, 0.8);
            color: white;
            font-size: 2vhvw;
            line-height: 25px;
            white-space: nowrap;
            position: absolute;
            bottom: 45px;
            left: -6.5vw;
            border: 1px solid #377aff;
            border-radius: 0.3vw;
            padding:0.3vw 0.7vw;
            z-index: 200;
        }
        .deviceDetail {
            background: #0F3D67;
            background-color: rgba(15, 61, 103, 0.8);
            color: white;
            font-size: 0.7vw;
            line-height: 25px;
            white-space: nowrap;
            position: absolute;
            bottom: 45px;
            /* left: -6.5vw; */
            border: 1px solid #0F3D67;
            border-radius: 0.3vw;
            padding: 0.3vw;
            z-index: 101;
        }

        .closeBtn {
            color: white;
            cursor: pointer;
        }

        .borrow {
            position: absolute;
            bottom: -6px;
            left: 50%;
            margin-right: 3px;
            border-top-color: #ebeef5;
            border-bottom-width: 0;
            border-width: 6px;
            filter: drop-shadow(0 2px 12px rgba(0, 0, 0, .03));
        }

        .borrow:after {
            bottom: -6px;
            margin-left: -11px;
            border-top-color: #0F3D67 !important;
            border-bottom-width: 0;
            content: " ";
            border-width: 6px;
            position: absolute;
            display: block;
            width: 0;
            height: 0;
            border-color: transparent;
            border-style: solid;
        }
        .borrow3 {
            position: absolute;
            bottom: -6px;
            left: 55%;
            margin-right: 3px;
            border-top-color: #ebeef5;
            border-bottom-width: 0;
            border-width: 6px;
            filter: drop-shadow(0 2px 12px rgba(0, 0, 0, .03));
        }

        .borrow3:after {
            bottom: -6px;
            margin-left: -11px;
            border-top-color: #00ff97 !important;
            border-bottom-width: 0;
            content: " ";
            border-width: 6px;
            position: absolute;
            display: block;
            width: 0;
            height: 0;
            border-color: transparent;
            border-style: solid;
        }
        .borrow2 {
            position: absolute;
            top: -6px;
            left: 50%;
            margin-right: 3px;
            border-bottom-color: #ebeef5;
            border-top-width: 0;
            border-width: 6px;
            filter: drop-shadow(0 2px 12px rgba(0, 0, 0, .03));
        }

        .borrow2:after {
            bottom: -6px;
            margin-left: -11px;
            border-bottom-color: #0F3D67 !important;
            border-top-width: 0;
            content: " ";
            border-width: 6px;
            position: absolute;
            display: block;
            width: 0;
            height: 0;
            border-color: transparent;
            border-style: solid;
        }

        .lightGy {
            /* animation: myfirst 1.5s infinite;
            box-shadow: 0px -15px 20px #fc780f;
            border-radius: 50%; */
        }
        .animate {
            -webkit-animation: ripple 4s linear infinite alternate;
            animation: ripple 4s linear infinite alternate;
        }
        @keyframes ripple {
            0% {
                -webkit-transform: scale(0.8);
                transform: scale(0.8);
            }
            100% {
                -webkit-transform: scale(1.2);
                transform: scale(1.2);
                background-color: transparent;
            }
        }
        @keyframes shadow {

            0%,
            100% {
                transform: scaleX(1);
            }

            50% {
                transform: scaleX(1.2);
            }
        }

        @keyframes rotate {
            0% {
                transform: translateY(0);
            }

            25% {
                transform: translateY(5px);
            }

            50% {
                transform: translateY(10px) scale(1.1, 0.9);
            }

            75% {
                transform: translateY(5px);
            }

            100% {
                transform: translateY(0);
            }
        }

        @keyframes breath {
            0% {
                width: 32px;
                height: 32px;
                margin-left: 0px;
            }

            25% {
                width: 36px;
                height: 36px;
                margin-left: -2.5px;
            }

            50% {
                width: 40px;
                height: 40px;
                margin-left: -5px;
            }

            75% {
                width: 36px;
                height: 36px;
                margin-left: -2.5px;
            }

            100% {
                width: 32px;
                height: 32px;
                margin-left: 0px;
            }
        }

        @keyframes myfirst {
            10% {
                transform: scale(1)
            }

            ;

            100% {
                transform: scale(8)
            }
        }

        @keyframes fadein {
            0% {
                opacity: 0
            }

            ;

            100% {
                opacity: 1
            }
        }
        @keyframes zoomin {
            0% {
                transform: scale(0)
            }

            ;

            100% {
                transform: scale(1)
            }
        }
    </style>
</head>

<body>
    <div id="allmap"></div>
    <div id="mapAdress"></div>
    <div id="deviceList"></div>
    <script>
        //获取地址栏参数
        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
            var r = window.location.search.substr(1).match(reg); //匹配目标参数
            if (r != null) return unescape(r[2]);
            return null; //返回参数值
        }
        // 绘制图标
        // function createDevide(sysType, list, showcheckList, showType, type, chooseId, status, isZoom) {
        //     var lnglatList = []
        //     if (type === 0) {
        //         for (var i = 0; i < list.length; i++) {
        //             if (isNaN(list[i].longitude) || isNaN(list[i].latitude) || list[i].longitude === '' || list[i].latitude === '' || list[i].pointType === '8' || list[i].pointType === '99' || list[i].longitude === null || list[i].latitude === null) continue
        //             else {
        //                 markerList.push(new AMap.Marker({
        //                     position: createLngLat(list[i].longitude, list[i].latitude),
        //                     offset: new AMap.Pixel(-20, -20),
        //                     zIndex: 0,
        //                     content: AddDevice(createLngLat(list[i].longitude, list[i].latitude), list[i], showType, type, chooseId), // 添加 Icon 实例
        //                 }))
        //                 console.log(document.getElementsByClassName('deviceDetail'), 1111)
        //             }
        //         }
        //     }
        //     if (type === 1 && list !== undefined) {
        //         for (var i = 0; i < list.length; i++) {
        //             //判断是否存在于过滤列表中，如果在则直接过滤掉不显示
        //             if ((showcheckList != undefined && showcheckList[sysType] !== undefined && showcheckList[sysType].indexOf(list[i].deviceId) >= 0) || showType !== 0) {
        //                 if (isNaN(list[i].longitude) || isNaN(list[i].latitude) || list[i].longitude === '' || list[i].latitude === '' || list[i].longitude === null || list[i].latitude === null) continue //|| (list[i].status !== status && status !== -1 && status !== undefined)
        //                 else {
        //                     var lnglat = createLngLat(list[i].longitude, list[i].latitude)
        //                     if (list[i].mapType === -1) { // 百度地图转化(暂时不用转化，效率过低，暂不使用，保留代码)
        //                         lnglatList.push(lnglat)
        //                     } else {
        //                         markerList.push(new AMap.Marker({
        //                             position: lnglat,
        //                             offset: new AMap.Pixel(-20, -20),
        //                             zIndex: 0,
        //                             content: AddDevice(lnglat, list[i], showType, type, chooseId), // 添加 Icon 实例
        //                         }))
        //                     }

        //                 }
        //             }
        //         }
        //     }
        //     if (type === 4) {
        //         var showList = ['2', '3', '4', '5', '7']
        //         for (var i = 0; i < list.length; i++) {
        //             if (isNaN(list[i].longitude) || isNaN(list[i].latitude) || list[i].longitude === '' || list[i].latitude === '' || (list[i].pointType !== -1 && list[i].pointType !== '0' && list[i].pointType !== '99' || list[i].longitude === null || list[i].latitude === null)) continue
        //             else {
        //                 markerList.push(new AMap.Marker({
        //                     position: createLngLat(list[i].longitude, list[i].latitude),
        //                     offset: new AMap.Pixel(-20, -20),
        //                     zIndex: 0,
        //                     content: AddDevice(createLngLat(list[i].longitude, list[i].latitude), list[i], showType, type, chooseId), // 添加 Icon 实例
        //                 }))
        //             }
        //         }
        //     }
        // }

        var markerList = []
        var maskLayer = undefined
        var isShowLoca = 0 // 是否已经展示热力图
        var List = [] // 此集合用于收集坐标
        var markerBackList = [] // 备份用集合，用于无缝刷新所有图标使用
        var rotation = 0 // 地图旋转角度
        var pitch = 0 // 地图俯视角度
        var setPoint = 0;
        var fromView = getUrlParam('from'); //是否来自大屏 1：大屏，2：来自线路图
        var center = window.config.MAP_CENTER;
        var zoom = window.config.MAP_ZOOM
        var zooms = [9, 20]
        var powerMapLayer = null
        // 此处没有用builder所以注释掉，很神奇，如果使用了则会改变zooms的值，很奇葩
        // var buildingLayer = new AMap.Buildings({ zIndex: 130, merge: false, sort: false, zooms: zooms, opacity: 0.8 });
        var imageLayer = new AMap.ImageLayer({
            url: './mapImgs/4.png',
            bounds: new AMap.Bounds(
                [116.318286, 39.885424], [116.3307, 39.889639]
            ),
            zooms: zooms
        }); // 左下角118.864083,26.221192  右上角 118.86963,26.225124
        var options = {
            hideWithoutStyle: false, //是否隐藏设定区域外的楼块
            areas: [{ //A区
                    //visible:false,//是否可见
                    rejectTexture: true, //是否屏蔽自定义地图的纹理
                    color1: '#2450A2', //楼顶颜色
                    color2: '#2450A2', //楼面颜色
                    path: [
                        [115.910507, 39.05612], //左上
                        [115.910473, 39.051999],
                        [115.910529, 39.045605], //左下
                        // [115.918884, 39.0475],
                        // [115.927339, 39.048567],
                        [115.935162, 39.046372], //右下
                        [115.935162, 39.054304],
                        [115.932287, 39.054804],
                        [115.933561, 39.057365],
                        [115.933295, 39.060219],
                        [115.926643, 39.059352],
                        [115.915657, 39.056353]
                    ]
                },
                { //B区
                    //visible:false,//是否可见
                    rejectTexture: true, //是否屏蔽自定义地图的纹理
                    color1: '1187B5', //楼顶颜色
                    color2: '1187B5', //楼面颜色
                    path: [
                        [115.911215, 39.0664],
                        [115.917352, 39.056936],
                        [115.926643, 39.059352], //右下
                        [115.927738, 39.061425],
                        [115.933145, 39.061492],
                        [115.933188, 39.062192],
                        [115.928124, 39.062159],
                        [115.929883, 39.065624],
                        [115.929798, 39.068099],
                        [115.929308, 39.0617],
                        [115.929798, 39.068099],
                        [115.928467, 39.071131]
                    ],
                },
                { //C区
                    //visible:false,//是否可见
                    rejectTexture: true, //是否屏蔽自定义地图的纹理
                    color1: '124582', //楼顶颜色
                    color2: '124582', //楼面颜色
                    path: [
                        [115.911224, 39.07146],
                        [115.91058, 39.07036],
                        [115.910881, 39.069061],
                        [115.912142, 39.068798],
                        [115.911842, 39.068431],
                        [115.912829, 39.06704],
                        [115.913039, 39.066467], //左下
                        [115.922652, 39.069332],
                        [115.928532, 39.070931], //右下
                        [115.927721, 39.073295],
                        [115.927034, 39.074795],
                        [115.920682, 39.074062],
                        [115.914803, 39.072729]
                    ],
                },
                { //D1区
                    //visible:false,//是否可见
                    rejectTexture: true, //是否屏蔽自定义地图的纹理
                    color1: '3FB3C8', //楼顶颜色
                    color2: '3FB3C8', //楼面颜色
                    path: [
                        [115.927163, 39.074761],
                        [115.929132, 39.068466],
                        [115.942054, 39.068299],
                        [115.941989, 39.070715],
                        [115.940359, 39.074429],
                        [115.939089, 39.077228],
                        [115.936561, 39.076627]
                    ]
                },
                { //D2区
                    //visible:false,//是否可见
                    rejectTexture: true, //是否屏蔽自定义地图的纹理
                    color1: '2F5685', //楼顶颜色
                    color2: '2F5685', //楼面颜色
                    path: [
                        [115.939818, 39.075929],
                        [115.941989, 39.070715],
                        [115.942054, 39.068299],
                        [115.949474, 39.067966], //右下

                        [115.950247, 39.070765],
                        [115.950504, 39.071764],
                        [115.948144, 39.074963],

                        [115.958014, 39.07343],
                        [115.957932, 39.075062],
                        [115.946256, 39.076529]
                    ],
                },
                { //E区
                    //visible:false,//是否可见
                    rejectTexture: true, //是否屏蔽自定义地图的纹理
                    color1: '275582', //楼顶颜色
                    color2: '275582', //楼面颜色
                    path: [
                        [115.931257, 39.068456],
                        [115.931986, 39.065557],
                        [115.933317, 39.065357],
                        [115.933274, 39.05776],
                        [115.93145, 39.055144],
                        [115.935162, 39.054304],
                        [115.9419, 39.055894],
                        [115.947135, 39.055994], //右下
                        [115.947178, 39.068489]
                    ],
                },
                { //F区
                    //visible:false,//是否可见
                    rejectTexture: true, //是否屏蔽自定义地图的纹理
                    color1: '0F6B93', //楼顶颜色
                    color2: '0F6B93', //楼面颜色
                    path: [
                        [115.935216, 39.05536],
                        [115.93528, 39.045812],
                        [115.947801, 39.045562],
                        [115.955525, 39.047862],
                        [115.955036, 39.058669],
                        [115.955321, 39.05851],
                        [115.952317, 39.056543],
                        [115.947833, 39.05731],
                        [115.941825, 39.057143]
                    ],
                },
                { //G区
                    //visible:false,//是否可见
                    rejectTexture: true, //是否屏蔽自定义地图的纹理
                    color1: '325277', //楼顶颜色
                    color2: '325277', //楼面颜色
                    path: [
                        [115.9472, 39.068506],
                        [115.947221, 39.05721],
                        [115.951556, 39.056627],
                        [115.953916, 39.058443],
                        [115.955036, 39.058669],
                        [115.961688, 39.058736],
                        [115.960314, 39.066316]
                    ],
                },
                { //辅助配套区
                    //visible:false,//是否可见
                    rejectTexture: true, //是否屏蔽自定义地图的纹理
                    color1: 'ffDFEFFE', //楼顶颜色
                    color2: 'ffAEC4D9', //楼面颜色
                    path: [
                        [115.888457, 39.060031], //左上
                        [115.888286, 39.041634], //左下
                        [115.910258, 39.046334], //右下
                        [115.910486, 39.05617],
                        [115.916751, 39.056837],
                        // [115.912576, 39.066528],
                        [115.910958, 39.068299]
                    ],
                },
                { //隧道区
                    //visible:false,//是否可见
                    rejectTexture: true, //是否屏蔽自定义地图的纹理
                    color1: 'ffDFEFFE', //楼顶颜色
                    color2: 'ffAEC4D9', //楼面颜色
                    path: [
                        [115.918199,39.056453], //左上
                        [115.93512,39.068131], //左下
                        [115.947068,39.059841], //右下
                        [115.93512,39.051202],
                    ],
                }
            ]
        };
        // buildingLayer.setStyle(options); //此配色优先级高于自定义mapStyle
        var map = new AMap.Map('allmap', {
            resizeEnable: true,
            center: center,
            zooms: zooms,
            viewMode: '2D', // 地图3D模式或者2D模式
            rotateEnable: false, // 是否允许旋转地图
            rotation: rotation,
            expandZoomRange: true, // 设置扩大缩放范围，最多20级
            animateEnable: false,
            // showBuildingBlock: false, // 设置地图显示3D楼块效果，移动端也可使用。推荐使用。
            pitchEnable: false, // 是否允许设置俯仰角度，3D视图下为true，2D视图下无效。
            pitch: pitch,
            showIndoorMap: false,
            // showLabel:false,
            features: ['bg', 'point', 'road'],
            // mapStyle: 'amap://styles/blue',
            mapStyle: 'amap://styles/c224b58f70acc9cb998bb0207014f03d', //设置地图的显示样式 
            layers: [
                // buildingLayer,
                // imageLayer
            ]
            // 以下是3D的设置
            // resizeEnable: true,
            // center: [center.longitude, center.latitude],
            // zooms: zooms,
            // viewMode: '3D', // 地图3D模式或者2D模式
            // rotateEnable: true, // 是否允许旋转地图
            // rotation: rotation,
            // expandZoomRange: true, // 设置扩大缩放范围，最多20级
            // showBuildingBlock: true, // 设置地图显示3D楼块效果，移动端也可使用。推荐使用。
            // pitchEnable: true, // 是否允许设置俯仰角度，3D视图下为true，2D视图下无效。
            // pitch: pitch,
            // showIndoorMap: false,
            // // showLabel:false,
            // features: ['bg', 'point', 'road'],
            // mapStyle: 'amap://styles/ffdd7999247c33473a8c5d6870f835b2', //设置地图的显示样式
            // layers: [
            //     // buildingLayer,
            //     // imageLayer
            // ]
        });
        //  鹰眼
        // map.plugin(['AMap.HawkEye'], function() {
        //     // 在图面添加鹰眼控件，在地图右下角显示地图的缩略图
        //     map.addControl(new AMap.HawkEye({ isOpen: true, mapStyle: 'amap://styles/ffdd7999247c33473a8c5d6870f835b2', }));
        // })
        // 聚合start
        var count = 0;
        var cluster = null
        var showBox = true // 是否显示弹框
        var startShowBox = false // 是否开始显示弹窗
        var time = null
        var boxIndex = 0
        var boxList = []
        var data = {}
        var clusterList = []
         // 聚合点配色
        var defaultColor = [
            '204,235,197',
            '168,221,181',
            '123,204,196',
            '78,179,211',
            '43,140,190',
        ]
        var _renderClusterMarker = function(context) {
            // 聚合中点个数
            var clusterCount = context.count;
            var div = document.createElement('div');
            // div.classList.add('animate')
            if (clusterCount >= 0 && clusterCount < 10) {
                bgColor = defaultColor[0];
            } else if (clusterCount >= 10 && clusterCount < 100) {
                bgColor = defaultColor[1];
            } else if (clusterCount >= 100 && clusterCount < 1000) {
                bgColor = defaultColor[2];
            } else if (clusterCount >= 1000 && clusterCount < 10000) {
                bgColor = defaultColor[3];
            } else if (clusterCount >= 10000) {
                bgColor = defaultColor[4];
            }
            div.id = context.clusterData[0].deviceId
            // div.style.backgroundColor = 'rgba(' + bgColor + ',.5)';
            div.style.background = `radial-gradient(50% 50% at 50% 50%, rgba(1, 255, 255, 0) 0%, rgba(1, 255, 255, 0.8) 100%)`;
            var size = Math.round(25 + Math.pow(clusterCount / count, 1 / 5) * 10);
            div.innerHTML = clusterCount;
            div.style.lineHeight = size + 'px';
            div.style.color = '#ffffff';
            div.style.fontSize = '12px';
            div.style.textAlign = 'center';
            div.style.borderRadius = '50%';
            div.style.padding = '10px';
            div.style.width = '18px';
            div.style.height = '18px';
            // context.marker.setOffset(new AMap.Pixel(-size / 2, -size / 2));
            context.marker.setContent(div);
            div.addEventListener('mouseover', evt => {
                const deviceId = context.marker.dom.children[0].id
                const curCluster = cluster._.find(ele=> ele.p.deviceId == deviceId)
                let markers = []
                curCluster.M.forEach(element => {
                    markers = markers.concat(element)
                });
                context.markers = markers
                initBox([context])
            })
            div.addEventListener('mouseout', evt => {
                map.remove(boxList)
            })
        };
        var _renderMarker = function(context) {
            // console.log('非聚合数据', context)
            var clusterData = context.data[0]
            var dataList = data.dataList
            var showcheckList = data.showcheckList
            var showType = data.showType
            var chooseId = data.chooseId
            var status = data.status
            var isZoom = data.isZoom
            var type = 1 // 默认显示设备
            var offset = new AMap.Pixel(-20, -20);
            context.marker.setContent(CreateClusterDevice(data.sysType, clusterData, showcheckList, showType, type, chooseId, status, isZoom))
            context.marker.setOffset(offset)
            // context.marker = CreateClusterDevice(data.sysType, clusterData, showcheckList, showType, type, chooseId, status)
        }
        // 聚合end

        // 展示聚合设备
        function CreateClusterDevice(sysType, data, showcheckList, showType, type, chooseId, status, isZoom) {
            //判断是否存在于过滤列表中，如果在则直接过滤掉不显示
            if ((showcheckList != undefined && showcheckList[sysType] !== undefined && showcheckList[sysType].indexOf(data.deviceId) >= 0) || showType !== 0) {
                if ((isNaN(data.longitude) || isNaN(data.latitude) || data.longitude === '' || data.latitude === '' || data.longitude === null || data.latitude === null) || (data.status !== status && status !== -1 && status !== undefined)) {} else {
                    // var marker = new AMap.Marker({
                    //     position: new AMap.LngLat(data.longitude, data.latitude),
                    //     offset: new AMap.Pixel(-20, -40),
                    //     zIndex: 0,
                    //     content: AddDevice(new AMap.LngLat(data.longitude, data.latitude), data, showType, type, chooseId), // 添加 Icon 实例
                    // })
                    // markerList.push(marker)
                    // return marker                    
                    return AddDevice(createLngLat(data.longitude, data.latitude), data, showType, type, chooseId) // 添加 Icon 实例
                }
            }
        }

        // 添加比例尺控件
        // map.addControl(new AMap.Scale())
        function AddNameDetail(point, data) {
            var div = this._div = document.createElement("div");
            div.style.position = "absolute";
            div.id = "nameBox";
            div.style.width = "100px";
            div.style.height = "40px";
            div.style.display = "flex";
            div.style.flexDirection = "column";
            div.style.alignItems = "center";
            div.style.color = "#F7FF00"
            div.style.fontSize = "0.8vw"
            div.innerHTML = "<span>" + data.name + "</span>"
            return div;
        }
        //不同区域显示不同底图颜色
        var curArea = { text: null, content: null }
        var curRoad = null
        function addArea(area) {
            curArea.text && map.remove(curArea.text)
            curArea.content && map.remove(curArea.content)
            area.markerCenterPoint && AddAreaName(area) //目前显示区域只需要显示一个

            curArea.content = new AMap.Polygon({
                bubble: true,
                fillColor: '#3FB3C8',
                fillOpacity: 0.65,
                strokeWeight: 2,
                strokeOpacity: 0,
                strokeStyle: 'solid',
                strokeColor: '#FF86FF',
                path: JSON.parse(area.point).map(ele => {
                    return new this.AMap.LngLat(...ele)
                }),
                map: map
            })
            map.add(curArea.content)
            map.setFitView(curArea.content)
        }
        map.setZoom(zoom)
        // 限制地图滚动范围
        var bounds = map.getBounds();
        // var bounds2 = new AMap.ArrayBounds([new AMap.LngLat(115.894199, 39.056476), new AMap.LngLat(115.967498, 39.076885)])
        // map.setLimitBounds(bounds2);

        // 更新地图
        function UpdateMap() {
            // console.log('执行UpdateMap')
            map.remove(markerList)
            markerList = []
            // markerBackList = [].concat(markerList);
        }

        // 调用父页面方法(传值回去)
        function aclick(longitude, latitude) {
            // 高程默认是0
            let data = { type: "getPoint", longitude: longitude, latitude: latitude, alt: 0 }
            let targetOrigin = "*"
            window.parent.postMessage(data, targetOrigin)
        }

        // 添加区域名称
        function AddAreaName(data) {
            var div = this._div = document.createElement("div");
            div.style.position = "absolute";
            div.id = "nameBox";
            div.style.width = "100px";
            div.style.height = "40px";
            div.style.display = "flex";
            div.style.flexDirection = "column";
            div.style.alignItems = "center";
            div.style.color = "#F7FF00"
            div.style.fontSize = "0.8vw"
            div.innerHTML = "<span>" + (data.markerName || data.deviceName) + "</span>"
            curArea.text = new AMap.Marker({
                position: new AMap.LngLat(...JSON.parse(data.markerCenterPoint)),
                offset: new AMap.Pixel(-40, 0),
                zIndex: 0,
                content: div, // 添加 Icon 实例
            })
            map.add(curArea.text)
        }
        function getIconName(deviceData) {
            // 灯亮度图标
            if (deviceData.deviceType === '6' && deviceData.onoff === 1 && deviceData.alarmState === 0 && deviceData.onlineState === 1) {
                if (deviceData.bir >= 0 && deviceData.bir < 20) {
                    return 'img1'
                } else if (deviceData.bir >= 20 && deviceData.bir < 40) {
                    return 'img2'
                } else if (deviceData.bir >= 40 && deviceData.bir < 60) {
                    return 'img3'
                } else if (deviceData.bir >= 60 && deviceData.bir < 80) {
                    return 'img4'
                } else if (deviceData.bir >= 80 && deviceData.bir <= 100) {
                    return 'img5'
                }
            }
            const deviceState = deviceData.onlineState === 0 ? 1 : deviceData.alarmState === 1 ? 2 : 0
            return deviceData.deviceType + "_" + deviceState
        }

        // 添加设备marker
        function AddDevice(point, deviceData, showType, type, chooseId, inMin) { // type 0表示公共设施 ， 1 表示接入的设备, 2表示老人，3表示居家老人照料服务中心,4党建界面单独显示设施
            var div = this._div = document.createElement("div");
            div.id = "deviceBox_" + deviceData.deviceId
            div.style.position = "absolute";
            if (chooseId === deviceData.deviceId) {
                div.style.background = "url(./img/border.png) round"
            }
            // div.style.width = "60px";
            // div.style.height = "60px";
            div.style.display = "flex";
            div.style.flexDirection = "column";
            div.style.alignItems = "center";
            var iconUrl = 'dingwei';
            //添加详情框
            var detailDiv = document.createElement("div");
            var htmlStr = '';
            var content = '';
            var className = 'lightGy';
            // var imgWidth = "60px"
            // var pixel = map.lngLatToContainer(point);
            //如果是主页，则显示详情
            if (fromView == 1) {
                if (type === 1) { // 显示各种设备
                    iconUrl = getIconName(deviceData);
                    htmlStr += '<div id="deviceDetail_' + deviceData.deviceId + `" style="display:${showDeviceName === 1 && !inMin ? 'block' : 'none'}; transform: translateX(-50%)" class="deviceDetail">`;
                    htmlStr += '<div style="border-bottom: 1px solid #f3e47124; margin-bottom: 2%"><span style="font-weight:bolder;font-size: 2vh;color:#03EDFB;">' + deviceData.deviceName + '</span></div>';
                    if (deviceData.onlineState === 1 && deviceData.alarmState !== 1) {
                        htmlStr += '<div style="font-size: 1.8vh">状态：<span style="color:#09E42B;">在线</span></div>';
                    } else if (deviceData.onlineState == 0) {
                        htmlStr += '<div style="font-size: 1.8vh">状态：<span style="color:#B1B1B1;">离线</span></div>';
                    } else if (deviceData.onlineState == 1 && deviceData.alarmState === 1) {
                        htmlStr += '<div style="font-size: 1.8vh">状态：<span style="color:#FF0000;">告警</span></div>';
                    }
                    htmlStr += '<div x-arrow="" class="borrow" ></div>';
                    htmlStr += '<div x-arrow="" class="borrow2" style="display: none" ></div></div>';
                    detailDiv.innerHTML = htmlStr;
                }
            }
            // 增加图标及其动画特效
            // console.log('图标', iconUrl, deviceData.deviceId)
            const url = `./img/deviceIcon/${iconUrl}.png`
            div.innerHTML = `<div style='height: 40px;width: 40px;border-radius: 50%;background: url("${url}");background-size: ${iconUrl.indexOf('_') !== -1 ? '40px 40px;' : '60px 60px;background-position: -10px -10px;'}background-repeat: no-repeat' class='" + className + "'  ></div>`;
            // div.innerHTML = "<img src='./img/deviceIcon/" + iconUrl + ".png' style='height: 40px;width: 40px' class='" + className + "'  />";
            // div.innerHTML = "<img style='width:19px;height:32px;'  src='//webapi.amap.com/theme/v1.3/markers/b/mark_bs.png'  />";
            //点击事件
            div.onmousedown = function(e) {
                if(e.button === 0) {
                    if (fromView == 1 && type === 1) {
                        //获取像素位置
                        // deviceData.pixel = pixel;
                        // console.log('设备数据')
                        // console.log(deviceData)
                        window.parent.postMessage({ type: 'getDevicePoint', data: deviceData }, '*')
                    }
                }
                else if(e.button === 2) {
                    if(deviceData.deviceType === '587') {
                        window.parent.postMessage({ type: 'getPowerMap', data: deviceData }, '*')
                    }
                }
            }
            // 悬浮事件
            div.onmouseover = function(e) {
                var dom = document.getElementById('deviceDetail_' + deviceData.deviceId);
                const inMin = dom.parentNode.parentNode.getAttribute('inMin');
                if (fromView == 1 && type === 1 && !showDeviceName || inMin) {
                    dom.style.display = 'block';

                    var width = dom.offsetWidth;
                    // dom.style.left = -width / 3 + "px";
                    if (dom.parentNode.parentNode.parentNode.parentNode.offsetTop <= dom.offsetHeight + 20) {
                        dom.style.bottom = 'unset';
                        dom.style.top = '45px';
                        dom.getElementsByClassName('borrow2')[0].style.display = "block";
                        dom.getElementsByClassName('borrow')[0].style.display = "none";
                    } else {
                        dom.style.bottom = '45px';
                        dom.style.top = 'unset';
                        dom.getElementsByClassName('borrow')[0].style.display = "block";
                        dom.getElementsByClassName('borrow2')[0].style.display = "none";
                    }
                    dom.parentNode.parentNode.parentNode.parentNode.style.zIndex = 100;
                }
            }
            // 下悬浮移除事件
            div.onmouseout = function() {
                var dom = document.getElementById('deviceDetail_' + deviceData.deviceId);
                const inMin = dom.parentNode.parentNode.getAttribute('inMin');
                if (fromView == 1 && type === 1 && !showDeviceName || inMin) {
                    var dom = document.getElementById('deviceDetail_' + deviceData.deviceId);
                    dom.style.display = 'none';
                    dom.parentNode.parentNode.parentNode.parentNode.style.zIndex = 0;
                }
            }
            div.appendChild(detailDiv)
            return div;
        }

        function initBox(clusterList) {
           // console.log('清理数据', boxList, boxIndex, clusterList[boxIndex])
            map.remove(boxList)
            // boxList = []
            if (showBox === true && clusterList.length > 0 && clusterList[0].markers !== undefined && clusterList[0].markers.length > 0) {
                var deviceBox = AddBox(clusterList[0])
                boxList = [
                    new AMap.Marker({
                        position: new AMap.LngLat(...clusterList[boxIndex].marker._position),
                        zIndex: 0,
                        content: deviceBox, // 添加 Icon 实例
                    })
                ]
                // console.log('弹框', clusterList, boxIndex)
                map.add(boxList)
                deviceBox = null
            }
        }
        // 添加弹框
        function AddBox(data) {
            var div = this._div = document.createElement("div");
            div.id = "BoxList_" + data.markers[0].deviceId
            div.style.width = "0px";
            div.style.height = "0px";
            div.style.left = "17px";
            div.style.position = "relative";
            // div.style.display = "flex";
            // div.style.flexDirection = "column";
            // div.style.alignItems = "center";
            normal = 0
            alarm = 0
            exit = 0
            if (data.markers.length > 0) {
                data.markers.forEach(item => {
                    if (item.onlineState === 1) {
                        normal++
                        if (item.alarmState === 1) {
                            alarm++
                        }
                    } else if (item.onlineState === 0) {
                        exit++
                    }
                })
            }
            //添加详情框
            var htmlStr = '';
            htmlStr += `<div id="pointNum_${data.markers[0].deviceId}" class="boxDetail" style="position: absolute; left: 0; transform: translateX(-50%) translateY(40px)">`;
            htmlStr += '<div style="color: #80FFFF;">在线：' + normal + '个</div><div style="color: #FFFFFF;">离线：' + exit + '个</div><div style="color: #FF451E;">告警：' + alarm + '个</div>'
            htmlStr += '<div x-arrow="" class="borrow3" ></div>';
            // htmlStr += '<div x-arrow="" class="borrow2" ></div></div>';
            div.innerHTML = htmlStr;
            //点击事件
            div.onclick = function() {
                if (data.markers.length > 1) {
                    if (map.getZoom() == zooms[1]) {
                        showMiniCluster(cluster)
                    } else {
                        window.parent.postMessage({ type: 'showClusterData', data: data.markers }, '*')
                    }
                }
                // window.parent.postMessage({ type: 'showClusterData', data: data.dataList }, '*')
            }
            return div
        }
        // 处理传递过来的设备类型编号，根据编号查询对应的数据列表并展示
        function ShowDevices(sysType, dataList, showcheckList, showType, chooseId, status, deviceData, isZoom) {
            // console.log('数据源：')
            // console.log(dataList[sysType])
            // console.log(showcheckList)
            var list = [];
            list = dataList[sysType];
            // console.log('设备传参', sysType, dataList, showcheckList, showType, chooseId, status, isZoom)
            list.forEach(item => {
                item.lnglat = [...createLngLat(item.longitude, item.latitude, 1)]
            })
            var count = list.length;
            if (cluster !== null) {
                cluster.setData([])
                cluster.vI = {}
                cluster = null
            }
            map.plugin(["AMap.MarkerCluster"], function() {
                // console.log('生成数据', cluster)
                cluster = new AMap.MarkerCluster(map, list, {
                    gridSize: 50, // 聚合网格像素大小
                    maxZoom: 99,
                    renderClusterMarker: _renderClusterMarker, // 自定义聚合点样式
                    renderMarker: _renderMarker, // 自定义非聚合点样式
                })
                clusterList = []
                if (time !== null) {
                    clearInterval(time)
                    time = null
                }
                map.remove(boxList)
                boxList = []
                boxIndex = 0
                // 根据当前聚合前五条展示弹框（轮播）
                if (cluster._ !== undefined && cluster._.length > 0) {
                    var arr = cluster._
                    for(var i=0;i<arr.length-1;i++){
                        for(var j=0;j<arr.length-i-1;j++){
                            var firstList = arr[j].M
                            var secondList = arr[j+1].M
                            var dataListFirst = []
                            var dataListSecond = []
                            var first = 0
                            var second = 0
                            firstList.forEach(item => {
                                first += item.length
                                item.forEach(ele => {
                                    dataListFirst.push(ele)
                                })
                            })
                            secondList.forEach(item => {
                                second += item.length
                                item.forEach(ele => {
                                    dataListSecond.push(ele)
                                })
                            })
                            arr[j].dataList = dataListFirst
                            arr[j+1].dataList = dataListSecond
                            if(first < second){// 相邻元素两两对比
                                var hand = arr[j]
                                arr[j]=arr[j+1]
                                arr[j+1]=hand
                            }
                        }
                    }
                    clusterList = arr
                }
                // // 这句是重点，当点击的时候
                cluster.on("click", (cluster) => {
                    if (cluster.clusterData.length > 1) {
                        if (map.getZoom() == zooms[1]) {
                            showMiniCluster(cluster)
                        } else {
                            window.parent.postMessage({ type: 'showClusterData', data: cluster.clusterData }, '*')
                        }
                    }
                })
            })
        }
        // 手动展开聚合球，用来解决设备位置重合问题
        function showMiniCluster (cluster) {
            function translateXY(index) {
                let a = 2 * Math.PI / cluster.clusterData.length
                let x = Math.sin( (index - 1) * a ) * (cluster.clusterData.length + 2) * 4
                let y = Math.cos( (index - 1) * a ) * (cluster.clusterData.length + 2) * 4
                return {x, y}
            }

            maskLayer = document.createElement('div')
            maskLayer.style = `position:fixed;top:0;left:0;width:100%;height:100%;background-color:#00000088;animation:fadein .3s`
            maskLayer.onclick = function() {this.remove()}
            maskLayer.innerHTML = `<div style="position:absolute;top:${cluster.marker._style.top};left:${cluster.marker._style.left};animation:zoomin .3s"></div>`
            cluster.clusterData.forEach((item, index )=> {
                let dom = AddDevice(null, item, -1, 1, -1, true) // 参数可能要改
                dom.style = `cursor:pointer;position:absolute;transform:translate(${translateXY(index).x}px,${translateXY(index).y}px)`
                dom.setAttribute('inMin', true)
                maskLayer.firstElementChild.appendChild(dom)
            })
            document.body.append(maskLayer)
        }
        // 清理集合
        function clearBoxList () {
            if (time !== null) {
                clearInterval(time)
            }
            time = null
            map.remove(boxList)
            boxList = []
            boxIndex = 0
            showBox = false
            startShowBox = false
        }
        
        function getWeather() {
            AMap.plugin('AMap.Weather', function() {
                var weather = new AMap.Weather()
                weather.getLive('130629', function(err, data) {
                    if(!err) {
                        window.parent.postMessage({ type: 'weatherInfo', data }, '*')
                    }
                })
            })
        }
        
        //单击获取点击的经纬度
        map.on('click', function(e) {
            //点击地图，因此设备搜索控件
            window.parent.postMessage({ type: 'hiddenSearchDevice' }, '*')
            if (fromView === 1) {
                List.push([e.lnglat.getLng(), e.lnglat.getLat()])
            }
            if (setPoint == 1 || fromView == 'test') {
                // 清除其余所有的图标
                UpdateMap()
                // 在指定经纬度添加图标
                markerList.push(new AMap.Marker({
                    position: new AMap.LngLat(e.lnglat.getLng(), e.lnglat.getLat()),
                    offset: new AMap.Pixel(-20, -20),
                    zIndex: 0,
                    content: AddDevice(new AMap.LngLat(e.lnglat.getLng(), e.lnglat.getLat()), null, -1, 0), // 添加 Icon 实例
                }))
                map.add(markerList)
                aclick(e.lnglat.getLng(), e.lnglat.getLat());
            }
            clearBoxList()
        });
        // 缩放事件
        map.on('zoomstart', function(e) {
            if (startShowBox === true) {
                clearBoxList()
            }
        })
        map.on('mousemove', function(e) {
            if (fromView == 0) {
                document.getElementById("mapAdress").innerHTML = e.lnglat.getLng() + "," + e.lnglat.getLat();
                document.getElementById("mapAdress").style.left = e.pixel.getX() + 5 + "px";
                document.getElementById("mapAdress").style.top = e.pixel.getY() + 5 + "px";
                document.getElementById("mapAdress").style.display = "block";
            }
        });
        map.on('mouseout', function(e) {
            if (fromView == 0) {
                document.getElementById("mapAdress").style.display = "none";
            }
        });
        //加载完毕之后返回给主界面告知加载完成
        map.on('complete', function(e) {
            //告知主界面已经加载完毕，可以进行操作
            window.parent.postMessage({ type: 'loadOver' }, '*')
            getWeather()
        });
        map.on('mapmove', function(e) {
            // 删除遮罩层
            maskLayer?.remove()
            maskLayer = undefined
        });

        // 监听事件
        var showDeviceName = null
        window.addEventListener('message', function(e) {
            var type = e.data.type
            // console.log(type)
            // 修改数据还原记录坐标点事件
            if (type === 'editPoint') {
                UpdateMap()
                markerList.push(new AMap.Marker({
                    position: createLngLat(e.data.longitude, e.data.latitude),
                    offset: new AMap.Pixel(-20, -20),
                    zIndex: 0,
                    content: AddDevice(createLngLat(e.data.longitude, e.data.latitude), null, -1, 0), // 添加 Icon 实例
                }))
                map.add(markerList)
                if (e.data.longitude === 0 && e.data.latitude === 0) {
                    map.panTo(center);
                } else {
                    map.panTo(createLngLat(e.data.longitude, e.data.latitude));
                }
            }
            if (type === 'reload') {
                // 初始化地图配置，清理所有图标，将地图重新放置于中心点
                UpdateMap()
                map.panTo(center);
                map.setZoom(zoom)
                setPoint = 0;
            }
            if (type === 'setPoint') {
                setPoint = 1;
            }
            if (type === 'setShowDeviceName') {
                showDeviceName = e.data.data;
            }
            if (type === 'fromView') {
                fromView = 1;
                // 清空上一次生成图标
                UpdateMap()
                //根据传回的设备类型进行渲染
                data = e.data.data
                var dataList = e.data.dataList
                var showcheckList = e.data.showcheckList
                var showType = e.data.showType
                var pointList = e.data.pointList
                var chooseId = e.data.chooseId
                var status = e.data.status
                var isZoom = e.data.isZoom
                if (e.data.isBack === -1) {
                    map.panTo(center);
                    map.setZoom(zoom)
                }
                // 根据类型进行展示图层
                // console.log('展示图层')
                // console.log(data)
                // console.log(e.data)
                markerList = []; // 清空所有的图标集合
                if (showType === 0) { // 大屏设备展示
                    var hasType = false
                    for (var i = 0; i < data.length; i++) {
                        ShowDevices(data[i].sysType, dataList, showcheckList, showType, chooseId, status, isZoom);
                        if (hasType !== true) {
                            hasType = data[i].sysType === 999
                        }
                        if (isZoom) {
                            map.setFitView()
                        }
                    }

                    if (hasType) {
                        // console.log(e.data.pointList)
                        // 当不勾选公共设施图层的时候才显示
                        pointList.push({ longitude: center[0], latitude: center[1], pointType: -1 })
                        ShowDevices(null, pointList, showcheckList, showType, chooseId, status, isZoom);
                    }
                }
            }
            if (type === 'backHome') {
                map.panTo(center);
                map.setZoom(zoom)
                map.setPitch(pitch)
                map.setRotation(rotation)
            }
            if (type === 'selectDevice') {
                // console.log(e.data.data)
                // console.log(e.data.lastChooseId)
                // map.setZoom(zooms[0])
                console.log('选中设备', e.data.data)
                map.panTo(createLngLat(e.data.data.longitude, e.data.data.latitude));
                var lastDiv = document.getElementById('deviceBox_' + e.data.lastChooseId)
                if (lastDiv !== null && lastDiv !== undefined) {
                    lastDiv.style.background = ""
                }
                if(map.getZoom !== 1) { 
                    map.setZoom(zooms[1]) 
                }
                setTimeout(function() {
                    var div = document.getElementById('deviceBox_' + e.data.data.deviceId)
                    if (div !== null && div !== undefined) {
                        div.style.background = "url(./img/border.png) round"
                    } else {
                        // 聚合情况下，如果设备被聚合，则自动展开
                        let clusterItem = cluster.V.find(item => (
                            item._amapMarker.originData[0].findIndex(i => (i.deviceId == e.data.data.deviceId)) != -1
                        ))
                        if(clusterItem) {
                            showMiniCluster({
                                clusterData: clusterItem._amapMarker.originData[0],
                                marker: {_style:{
                                    top: clusterItem._amapMarker.posContainer.y + 'px',
                                    left: clusterItem._amapMarker.posContainer.x + 'px'
                                }}
                            })
                            setTimeout(() => {
                                div = document.getElementById('deviceBox_' + e.data.data.deviceId)
                                if (div !== null && div !== undefined) {
                                    div.style.background = "url(./img/border.png) round"
                                }
                            }, 500);
                        }
                    }
                }, 1000)
            }
            if (type === 'resetChoose') {
                var lastDiv = document.getElementById('deviceBox_' + e.data.chooseId)
                if (lastDiv !== null && lastDiv !== undefined) {
                    lastDiv.style.background = ""
                }
            }
            if (type === 'closeMap') {
                map.destroy();
            }
            if (type === 'setAreaColor') {
                addArea(e.data.area)
            }
            if (type === 'clearMap') {
                map.clearMap()
                curArea = { text: null, content: null }
                curRoad = null
            }
            //道路选中
            if (type === 'setPolyline') {
                if (e.data.data.point != null) {
                    curRoad && map.remove(curRoad)
                    var pathInfo = JSON.parse(e.data.data.point)
                    // var pathInfo = [[113.222194,23.083962],[113.222205,23.08379],[113.221878,23.08227]]
                    curRoad = new AMap.Polyline({
                        map: map, //指定目标地图
                        path: pathInfo, //折线的节点坐标数组
                        showDir: true, //是否延路径显示白色方向箭头,默认false(Canvas绘制时有效,建议折线宽度大于6时使用)
                        strokeColor: "#F97A16", //线颜色
                        strokeOpacity: 1, //线透明度
                        strokeWeight: 6, //线宽
                        // strokeStyle: "solid"  //线样式
                    });
                    map.setFitView(curRoad)
                }
            }
            // 供电地图
            if (type === 'showPowerMap') {
                powerMapLayer && map.remove(powerMapLayer)
                if(e.data.data == null || e.data.data.length  === 0) return
                let lines = e.data.data.map(i => {
                    let path = JSON.parse(i.pathPoint)
                    let line = new AMap.Polyline({
                        path, //折线的节点坐标数组
                        strokeColor: i.markerColour || "#ebca26", //线颜色
                        strokeOpacity: 1, //线透明度
                        strokeWeight: 2, //线宽
                        cursor: 'pointer',
                    });
                    line.on('click', e=>{
                        let text = new AMap.Text({
                            map,
                            text: i.lineName,
                            position: e.lnglat
                        })
                        setTimeout(()=>{text.remove()}, 3000)
                    })
                    return line
                })
                powerMapLayer = new AMap.OverlayGroup(lines)
                map.add(powerMapLayer)
                map.setFitView(lines)
            }
        })

        function createLngLat(a, b, type) {
            return type? wgs84togcj02(a, b) : new AMap.LngLat(...wgs84togcj02(a, b))
        }
        // 坐标转换
        const PI = 3.1415926535897932384626
        const a = 6378245.0
        const ee = 0.00669342162296594323

        /**
             * WGS84转GCj02
             * @param lng
             * @param lat
             * @returns {*[]}
             */
        function wgs84togcj02(lng, lat) {
        var wgs_lat = +lat
        var wgs_lng = +lng
        if (out_of_china(lng, wgs_lat)) {
            return [wgs_lng, wgs_lat]
        } else {
            var dlat = transformlat(wgs_lng - 105.0, wgs_lat - 35.0)
            var dlng = transformlng(wgs_lng - 105.0, wgs_lat - 35.0)
            var radlat = wgs_lat / 180.0 * PI
            var magic = Math.sin(radlat)
            magic = 1 - ee * magic * magic
            var sqrtmagic = Math.sqrt(magic)
            dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI)
            dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI)
            var mglat = wgs_lat + dlat
            var mglng = wgs_lng + dlng
            return [mglng, mglat]
        }
        }

        /**
             * GCJ02 转换为 WGS84
             * @param lng
             * @param lat
             * @returns {*[]}
             */
        function gcj02towgs84(lng, lat) {
        var gcj_lat = +lat
        var gcj_lng = +lng
        if (out_of_china(gcj_lng, gcj_lat)) {
            return [gcj_lng, gcj_lat]
        } else {
            var dlat = transformlat(gcj_lng - 105.0, gcj_lat - 35.0)
            var dlng = transformlng(gcj_lng - 105.0, gcj_lat - 35.0)
            var radlat = gcj_lat / 180.0 * PI
            var magic = Math.sin(radlat)
            magic = 1 - ee * magic * magic
            var sqrtmagic = Math.sqrt(magic)
            dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI)
            dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI)
            var mglat = gcj_lat + dlat
            var mglng = gcj_lng + dlng
            return [gcj_lng * 2 - mglng, gcj_lat * 2 - mglat]
        }
        }

        function transformlat(lng, lat) {
        lat = +lat
        lng = +lng
        var ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng))
        ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0
        ret += (20.0 * Math.sin(lat * PI) + 40.0 * Math.sin(lat / 3.0 * PI)) * 2.0 / 3.0
        ret += (160.0 * Math.sin(lat / 12.0 * PI) + 320 * Math.sin(lat * PI / 30.0)) * 2.0 / 3.0
        return ret
        }

        function transformlng(lng, lat) {
        lat = +lat
        lng = +lng
        var ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng))
        ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0
        ret += (20.0 * Math.sin(lng * PI) + 40.0 * Math.sin(lng / 3.0 * PI)) * 2.0 / 3.0
        ret += (150.0 * Math.sin(lng / 12.0 * PI) + 300.0 * Math.sin(lng / 30.0 * PI)) * 2.0 / 3.0
        return ret
        }

        /**
             * 判断是否在国内，不在国内则不做偏移
             * @param lng
             * @param lat
             * @returns {boolean}
             */
        function out_of_china(lng1, lat1) {
        var lat = +lat1
        var lng = +lng1
        // 纬度3.86~53.55,经度73.66~135.05
        return !(lng > 73.66 && lng < 135.05 && lat > 3.86 && lat < 53.55)
        }
    </script>
</body>

</html>