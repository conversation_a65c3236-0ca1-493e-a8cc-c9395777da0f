import request from '@/utils/request'
export function fetchList(data) {
  return request({
    url: '/pmslinkage/api/linkageResult/list',
    method: 'post',
    data
  })
}

export function fetchDetail(queryData) {
  return request({
    url: '/pmslinkage/api/linkageResult/detail',
    method: 'get',
    params: queryData
  })
}

export function fetchDelete(linkageIds) {
  var data = { linkageIds }
  return request({
    url: '/pmslinkage/api/linkageResult/delete',
    method: 'post',
    data
  })
}

export function getLinkageResultCount(data) {
  return request({
    url: '/pmslinkage/api/linkageResult/getLinkageResultCount',
    method: 'post',
    data
  })
}

export function fetchUpdate(data) {
  return request({
    url: '/pmslinkage/api/linkageConfig/update',
    method: 'post',
    data
  })
}

export function fetchUpdateIsEnable(data) {
  return request({
    url: '/pmslinkage/api/linkageResult/updateIsEnable',
    method: 'post',
    data
  })
}
