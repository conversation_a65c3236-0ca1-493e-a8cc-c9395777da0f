import request from '@/utils/request'
export function fetchList(data) {
  return request({
    url: '/pmsService/api/buldLinkFloor/list',
    method: 'post',
    data
  })
}

export function fetchDetail(data) {
  return request({
    url: '/pmsService/api/buldLinkFloor/info',
    method: 'post',
    data
  })
}

export function fetchDelete(delList) {
  var data = { delList }
  return request({
    url: '/pmsService/api/buldLinkFloor/delete',
    method: 'post',
    data
  })
}

export function fetchCreate(data) {
  return request({
    url: '/pmsService/api/buldLinkFloor/insert',
    method: 'post',
    data
  })
}

export function fetchUpdate(data) {
  return request({
    url: '/pmsService/api/buldLinkFloor/update',
    method: 'post',
    data
  })
}

export function fetchTreeList(data) {
  return request({
    url: '/pmsdevice/api/floor/selectFloorTree',
    method: 'post',
    data
  })
}
