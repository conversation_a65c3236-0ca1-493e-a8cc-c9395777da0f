<template>
  <div>
    <el-button :style="{ background: color, borderColor: color }" icon="el-icon-upload" size="mini" type="primary"
      @click="dialogVisible = true">
      上传图片
    </el-button>
    <el-dialog v-dialogDrag :close-on-click-modal="false" :visible.sync="dialogVisible" title="上传图片">
      <el-upload name="files" :on-success="handleSuccess" :action="uploadUrl" :show-file-list="true"
        list-type="picture-card" :on-remove="handleRemove" multiple :file-list="files">
        <i class="el-icon-plus" />
        <!--<el-button size="small" type="primary" >点击上传</el-button>-->
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false"
          style="background-color: rgba(150, 170, 222, 0.4);border-color:rgba(150, 170, 222, 0.4);color: #fff;">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

export default {
  name: 'EditorSlideUpload',
  props: {
    color: {
      type: String,
      default: '#99171A'
    }
  },
  props: ['color'],
  data() {
    return {
      uploadUrl: '',
      downloadUrl: '',
      files: [],
      // color: '',
      dialogVisible: false,
      listObj: {},
      fileList: []
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.fileList = []
      this.files = []
      this.uploadUrl = process.env.VUE_APP_UPLOAD
      this.downloadUrl = process.env.VUE_APP_DOWNLOAD
    },
    handleSubmit() {
      this.$emit('successCBK', this.fileList)
      this.fileList = []
      this.files = []
      this.dialogVisible = false
    },
    handleSuccess(response, file) {
      this.fileList.push({
        url: this.downloadUrl + file.response.data[0].fileId,
        fileName: response.data[0].fileName,
        fileType: this.judgeFileType(file.raw.type)
      })
    },
    // 删除图片
    handleRemove(file, fileList) {
      var currentUrl = this.downloadUrl + file.response.data[0].fileId // 获取当前删除文件的url
      var currentFileName = response.data[0].fileName // 获取当前删除文件的文件名
      var currentFileType = this.judgeFileType(file.raw.type) // 获取当前删除文件的文件类型
      this.fileList.forEach((item, index) => {
        if (currentUrl === item.url) {
          this.fileList.splice(index, 1)
        }
      })
    },
    // 判断上传的文件类型，方便数据库保存
    judgeFileType(str) {
      if (str === 'image/png' || str === 'image/jpeg' || str === 'image/jpg') {
        return 2
      } else {
        return 1
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.editor-slide-upload {
  margin-bottom: 1.8520vh;

  /deep/ .el-upload--picture-card {
    width: 100%;
  }
}
</style>
