// 穿梭框带分页控件
<template>
  <div class="transferDiv">
    <div class="el-transfer-panel district-panel">
      <div class="el-transfer-panel__header">
        <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">
          {{ title[titleId] }}
        </el-checkbox>
        <span class="check-number">{{ checkedCities.length }}/{{ districtListMock.length }}</span>
      </div>
      <div class="el-transfer-panel__body">
        <div class="el-transfer-panel__filter el-input el-input--small el-input--prefix">
          <input v-model="searchWord" type="text" autocomplete="off" placeholder="请输入" class="el-input__inner">
          <span class="el-input__prefix"><i class="el-input__icon el-icon-search" /></span>
        </div>
        <el-checkbox-group v-if="districtListMock.length > 0" v-model="checkedCities" @change="handleCheckedChange">
          <el-checkbox v-for="item in districtListMock" :key="item.id" class="el-transfer-panel__item" :label="item" style="display:block">
            {{ item.name }}
          </el-checkbox>
        </el-checkbox-group>
        <p v-else class="no-data">无数据</p>
      </div>
      <div class="vip-footer">
        <div style="padding: 2vh 0;">共{{ districtList.length }}条，当前页：{{ pageIndex+1 }}，总页数：{{ total }}</div>
        <el-button v-if="titleId === 1" size="small" icon="el-icon-arrow-left" type="primary" @click="clearAll">移除所有</el-button>

        <el-button type="primary" size="small" :disabled="disabledPre" @click="prev">上一页</el-button>
        <el-button type="primary" size="small" :disabled="disabledNex" @click="next">下一页</el-button>
        <!-- <el-button class="v-page" plain :disabled="disabledPre" @click="prev">上一页</el-button> -->
        <!-- <el-button class="v-page" plain :disabled="disabledNex" @click="next">下一页</el-button> -->

        <el-button v-if="titleId === 0" size="small" type="primary" @click="selectAll">选中所有<i class="el-icon-arrow-right" /></el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    titleId: {
      type: Number
    },
    districtList: { // 父组件传递的数据
      type: Array
    },
    pageSize: {
      type: Number
    },
    disabledAll: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      title: ['设备列表', '已选设备'],
      districtListMock: [], // 展示的数据 （搜索和分页会自动修改这个数组）
      checkedCities: [], // 已选择，数据格式：[id,id,id...]
      isIndeterminate: false,
      checkAll: false,
      searchWord: '',
      len: 0,
      total: 0,
      pageIndex: 0,
      disabledPre: true,
      disabledNex: false
    }
  },
  watch: {
    districtList() {
      this.getDistrict()
    },
    // 搜索框的监听器
    searchWord(newWord) {
      this.$emit('search-word', newWord, this.titleId)
    },
    // districtListMock 和 checkAll 的监听器
    districtListMock() {
      // 当方框中无已选择的数据时，不能勾选checkBox
      if (this.checkedCities.length === 0) {
        this.checkAll = false
        this.isIndeterminate = false
      }
    },
    checkedCities(newWord) {
      this.$emit('check-disable', newWord, this.titleId)
    },
    // 当列表中无数据时，不能勾选checkBox
    checkAll() {
      this.checkAll = this.districtListMock.length === 0 ? false : this.checkAll
    }
  },
  created() {
    this.getDistrict()
  },
  methods: {
    // 分页数据
    getDistrict() {
      this.len = this.districtList.length
      this.total = Math.ceil(this.len / this.pageSize)
      this.pageIndex = 0
      this.pageData()
    },
    pageData() {
      this.checkedCities = []
      if (this.total > 1 && this.pageIndex < (this.total - 1)) {
        this.pageIndex === 0 ? this.disabledPre = true : this.disabledPre = false
        this.disabledNex = false
        this.districtListMock = this.districtList.slice(this.pageIndex * this.pageSize, this.pageIndex * this.pageSize + this.pageSize)
      } else {
        this.total > 1 ? this.disabledPre = false : this.disabledPre = true
        this.disabledNex = true
        this.districtListMock = this.districtList.slice(this.pageIndex * this.pageSize, this.len)
      }
    },
    // 上一页
    prev() {
      this.pageIndex > 0 && --this.pageIndex
      this.pageData()
    },
    // 下一页
    next() {
      this.pageIndex <= (this.total - 1) && ++this.pageIndex
      this.pageData()
    },
    // 单选
    handleCheckedChange(value) {
      const checkedCount = value.length
      this.checkAll = checkedCount === this.districtListMock.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.districtListMock.length
      // 子传父
      this.$emit('check-district', value)
    },
    // 全选
    handleCheckAllChange(val) {
      this.checkedCities = val ? this.districtListMock.map(val => val) : []
      this.isIndeterminate = false
      // 子传父
      this.$emit('check-district', this.checkedCities)
    },
    // 全选
    selectAll() {
      this.$emit('selectAllEquipment')
    },
    clearAll() {
      this.$emit('clearAllEquipment')
    }
  }
}
</script>

<style lang="scss" scoped>
  .district-panel {
    width: 25.9280vh;
  }

  .transferDiv{
      float: left;
  }

  .transferDiv .el-transfer-panel__body {
    height: 27.7800vh;
  }

  .transferDiv .el-checkbox-group {
    height: 21.2980vh;
    overflow: auto;
  }

  .transferDiv .check-number {
    position: absolute;
    right: 1.3890vh;
    top: 0;
    font-size: 1.1112vh;
    font-weight: 400;
  }

  .transferDiv .no-data {
    margin: 0;
    height: 2.7780vh;
    line-height: 2.7780vh;
    padding: 0.5556vh 1.3890vh 0;
    text-align: center;
  }

  .transferDiv .vip-footer {
    position: relative;
    margin: 0;
    text-align: center;
  }

  .transferDiv .v-page {
    display: inline;
    // float: left;
    // width: 50%;
    border: none;
    margin: 0;
    border-radius: 0;
  }
</style>

<style>
  .transferDiv .vip-footer {
    margin-bottom: 0.9260vh !important;
  }

  .transferDiv .el-transfer-panel {
    /* border: 1px solid #b0e8dc;
    background: #ebf9f6; */
  }

  .transferDiv .el-transfer-panel .el-transfer-panel__header {
    /* background: #32BBA0;
    border-bottom: 1px solid #b0e8dc; */
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }

  .transferDiv .el-transfer-panel__item.el-checkbox .el-checkbox__label {
    /* color: #707174 !important; */
  }

  .transferDiv .el-checkbox-group {
    height: 29.6320vh !important;
  }

  .transferDiv .el-transfer-panel__body {
    height: 34.2620vh !important;
  }

  .transferDiv .el-button.is-disabled.is-plain,
  .el-button.is-disabled.is-plain:focus,
  .el-button.is-disabled.is-plain:hover {
    /* background-color: #32BBA0; */
  }

  .transferDiv .district-panel .el-button {
    /* background-color: #32BBA0;
    color: #ffffff; */
  }
</style>
