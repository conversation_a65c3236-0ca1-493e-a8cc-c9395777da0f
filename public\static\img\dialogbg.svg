<svg width="1115" height="800" viewBox="0 0 1115 800" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_105_2)" filter="url(#filter0_b_105_2)">
<rect width="1115" height="800" fill="url(#paint0_linear_105_2)"/>
<g filter="url(#filter1_b_105_2)">
<rect x="1113.51" y="177.553" width="1.48667" height="622.447" fill="url(#paint1_linear_105_2)"/>
<rect y="177.553" width="1.48667" height="622.447" fill="url(#paint2_linear_105_2)"/>
<rect x="1.48669" y="797.423" width="1112.03" height="2.57742" fill="url(#paint3_linear_105_2)"/>
<rect y="790.979" width="43.1133" height="9.02097" fill="#76AFF9"/>
<rect x="1071.89" y="790.979" width="43.1133" height="9.02097" fill="#76AFF9"/>
<path d="M1115 3H0V82H1115V3Z" fill="url(#paint4_linear_105_2)" fill-opacity="0.3"/>
<rect y="2.5" width="16" height="3" fill="#76AFF9"/>
<rect x="16" y="3.5" width="1083" height="1" fill="#76AFF9"/>
<rect x="1099" y="2.5" width="16" height="3" fill="#76AFF9"/>
<rect y="79" width="16" height="3" fill="#76AFF9"/>
<rect x="16" y="80" width="1015" height="1" fill="#76AFF9"/>
<path d="M1039 79H1037L1035 82H1037L1039 79Z" fill="#76AFF9"/>
<path d="M1043 79H1041L1039 82H1041L1043 79Z" fill="#76AFF9"/>
<path d="M1047 79H1045L1043 82H1045L1047 79Z" fill="#76AFF9"/>
<rect x="1051" y="79" width="64" height="3" fill="url(#paint5_linear_105_2)"/>
<path d="M22 30L32 15H38L28 30H22Z" fill="#76AFF9"/>
<path d="M34 30L44.625 15H51L40.375 30H34Z" fill="#76AFF9"/>
<path d="M47 30L57 15H63L53 30H47Z" fill="#76AFF9"/>
</g>
</g>
<defs>
<filter id="filter0_b_105_2" x="-47" y="-47" width="1209" height="894" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImage" stdDeviation="23.5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_105_2"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_105_2" result="shape"/>
</filter>
<filter id="filter1_b_105_2" x="-16" y="-15" width="1147" height="831" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImage" stdDeviation="8"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_105_2"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_105_2" result="shape"/>
</filter>
<linearGradient id="paint0_linear_105_2" x1="557.5" y1="0" x2="557.5" y2="800" gradientUnits="userSpaceOnUse">
<stop stop-color="#151D37"/>
<stop offset="0.21875" stop-color="#212A45"/>
<stop offset="1" stop-color="#212A45"/>
</linearGradient>
<linearGradient id="paint1_linear_105_2" x1="1114.26" y1="177.553" x2="1114.26" y2="800" gradientUnits="userSpaceOnUse">
<stop stop-color="#76AFF9" stop-opacity="0"/>
<stop offset="0.4875" stop-color="#76AFF9"/>
<stop offset="1" stop-color="#76AFF9" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_105_2" x1="0.743333" y1="177.553" x2="0.743333" y2="800" gradientUnits="userSpaceOnUse">
<stop stop-color="#76AFF9" stop-opacity="0"/>
<stop offset="0.4875" stop-color="#76AFF9"/>
<stop offset="1" stop-color="#76AFF9" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_105_2" x1="1.48669" y1="800.002" x2="1113.51" y2="800.002" gradientUnits="userSpaceOnUse">
<stop stop-color="#76AFF9" stop-opacity="0"/>
<stop offset="0.492708" stop-color="#76AFF9"/>
<stop offset="1" stop-color="#76AFF9" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint4_linear_105_2" x1="-5.71134e-06" y1="42.4999" x2="1115" y2="42.4999" gradientUnits="userSpaceOnUse">
<stop stop-color="#76AFF9"/>
<stop offset="0.669792" stop-color="#76AFF9" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint5_linear_105_2" x1="1115" y1="81" x2="1051" y2="81" gradientUnits="userSpaceOnUse">
<stop stop-color="#76AFF9"/>
<stop offset="1" stop-color="#76AFF9" stop-opacity="0.4"/>
</linearGradient>
<clipPath id="clip0_105_2">
<rect width="1115" height="800" fill="white"/>
</clipPath>
</defs>
</svg>
