import request from '@/utils/request'
export function fetchList(data) {
  return request({
    url: '/pmslinkage/api/linkage/logicDict/list',
    method: 'post',
    data
  })
}

export function fetchDetail(queryData) {
  return request({
    url: '/pmslinkage/api/linkage/logicDict/detail',
    method: 'get',
    params: queryData
  })
}

export function fetchDelete(ids) {
  var data = { ids }
  return request({
    url: '/pmslinkage/api/linkage/logicDict/delete',
    method: 'post',
    data
  })
}

export function fetchCreate(data) {
  return request({
    url: '/pmslinkage/api/linkage/logicDict/create',
    method: 'post',
    data
  })
}

export function fetchUpdate(data) {
  return request({
    url: '/pmslinkage/api/linkage/logicDict/update',
    method: 'post',
    data
  })
}
