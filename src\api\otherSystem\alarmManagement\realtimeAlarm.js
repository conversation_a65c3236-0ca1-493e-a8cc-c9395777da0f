import request from '@/utils/request'

// 离线实时告警列表
export function logOfflineRTList(data) {
  return request({
    url: `/devService/api/deviceOfflineAlert/list`,
    method: 'post',
    data
  })
}
// 离线历史告警列表
export function logOfflineList(data) {
  return request({
    url: ` /esService/deviceOfflineAlertHistoryES/query`,
    method: 'post',
    data
  })
}

// 故障、性能告警列表
export function fetchList(data) {
  return request({
    url: '/devService/api/alertRecord/list',
    method: 'post',
    data
  })
}
// 实时性能告警列表
export function getRealTimeList(data) {
  return request({
    url: '/devService/api/alertRecord/list',
    method: 'post',
    data
  })
}
// 历史性能告警列表old
export function _getHistoryList(data) {
  return request({
    url: '/devService/api/alertRecord/historyList',
    method: 'post',
    data
  })
}
// 历史性能告警列表
export function getHistoryList(data) {
  return request({
    url: '/esService/deviceAlertRecordHistoryES/query',
    method: 'post',
    data
  })
}
// 获取性能告警频次详情列表
export function geCountList(data) {
  return request({
    url: '/devService/api/alertRecord/getAlarmDetail',
    method: 'post',
    data
  })
}
// 详情
export function fetchDetail(data) {
  return request({
    url: '/devService/api/alertRecord/get',
    method: 'post',
    data
  })
}
