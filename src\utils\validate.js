/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/11/18.
 */

/**
 * @param {string} path
 * @returns {Boolean}
 */

// 判断身份证号码
/* export  function isIdCardNo(idCard){
            var reg = /^\d{6}(18|19|20)?\d{2}(0[1-9]|1[12])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i
             if (!reg.test(idCard)) {
                return false
             }else{
              return true
             }
} */

export function isIdCardNo(code) {
  // 身份证号合法性验证
  // 支持15位和18位身份证号
  // 支持地址编码、出生日期、校验位验证
  var city = { 11: '北京', 12: '天津', 13: '河北', 14: '山西', 15: '内蒙古', 21: '辽宁', 22: '吉林', 23: '黑龙江 ', 31: '上海', 32: '江苏', 33: '浙江', 34: '安徽', 35: '福建', 36: '江西', 37: '山东', 41: '河南', 42: '湖北 ', 43: '湖南', 44: '广东', 45: '广西', 46: '海南', 50: '重庆', 51: '四川', 52: '贵州', 53: '云南', 54: '西藏 ', 61: '陕西', 62: '甘肃', 63: '青海', 64: '宁夏', 65: '新疆', 71: '台湾', 81: '香港', 82: '澳门', 91: '国外 ' }
  var row = true

  if (!code || !/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|[xX])$/.test(code)) {
    row = false
  } else if (!city[code.substr(0, 2)]) {
    row = false
  } else {
    // 18位身份证需要验证最后一位校验位
    if (code.length == 18) {
      code = code.split('')
      // ∑(ai×Wi)(mod 11)
      // 加权因子
      var factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
      // 校验位
      var parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2]
      var sum = 0
      var ai = 0
      var wi = 0
      for (var i = 0; i < 17; i++) {
        ai = code[i]
        wi = factor[i]
        sum += ai * wi
      }
      if (parity[sum % 11] != code[17].toUpperCase()) {
        row = false
      }
    }
  }

  return row
}

export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  const valid_map = ['admin', 'editor']
  return valid_map.indexOf(str.trim()) >= 0
}

/**
 * 密码设置应避免3位以上（含3位）键盘排序密码，如qwe（键盘第1行前三个字母）、asd（键盘第2行前三个字母）、qaz（键盘第1列三个字母）、1qaz（键盘第1列数字加前三个字母）、！QAZ（键盘第1列特殊字符加前三个字母）等
 * @param str
 * @return
 */
export function _isKeyBoardContinuousChar(str) {
  const c1 = [
    ['!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '_', '+'],
    ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', '{', '}', '|'],
    ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', ':', '"'],
    ['z', 'x', 'c', 'v', 'b', 'n', 'm', '<', '>', '?']
  ]
  const c2 = [
    ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '='],
    ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', '[', ']', '\\'],
    ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', ';', '\''],
    ['z', 'x', 'c', 'v', 'b', 'n', 'm', ',', '.', '/']
  ]
  str = str.split('')
  // 获取坐标位置
  const y = []
  const x = []
  for (let c = 0; c < str.length; c++) {
    y[c] = 0 // 当做~`键处理
    x[c] = -1
    for (let i = 0; i < c1.length; i++) {
      for (let j = 0; j < c1[i].length; j++) {
        if (str[c] === c1[i][j]) {
          y[c] = i
          x[c] = j
        }
      }
    }
    if (x[c] !== -1) continue
    for (let i = 0; i < c2.length; i++) {
      for (let j = 0; j < c2[i].length; j++) {
        if (str[c] === c2[i][j]) {
          y[c] = i
          x[c] = j
        }
      }
    }
  }
  // 匹配坐标连线
  for (let c = 1; c < str.length - 1; c++) {
    if (y[c - 1] === y[c] && y[c] === y[c + 1]) {
      if ((x[c - 1] + 1 === x[c] && x[c] + 1 === x[c + 1]) || (x[c + 1] + 1 === x[c] && x[c] + 1 === x[c - 1])) {
        return true
      }
    } else if (x[c - 1] === x[c] && x[c] === x[c + 1]) {
      if ((y[c - 1] + 1 === y[c] && y[c] + 1 === y[c + 1]) || (y[c + 1] + 1 === y[c] && y[c] + 1 === y[c - 1])) {
        return true
      }
    }
  }
  return false
}

/**
 * 不能连续的三个数字
 * @param {*} str
 * @param return
 */
export function LxStr(str) {
  const arr = str.split('')
  let flag = true
  for (let i = 1; i < arr.length - 1; i++) {
    const firstIndex = arr[i - 1]
    const secondIndex = arr[i]
    const thirdIndex = arr[i + 1]
    thirdIndex - secondIndex === 1
    secondIndex - firstIndex === 1
    if ((thirdIndex - secondIndex === 1) && (secondIndex - firstIndex === 1)) {
      flag = false
    }
  }
  return flag
}
