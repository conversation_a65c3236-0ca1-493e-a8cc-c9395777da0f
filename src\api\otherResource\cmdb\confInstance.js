import request from '@/utils/request'

const url = '/cmdbService'
export function queryInstance(params) {
  return request({
    url: url + '/resCiInstance/getCiInstance',
    method: 'post',
    data: params
  })
}

export function queryRelate(params) {
  return request({
    url: url + '/resCiInstance/getRelateCiInstacne',
    method: 'post',
    data: params
  })
}

export function saveResource(params) {
  return request({
    url: url + '/confCiType/',
    method: 'post',
    data: params
  })
}

export function updateInstance(params) {
  return request({
    url: url + '/resCiInstance/',
    method: 'put',
    data: params
  })
}

export function insertInstance(params) {
  return request({
    url: url + '/resCiInstance/',
    method: 'post',
    data: params
  })
}

export function deleteInstance(params) {
  return request({
    url: url + '/resCiInstance/' + params,
    method: 'delete',
    data: params
  })
}

export function getCiInstanceSelectOptionByDictId(params) {
  return request({
    url: url + '/resCiInstance/getCiInstanceSelectOptionByDictId',
    method: 'post',
    data: params
  })
}

export function getSelectOptionDataByTypeCode(params) {
  return request({
    url: url + '/resCiInstance/getSelectOptionDataByTypeCode',
    method: 'post',
    data: params
  })
}

export function getUseStatusDictFunction(params) {
  return request({
    url: '/userService/api/dict/getUseStatusDict',
    method: 'post',
    data: params
  })
}

export function queryViewList(params) {
  return request({
    url: url + '/resCiInstanceRelView/queryViewList',
    method: 'post',
    data: params
  })
}

export function queryResCiInstanceRelView(params) {
  return request({
    url: url + '/resCiInstanceRelView/queryResCiInstanceRelView',
    method: 'post',
    data: params
  })
}

export function deleteSaveDataItem(id) {
  return request({
    url: url + `/resCiInstanceRelView/${id}`,
    method: 'delete'
  })
}

export function getUserSearchFavorOptionFunction() {
  return request({
    url: url + '/userSearchFavor/',
    method: 'get'
  })
}

export function addUserSearchFavorFunction(param) {
  return request({
    url: url + '/userSearchFavor/addUserSearchFavor',
    method: 'post',
    data: param
  })
}

export function editUserSearchFavorFunction(param) {
  return request({
    url: url + '/userSearchFavor/editUserSearchFavor',
    method: 'post',
    data: param
  })
}

export function getRelateCiTypeOptionFunction(param) {
  return request({
    url: url + '/resCiInstance/getRelateCiTypeOption',
    method: 'post',
    data: param
  })
}

export function getBelongSystemFunction(param) {
  return request({
    url: url + '/resCiInstance/getBelongSystem',
    method: 'post',
    data: param
  })
}

export function commonQueryFunction(param) {
  return request({
    url: url + '/resCiInstance/commonQuery',
    method: 'post',
    data: param
  })
}

export function getSystemInstanceFunction(param) {
  return request({
    url: url + '/resCiInstance/getSystemInstance',
    method: 'post',
    data: param
  })
}

export function getCommonQueryAndCustomizeQuery(param) {
  return request({
    url: url + '/resCiInstance/getCommonQueryAndCustomizeQuery',
    method: 'post',
    data: param
  })
}

export function getRelateCiInstanceDetail(param) {
  return request({
    url: url + '/resCiInstance/getRelateCiInstanceByInstanceId',
    method: 'get',
    params: param
  })
}

export function getSecondMenu(param) {
  return request({
    url: url + '/confCiTypeAttri/getSecondConfCiTypeByInstanceId',
    method: 'post',
    data: param
  })
}

export function getFirstMenu(param) {
  return request({
    url: url + '/confCiTypeAttri/getFirstConfCiTypeByInstanceId',
    method: 'post',
    data: param
  })
}

export function getOperationRecord(params) {
  return request({
    url: url + '/use-status-record/page',
    method: 'get',
    params: params
  })
}

export function takeDown(data) {
  return request({
    url: url + '/resCiInstance/takeDown',
    method: 'post',
    data: data
  })
}

export function findCommonQueryAndCustomizeQuery(data) {
  return request({
    url: url + '/resCiInstance/findCommonQueryAndCustomizeQuery',
    method: 'post',
    data: data
  })
}

export function getConfCiInstanceRelByInstanceIda(data) {
  return request({
    url: url + '/confCiInstanceRel/getConfCiInstanceRelByInstanceIda',
    method: 'get',
    params: data
  })
}

export function mergeConfCiInstanceRel(data) {
  return request({
    url: url + '/confCiInstanceRel/mergeConfCiInstanceRel',
    method: 'post',
    data: data
  })
}

export function findResCiInstanceByTypeCode(data) {
  return request({
    url: url + '/resCiInstance/findResCiInstanceByTypeCode',
    method: 'post',
    data: data
  })
}

export function batchQrCodeGeneral(data) {
  return request({
    url: url + '/resCiInstance/batchQrCodeGeneral',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

export function batchQrCodeDownload(param) {
  return request({
    url: url + '/resCiInstance/batchQrCodeDownload',
    method: 'post',
    data: param,
    responseType: 'blob'
  })
}
