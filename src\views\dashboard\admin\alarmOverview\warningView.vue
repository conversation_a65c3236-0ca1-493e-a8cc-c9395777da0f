<template>
  <div class="box">
    <div class="sbglDiv1">
      <!-- <div class="sbglDiv">
        <div class="icon icon-warning" @click="giveAnAlarm" />
        <div class="sbglFont-text">当前告警</div>
        <div class="sbglFont-number">{{ keyModel.deviceAlarmNum || 0 }}</div>
      </div> -->
      <div class="sbglDiv icon icon-total" @click="curAlarmListDialog">
        <div class="alarm-content">
          <div class="sbglFont-number" :class="{'number-changed': numberChanged.history}">
            {{ keyModel.historyAlarmNum || 0 }}
          </div>
        <div class="sbglFont-text">历史告警</div>
        </div>
      </div>
      <div class="sbglDiv icon icon-warning" :class="{'warning-active': hasRealTimeAlarm}" @click="noDealAlarmListDialog">
        <div class="alarm-content">
          <div class="sbglFont-number" :class="{'number-changed': numberChanged.realtime}">
            {{ keyModel.realtimeAlarmNum == null ? 0 : keyModel.realtimeAlarmNum }}
          </div>
        <div class="sbglFont-text">实时告警</div>
        </div>
      </div>
    </div>
    <div class="bg1">
      <el-dialog ref="dialog" v-dialogDrag append-to-body class="alarmDialogDetail dashboard-dialog" :title="title"
        :visible.sync="warnBoxVisible" width="142.2224vh" @close="hideWarnBox">
        <dealAlarmList :show-dialog="warnBoxVisible" :query-type="queryType" :sys-type="sysType"
          @resetChoose="resetChoose" @positionMap="positionMap" />
      </el-dialog>
      <el-dialog v-dialogDrag append-to-body class="alarmDialogDetail dashboard-dialog" title="工单情况统计分析"
        :visible.sync="workOrderVisible" width="106.6668vh" @close="hideWorkOrderBox">
        <workOrderStatisticalanalysis :sys-type="sysType" :show-dialog="workOrderVisible" />
      </el-dialog>
    </div>
    <div class="bg1">
      <div class="equipMentDiaLogDiv">
        <el-dialog ref="dialog" v-dialogDrag destroy-on-close append-to-body class="layerManager dashboard-dialog"
          :title="title" :visible.sync="equipMentTotalListValue.showDialog" :width="equipMentTotalListDialogWidth"
          :before-close="handleClose">
          <equipMentTotalList ref="equipMentTotalList" :sys-type="sysType" v-bind="equipMentTotalListValue"
            @positionMap="positionMap" @resetChoose="resetChoose" />
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import workOrderStatisticalanalysis from '../../../PMS/statisticAnalysis/workOrderStatisticalanalysis'
import dealAlarmList from './noDealAlarmList'
import equipMentTotalList from '../equipMentObject/equipMentTotalList'

export default {
  components: {
    workOrderStatisticalanalysis,
    dealAlarmList,
    equipMentTotalList
  },
  props: ['keyModel', 'sysType'],
  data() {
    return {
      equipMentTotalListValue: {
        showDialog: false,
        isClear: true,
        deviceStatus: null
      },
      warnBoxVisible: false,
      workOrderVisible: false,
      thisTime: null,
      title: '',
      equipMentTotalListDialogWidth: '142.2224vh',
      queryType: 1, // 1、未处理告警；2、今日告警总数
      numberChanged: {
        history: false,
        realtime: false
      },
      previousNumbers: {
        history: 0,
        realtime: 0
      }
    }
  },
  computed: {
    hasRealTimeAlarm() {
      return this.keyModel.realtimeAlarmNum > 0
    }
  },
  watch: {
    'keyModel.historyAlarmNum'(newVal, oldVal) {
      if (oldVal !== undefined && newVal !== oldVal) {
        this.numberChanged.history = true
        setTimeout(() => {
          this.numberChanged.history = false
        }, 1000)
      }
      this.previousNumbers.history = newVal
    },
    'keyModel.realtimeAlarmNum'(newVal, oldVal) {
      if (oldVal !== undefined && newVal !== oldVal) {
        this.numberChanged.realtime = true
        setTimeout(() => {
          this.numberChanged.realtime = false
        }, 1000)
      }
      this.previousNumbers.realtime = newVal
    }
  },
  created() {
    const obj = {
      isReally: 1
    }
    if (this.sysType) {
      obj['sysType'] = this.sysType
    }
  },
  methods: {
    ShowWarnDialog() {
      this.warnBoxVisible = true
    },
    noDealAlarmListDialog() {
      this.initDialog()
      this.queryType = 1
      this.title = '实时告警列表'
      this.warnBoxVisible = true
    },
    curAlarmListDialog() {
      this.initDialog()
      this.queryType = 2
      this.title = '历史告警列表'
      this.warnBoxVisible = true
    },
    initDialog() {
      setTimeout(() => {
        if (this.$refs.dialog) {
          this.$refs.dialog.$el.firstChild.style.top = 0
          this.$refs.dialog.$el.firstChild.style.left = 0
        }
      })
    },
    giveAnAlarm() {
      this.$emit('changeOpacityOne')
      this.title = '当前设备告警列表'
      this.initList(2)
      this.equipMentTotalListValue.deviceStatus = 2
      this.showList()
    },
    initList(value) {
      if (this.$refs.equipMentTotalList) {
        if (!this.equipMentTotalListValue.isClear && this.equipMentTotalListValue.deviceStatus !== value) {
          this.equipMentTotalListValue.isClear = true
          setTimeout(() => {
            this.$refs.dialog.$el.firstElementChild.lastElementChild.classList.remove('dialogBodyNone')
            this.$refs.dialog.$el.firstChild.style.left = 0
            this.equipMentTotalListDialogWidth = '142.2224vh'
            this.$refs.equipMentTotalList.tableHeight = 570
            this.$refs.dialog.$el.firstChild.style.opacity = 1
          })
        }
      }
    },
    showList() {
      this.equipMentTotalListValue.showDialog = true
      // 不需要清空，说明没有关闭弹窗，只是点击了定位地图，现在要重新打开，不要重置数据
      if (!this.equipMentTotalListValue.isClear) {
        this.fangdaDialog()
      } else {
        setTimeout(() => {
          if (this.$refs.equipMentTotalList) {
            this.$refs.equipMentTotalList.tableHeight = 570
            this.$refs.dialog.$el.firstChild.style.left = 0
            this.$refs.dialog.$el.firstChild.style.top = 0
          }
        })
      }
    },
    // 缩小dialog
    smallDialog() {
      this.equipMentTotalListValue.isClear = false
      let width = 142.2224
      let left = 0
      let height = 570
      let timer = null
      timer = setInterval(() => {
        width -= 17.7778
        left += 10.48890
        height -= 71.25
        if (width < 8.8889) {
          width = 8.8889
          clearInterval(timer)
          this.equipMentTotalListValue.showDialog = false
        }
        if (width < 88.889) {
          this.$refs.equipMentTotalList.tableConfig.showPagination = false
        }
        if (width < 17.7778) {
          this.$refs.dialog.$el.firstElementChild.lastElementChild.className += ' dialogBodyNone'
        }
        this.equipMentTotalListDialogWidth = width + 'vh'
        this.$refs.equipMentTotalList.tableHeight = height
        this.$refs.dialog.$el.firstChild.style.left = -left + 'vh'
        this.$refs.dialog.$el.firstChild.style.opacity = width / 177.778
      }, 10)
    },
    // 放大
    fangdaDialog() {
      this.$refs.dialog.$el.firstElementChild.lastElementChild.classList.remove('dialogBodyNone')
      let width = parseInt(this.$refs.dialog.$el.firstChild.style.width)
      let left = parseInt(this.$refs.dialog.$el.firstChild.style.left)
      let height = parseInt(this.$refs.equipMentTotalList.tableHeight)
      let opacity = parseInt(this.$refs.dialog.$el.firstChild.style.opacity)
      let timer = null
      timer = setInterval(() => {
        width += 17.7778
        left += 10.66668
        height += 71.25
        opacity += 0.2
        if (width >= 142.2224) {
          this.$refs.dialog.$el.firstChild.style.left = 0
          this.equipMentTotalListDialogWidth = '142.2224vh'
          this.$refs.equipMentTotalList.tableHeight = 570
          this.$refs.dialog.$el.firstChild.style.opacity = 1
          clearInterval(timer)
          return
        }
        if (width > 88.889) {
          this.$refs.equipMentTotalList.tableConfig.showPagination = true
        }
        this.equipMentTotalListDialogWidth = width + 'vh'
        this.$refs.dialog.$el.firstChild.style.left = left + 'vh'
        this.$refs.equipMentTotalList.tableHeight = height
        this.$refs.dialog.$el.firstChild.style.opacity = opacity
      }, 10)
    },
    hideWarnBox() {
      this.warnBoxVisible = false
    },
    ShowWorkOrdeDialog() {
      this.workOrderVisible = true
    },
    hideWorkOrderBox() {
      this.workOrderVisible = false
    },
    // 取消选中设备
    resetChoose(deviceData) {
      this.warnBoxVisible = false
      this.$EventBus.$emit('resetChoose', deviceData)
    },
    // 定位地图
    positionMap(deviceData) {
      this.equipMentTotalListValue.showDialog = false
      this.warnBoxVisible = false
      // 显示设备
      this.$EventBus.$emit('showDevice', deviceData)
      // 选择某一个设备
      this.$EventBus.$emit('deviceData', deviceData)
    },
    handleClose(done) {
      this.$emit('changeOpacityOnes')
      this.equipMentTotalListValue.isClear = true
      this.$refs.equipMentTotalList.searchClear = false
      done()
    }
  }
}
</script>
<style lang="scss" scoped>
.box {
  width: 100%;
  display: flex;
  justify-content: center;
}

.alert_box {
  margin-left: -0.35556vh;
  margin-right: -0.35556vh;
  margin-top: -0.4vh;

}

.sbglDiv1 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;

}

.sbglDiv {
  width: 50%;
  text-align: center;
  border-radius: 5px;
  margin: 0 0.88889vh;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    filter: brightness(1.1);
  }
}

.alarm-content {
  position: relative;
  z-index: 2;
}

.sbglFont-text {
  color: #ffffff;
  font-size: 1.42vh;
  font-weight: 400;
  width: 100%;
  height: 3vh;
  line-height: 3vh;
  text-indent: -0.5vh;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.sbglFont-number {
  font-size: 1.85vh;
  color: rgba(255, 255, 255, 1);
  text-align: center;
  width: 100%;
  height: 7.4vh;
  line-height: 9.3vh;
  text-indent: -0.5vh;
  font-weight: 700;
  position: relative;
  transition: all 0.3s ease;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  
  &.number-changed {
    animation: numberPop 0.5s ease;
  }
}

.icon {
  width: 13.98vh;
  height: 10.47vh;
  background-size: 100% 100%;
  position: relative;
  overflow: hidden;
}

.icon-undeal {
  background-image: url('/static/img/undeal.png')
}

.icon-warning {
  background-image: url('/static/img/newImg/deviceWarning.svg');
  
  &.warning-active {
    animation: warningPulse 2s infinite;
  }
}

.icon-total {
  background-image: url('/static/img/newImg/total.svg');
}

.alarm-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 3vh;
  height: 3vh;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  animation: ringPulse 1.5s infinite;
}

.alarm-wave {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  
  span {
    position: absolute;
    width: 4vh;
    height: 4vh;
    border: 2px solid rgba(255, 87, 34, 0.5);
    border-radius: 50%;
    animation: waveEffect 2s infinite;
    
    &:nth-child(2) {
      animation-delay: 0.5s;
    }
    
    &:nth-child(3) {
      animation-delay: 1s;
    }
  }
}

@keyframes numberPop {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
    color: #FFF;
  }
  100% {
    transform: scale(1);
  }
}

@keyframes warningPulse {
  0% {
    filter: brightness(1);
  }
  50% {
    filter: brightness(1.3);
  }
  100% {
    filter: brightness(1);
  }
}

@keyframes ringPulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

@keyframes waveEffect {
  0% {
    transform: scale(0.5);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}


</style>
