<template>
  <div class="noDealAlarmDiv">
    <div v-if="isShowSearch">
      <!-- <deviceSearch ref="deviceSearch" style="display: inline-block" v-bind="deviceSearchConfig"
        :share-device-name="listQuery.shareDeviceName" :is-clear="searchClear" :share-sys-type="sysType"
        @handleData="handleData"> -->
      <search-input ref="searchInput" v-model="listQuery" v-bind="curPageConfig.searchItems" @getTips="getTips"
        @handleData="handleData" @dateRangePickerChange="dateRangePickerChange">
        <template v-slot:after>
          <!-- <el-select
        v-model="listQuery.orderStatus"
        style="margin-right: 6px"
        clearable
        placeholder="请选择派单状态"
        class="form-item-width"
      >
        <el-option label="未派单" :value="0" />
        <el-option label="自动派单" :value="1" />
        <el-option label="手动派单" :value="2" />
      </el-select> -->
          <!-- <el-button type="primary" icon="el-icon-search" @click="search">搜索</el-button>
          <el-button v-waves type="primary" style="height: 35px" @click="$refs.deviceSearch.clear(listQuery)">{{
            $t("button.clear") }}</el-button> -->
          <!-- <el-button v-if="queryType == 1" type="primary" @click="answerWarning">批量关闭</el-button> -->
          <el-button v-waves style="margin-left: 6px" type="primary" icon="el-icon-search" @click="searchListInfo">{{
            $t("button.search") }}</el-button>
          <el-button v-waves style="margin-left: 6px" type="primary" @click="$refs.searchInput.clear(listQuery)">{{
            $t("button.clear") }}</el-button>
          <!-- <el-button type="primary" icon="el-icon-download" :loading="exportLoading"
            @click.stop="handleExportExcelServer">导出</el-button> -->
        </template>
      </search-input>

    </div>

    <el-table ref="tableDataRef" v-loading="listLoading" :data="dataList" element-loading-text="数据正在加载中..." border
      stripe fit highlight-current-row style="width: 100%" @row-click="handleRowClick">
      <el-table-column key="1" type="index" :label="$t('table.id')" width="100" />
      <el-table-column key="2" align="center" label="设备名称" min-width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <span
            :style="'color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';border-color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';'">{{
              scope.row.deviceName }}</span>
        </template>
      </el-table-column>
      <el-table-column key="3" align="center" label="设备编号" min-width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <span
            :style="'color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';border-color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';'">{{
              scope.row.deviceSerno }}</span>
        </template>
      </el-table-column>
      <el-table-column key="4" align="center" label="区域" min-width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span
            :style="'color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';border-color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';'">{{
              scope.row.areaCodeName ? scope.row.areaCodeName : scope.row.areaCode }}</span>
        </template>
      </el-table-column>
      <el-table-column key="5" align="center" label="所属路口" min-width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span
            :style="'color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';border-color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';'">{{
              scope.row.intersectionName }}</span>
        </template>
      </el-table-column>
      <el-table-column key="6" align="center" label="专题类型" min-width="100">
        <template slot-scope="scope">
          <span
            :style="'color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';border-color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';'">{{
              scope.row.sysTypeName }}</span>
        </template>
      </el-table-column>
      <el-table-column key="7" align="center" label="设备类型" min-width="80">
        <template slot-scope="scope">
          <span
            :style="'color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';border-color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';'">{{
              scope.row.deviceTypeName }}</span>
        </template>
      </el-table-column>
      <el-table-column key="8" align="center" label="厂商" min-width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span
            :style="'color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';border-color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';'">{{
              scope.row.manufacturerName }}</span>
        </template>
      </el-table-column>
      <el-table-column key="9" align="center" label="型号" min-width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span
            :style="'color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';border-color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';'">{{
              scope.row.equipType }}</span>
        </template>
      </el-table-column>
      <el-table-column key="10" align="center" label="告警内容" min-width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <span
            :style="'color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';border-color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';'">{{
              scope.row.alarmContent }}</span>
        </template>
      </el-table-column>
      <el-table-column key="11" align="center" label="告警级别" min-width="100">
        <template slot-scope="scope">
          <span
            :style="'color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';border-color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';'">{{
              scope.row.priority }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column key="12" align="center" label="今日频次" min-width="50">
        <template slot-scope="scope">
          <span
            :style="'color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';border-color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';'">{{
              scope.row.todayAlarmCount }}</span>
        </template>
      </el-table-column> -->
      <el-table-column key="13" align="center" label="告警时间" min-width="170">
        <template slot-scope="scope">
          <span
            :style="'color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';border-color:' + (scope.row.color == null ? 'unset' : scope.row.color) + ';'">{{
              scope.row.createTime }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column key="14" align="center" label="告警状态" min-width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.state == '0'" type="warning">待处理</el-tag>
          <el-tag v-else-if="scope.row.state == '1'" type="warning">待处理</el-tag>
          <el-tag v-else-if="scope.row.state == '2'">已关闭</el-tag>
          <el-tag v-else-if="scope.row.state == '3'" type="success">已派单</el-tag>
          <el-tag v-else type="warning">未知</el-tag>
        </template>
      </el-table-column> -->
      <!--  <el-table-column v-if="queryType == 2" key="15" align="center" label="工单号" min-width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <div v-if="scope.row.orderNo" @click.stop="showOrderNoDialog(scope.row.orderNo)">
            <el-link
              :style="'color:' + (scope.row.color == null ? '#606266' : scope.row.color) + ';display: block;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;'"
              type="primary">{{ scope.row.orderBusinessKey }}</el-link>
          </div>
        </template>
      </el-table-column> -->

      <!-- <el-table-column key="3" align="center" :render-header="renderHeader" label="告警源" show-overflow-tooltip>
        <template slot-scope="scope">
          <span :style="'color:'+(scope.row.color == null? 'unset':scope.row.color) +';border-color:'+(scope.row.color == null? 'unset':scope.row.color) +';'">{{ scope.row.source }}</span>
        </template>
      </el-table-column> -->

    </el-table>


    <div class="pagination-container">
      <el-pagination background :current-page="listQuery.page" :page-sizes="[10, 20, 30, 50, 100, 200]"
        :page-size="listQuery.limit" layout="total, sizes, prev, pager, next, jumper" :total="total"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>

    <el-dialog ref="dialog" v-dialogDrag append-to-body class="dashboard-dialog" :close-on-click-modal="false"
      :visible.sync="dialogAlarmDetailVisible" title="告警详细信息" width="74.074vh">
      <detail :is-dark-bg="true" :device-id="deviceId" :record-id="id" :show-dialog="dialogAlarmDetailVisible"
        @handCancle="handCancle" />
    </el-dialog>
    <el-dialog ref="dialog1" v-dialogDrag append-to-body class="dashboard-dialog" :close-on-click-modal="false"
      :visible.sync="dialogOrderNoDetailVisible" title="告警工单" width="124.4446vh">
      <orderNoDetail :order-no="orderNo" :show-dialog="dialogOrderNoDetailVisible" @handCancle="handCancle" />
    </el-dialog>
  </div>
</template>
<script>
import deviceSearch from '@/components/deviceSearch/index'
import orderNoDetail from './orderNoDetail.vue'
import detail from '@/views/PMS/alarmManagement/realtimeAlarm/details'
import SearchInput from '@/components/SearchInput/index.vue'
import { pageConfig } from './pageConfig'
import { pollingList, updateOrder } from '@/api/main/admin'
import download from '@/views/otherResource/soc/mixins/download'
import { logOfflineRTList, logOfflineList } from '@/api/otherSystem/alarmManagement/realtimeAlarm'

export default {
  name: 'NoDealAlarm',
  components: {
    detail,
    deviceSearch,
    orderNoDetail,
    SearchInput
  },
  mixins: [download],
  props: {
    showDialog: {
      type: Boolean,
      default: false
    },
    // queryType: 1待处理告警信息列表,2今日告警信息列表
    queryType: {
      type: Number,
      default: 1
    },
    sysType: {
      type: Number,
      default: null
    },
    isShowSearch: {
      type: Boolean,
      default: true
    },
    alarmDeviceId: {
      type: Number,
      default: null
    },
    isLayoutNav: {
      type: Boolean,
      default: false
    }
  },
  //inject: ['getRightOne'],
  data() {
    return {
      dataList: [],
      listLoading: false,
      total: null,
      dialogAlarmDetailVisible: false,
      id: null,
      deviceId: null,
      curClickRowValue: {},
      deviceSearchConfig: {
        isMutiSearch: false,
        isArea: true,
        isSysType: true,
        isDeviceType: true,
        isManufacturerCode: true
      },
      listQuery: {
        page: 1,
        limit: 10,
        orderBy: null,
        order: null,
        tagId: undefined,
        shareDeviceName: null,
        shareAreaCode: null,
        streetCode: null,
        intersectionCode: null,
        shareDeviceTypes: null,
        shareManufacturerCode: null,
        startTime: null,
        endTime: null,
        priority: null,
        shareEquipType: null,
        states: null,
        selectType: null
      },
      searchClear: false,
      curPageConfig: null,
      orderNo: '',
      dialogOrderNoDetailVisible: false,
      exportLoading: false
    }
  },
  watch: {
    showDialog(newValue, oldValue) {
      this.searchClear = !this.searchClear
      if (newValue) {
        this.init()
      }
    },
  },
  created() {
    this.curPageConfig = this.queryType == 1 ? pageConfig.offline.realTime : pageConfig.offline.history
    this.init()
  },
  methods: {
    // 开始时间与结束时间
    getTips() {
      if ((this.listQuery.startTime !== '' && this.listQuery.startTime !== null) && (this.listQuery.endTime !== '' && this.listQuery.endTime !== null)) {
        if (this.listQuery.startTime > this.listQuery.endTime) {
          this.$notify({
            title: '时间不合理',
            message: '开始时间大于结束时间,请重新选择!',
            type: 'info',
            duration: 3500
          })
          this.listQuery.endTime = ''
        }
      }
    },
    // 时间选择器
    dateRangePickerChange(val) {
      if (!val) {
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      } else {
        this.listQuery.startTime = val[0]
        this.listQuery.endTime = val[1]
      }
    },
    renderHeader(h, { column }) {
      const content = [
        h(
          'div',
          {
            slot: 'content'
          },
          '设备区域、设备类型、设备编号'
        )
      ]

      return h('div', [
        h('span', column.label),
        h(
          'el-tooltip',
          {
            props: {
              placement: 'top'
            }
          },
          [
            content,
            h('i', {
              class: 'el-icon-warning-outline',
              style: 'color:orange;margin-left:5px;'
            })
          ]
        )
      ])
    },
    init() {
      this.listQuery = {
        page: 1,
        limit: 10,
        orderBy: null,
        order: null,
        tagId: undefined,
        shareDeviceName: null,
        shareAreaCode: this.queryType === 1 ? null : "",
        streetCode: null,
        intersectionCode: null,
        shareDeviceTypes: null,
        shareManufacturerCode: null,
        startTime: null,
        endTime: null,
        priority: null,
        shareEquipType: null,
        states: null,
        selectType: this.queryType === 1 ? 0 : 1  // 确保初始化时就设置正确的selectType
      }
      this.getList()
    },
    // 获取列表信息
    getList() {
      this.listLoading = true
      // 确保每次请求前都设置正确的selectType
      this.listQuery.selectType = this.queryType === 1 ? 0 : 1
      const api = this.queryType === 1 ? logOfflineRTList : logOfflineList

      api(this.listQuery).then(res => {
        if (res.status === 200) {
          this.total = res.data.data.total ? res.data.data.total : res.data.total
          this.dataList = this.queryType === 1 ? res.data.data.records : res.data.data
          this.listLoading = false
        }
      }).catch(err => {
        console.log('err', err)
        this.listLoading = false
      })
    },
    // 后端导出excel
    handleExportExcelServer() {
      // 处理loading
      this.exportLoading = true
      this.listQuery.isReally = 1
      this.listQuery.queryType = this.queryType
      this.listQuery.shareDeviceName = null
      this.listQuery.isSpot = 1
      this.listQuery.sysType = this.sysType || '0'
      const data = { ...this.listQuery }
      const nametype = ['', '待处理', '今日'][this.listQuery.queryType] || ''
      delete data.page
      delete data.limit
      const url = '/spotService/api/basisSpot/exportExcel'
      const title = `${nametype}告警导出报表`

      this.download({
        url,
        data,
        method: 'post',
        fileName: `${title}.xlsx`
      }, 'application/vnd.ms-excel').then(res => {
        this.exportLoading = false
      }).catch(() => {
        this.exportLoading = false
      })
    },
    handleSizeChange(val) {
      // 每页显示条目个数
      this.listQuery.limit = val
      this.getList()
    },
    handleCurrentChange(val) {
      // 上一页/下一页
      this.listQuery.page = val
      this.getList()
    },
    handleRowClick(row, event, column) {
      this.initDialog()
      this.curClickRowValue = row
      this.id = row.id
      this.deviceId = row.deviceId
      this.dialogAlarmDetailVisible = true
    },
    // 打开模态框的时候，初始化模态框位置
    initDialog() {
      setTimeout(() => {
        if (this.$refs.dialog) {
          this.$refs.dialog.$el.firstChild.style.top = 0
          this.$refs.dialog.$el.firstChild.style.left = 0
        }
        if (this.$refs.dialog1) {
          this.$refs.dialog1.$el.firstChild.style.top = 0
          this.$refs.dialog1.$el.firstChild.style.left = 0
        }
      })
    },
    handCancle(reload) {
      this.dialogAlarmDetailVisible = false
      this.dialogOrderNoDetailVisible = false
      this.searchClear = false
      this.getList()
      //  刷新大屏未处理数量
      //this.getRightOne(this.sysType)
    },
    // 定位地图
    positionMap(alarmInfo) {
      this.handCancle()
      this.curClickRowValue['latitude'] = alarmInfo.lat
      this.curClickRowValue['longitude'] = alarmInfo.lon
      if (this.curClickRowValue.latitude == null || this.curClickRowValue.longitude == null) {
        this.$message.error('该设备未获取到经纬度坐标信息，无法定位跳转到对应告警点位，请检查！')
        // 取消选中设备
        this.$emit('resetChoose', this.curClickRowValue)
        return
      }
      // 其他的一些定位地图的操作
      this.$emit('positionMap', this.curClickRowValue)
    },
    // 设备的搜索框返回的值
    handleData(data) {
      this.listQuery = Object.assign(this.listQuery, data)
    },
    // 显示工单号的窗口
    showOrderNoDialog(orderNo) {
      if (orderNo) {
        this.initDialog()
        const { href } = this.$router.resolve({
          path: `/flow/view/${orderNo}`
        })
        window.open(href, '_blank')
        // this.orderNo = orderNo;
        // this.dialogOrderNoDetailVisible = true;
      }
    },
    // 搜索
    search() {
      this.listQuery.page = 1
      this.listQuery.limit = 10
      this.listQuery.isReally = 1
      this.listQuery.queryType = this.queryType
      this.listQuery.shareDeviceName = null
      this.listQuery.isSpot = 1
      this.listQuery.sysType = this.sysType || '0'
      this.getList()
    },
    // 根据条件查询报表信息
    searchListInfo() {
      this.listQuery.page = 1
      this.getList()
    },
    // 批量关闭
    answerWarning() {
      if (this.selectDeviceIdArr.length) {
        updateOrder({
          ids: this.selectDeviceIdArr,
          type: 0
        }).then(r => {
          if (r.data.code === 200) {
            this.Alert({ alertType: 'update', message: '处理成功！' })
            this.listQuery.page = 1
            this.listQuery.limit = 10
            this.getList()
            // 刷新大屏未处理数量
            this.getRightOne(this.sysType)
          }
        })
      } else {
        this.$notify({
          title: '警告',
          message: '请选择要处理的设备',
          type: 'warning',
          duration: 2000
        })
      }
    }
  }
}
</script>
