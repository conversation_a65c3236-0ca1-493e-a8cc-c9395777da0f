import request from '@/utils/request'
export function fetchList(data) {
  return request({
    url: '/pmslinkage/api/linkage/strategy/list',
    method: 'post',
    data
  })
}

export function fetchDetail(queryData) {
  return request({
    url: '/pmslinkage/api/linkage/strategy/detail',
    method: 'get',
    params: queryData
  })
}

export function fetchDelete(ids) {
  var data = { ids }
  return request({
    url: '/pmslinkage/api/linkage/strategy/delete',
    method: 'post',
    data
  })
}

export function fetchCreate(data) {
  return request({
    url: '/pmslinkage/api/linkage/strategy/create',
    method: 'post',
    data
  })
}

export function fetchUpdate(data) {
  return request({
    url: '/pmslinkage/api/linkage/strategy/update',
    method: 'post',
    data
  })
}
export function updateLinkageEnable(data) {
  return request({
    url: '/pmslinkage/api/linkage/strategy/updateLinkageEnable',
    method: 'post',
    data
  })
}

