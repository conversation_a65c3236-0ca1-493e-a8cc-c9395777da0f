import { Notification } from 'element-ui'
export default {
  install: function(Vue, options) {
    Vue.prototype.Alert = function(option) {
      const $option = {
        title: '',
        message: '',
        type: '',
        duration: 2000,
        alertType: ''
      }
      Object.assign($option, option)
      switch ($option.alertType) {
      case 'add':
        $option.title = ($option.title === '' ? '成功' : $option.title)
        $option.message = ($option.message === '' ? '添加成功' : $option.message)
        $option.type = 'success'
        break
      case 'update':
        $option.title = ($option.title === '' ? '成功' : $option.title)
        $option.message = ($option.message === '' ? '更新成功' : $option.message)
        $option.type = 'success'
        break
      case 'action':
        $option.title = ($option.title === '' ? '成功' : $option.title)
        $option.message = ($option.message === '' ? '操作成功' : $option.message)
        $option.type = 'success'
        break
      case 'valid':
        $option.title = ($option.title === '' ? '失败' : $option.title)
        $option.message = ($option.message === '' ? '数据校验失败' : $option.message)
        $option.type = 'error'
        break
      case 'error':
        $option.title = ($option.title === '' ? '失败' : $option.title)
        $option.message = ($option.message === '' ? '操作失败' : $option.message)
        $option.type = 'error'
        break
      case 'delete':
        $option.title = ($option.title === '' ? '成功' : $option.title)
        $option.message = ($option.message === '' ? '删除成功' : $option.message)
        $option.type = 'success'
        break
      case 'nodata':
        $option.title = ($option.title === '' ? '提示' : $option.title)
        $option.message = ($option.message === '' ? '未选择记录' : $option.message)
        $option.type = 'error'
        break
      default:
        break
      }
      Notification($option)
    }
  }
}
