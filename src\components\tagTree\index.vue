<template>
  <div>
    <div v-if="treeType === 'group'" class="tag-tree">
      <div class="title">
        {{ title }}
      </div>
      <!-- v-if="deviceList" -->
      <div v-if="showEdit" class="edit">
        <i class="el-icon el-icon-circle-plus-outline" @click="add" />
        <i class="el-icon el-icon-edit" @click="edit" />
        <i class="el-icon el-icon-delete" @click="del" />
      </div>
      <div class="tree">
        <el-tree
          key="1"
          highlight-current
          node-key="tagId"
          :default-expanded-keys="openId"
          :data="groupTree"
          :props="groupProps"
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
        >
          <!-- <div slot-scope="{ node, data }" style="width: 100%">
            <span>
              <el-button type="text" size="mini" class="showname" style="text-align: left;color: #fff;">{{node.label}}</el-button>
              <el-tooltip class="item" effect="dark" :content="node.label" placement="top-start">
                <el-button type="text" size="mini" class="showname" style="text-align: left;">{{node.label}}</el-button>
              </el-tooltip>
            </span>
          </div> -->
        </el-tree>

      </div>
    </div>

    <el-dialog v-dialogDrag :visible.sync="addVisible" :title="groupTitle" width="450px">
      <!-- :deviceTypeId="deviceTypeId" -->
      <add :type="type" :checked="node" :show-dialog="addVisible" @handCancle="handCancle" />
    </el-dialog>
  </div>
</template>

<script>
import add from './add'
import { deviceTagList, deleteDeviceTag } from '@/api/otherSystem/equipmentManagement/tag'
export default {
  components: {
    add
  },
  props: {
    // 是否可以显示添加、删除修改，原本由 deviceList决定是(可以增删改标签) 不是(可以切换为区域)
    showEdit: { // 是否可以显示添加、删除修改
      type: Boolean,
      default: false
    },
    title: { // 标签名称
      type: String,
      default: '标签'
    },
    groupTreeType: {
      type: Number,
      default: 1
    }
    // deviceTypeId: { // 设备类型
    //   type: Number,
    //   required: true
    // }
  },
  data() {
    return {
      groupTitle: '',
      addVisible: false,
      data: [],
      groupTree: [],
      areaTree: [],
      nodeId: 0,
      node: {},
      openId: [],
      type: '',
      treeType: 'group',
      groupProps: {
        value: 'tagId',
        // children: 'childList',
        label: 'tagName'
      },
      areaProps: {
        value: 'groupId',
        // children: 'childAreaList',
        label: 'groupName'
      }
    }
  },
  watch: {
    groupTree(val) {
      if (val) {
        this.$nextTick(() => {
          if (this.groupTree !== null && this.groupTree.length > 0) {
            document.querySelector('.el-tree-node__content').click()
          }
        })
      }
    },
    treeType: {
      handler(val) {
        if (val === 'group') {
          if (this.groupTree !== null && this.groupTree.length > 0) {
            this.$nextTick(() => {
              document.querySelector('.el-tree-node__content').click()
            })
          }
        } else if (val === 'area') {
          if (this.areaTree !== null && this.areaTree.length > 0) {
            this.$nextTick(() => {
              document.querySelector('.el-tree-node__content').click()
            })
          }
        }
      }
    }
  },
  created() {
    this.init()
  },

  methods: {
    init() {
      // deviceTypeId: this.deviceTypeId
      deviceTagList({
        type: this.groupTreeType
      }).then(res => {
        if (res.data.code === 200) {
          const arr = []
          this.groupTree = res.data.data
          // if (this.groupTree !== null && this.groupTree.length > 0) {
          //   arr.push(this.groupTree[0].groupId == null ? -1 : this.groupTree[0].groupId)
          // }
          this.openId = arr
          this.$emit('gerTreeData', this.groupTree)
        }
      })
    },
    handleNodeClick(data) {
      this.node = data
      this.$emit('getNode', this.node)
    },
    handCancle(val) {
      if (val) {
        this.init()
      }
      this.addVisible = false
    },
    add() {
      this.type = 'add'
      this.groupTitle = '增加标签'
      this.addVisible = true
    },
    del() {
      if (this.node.isSysTag === 1) {
        this.$message.error('系统标签不允许删除')
        return
      } else {
        this.$confirm(`此操作将删除${this.node.tagName}标签, 其关联设备将失去该标签属性，是否继续?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteDeviceTag(this.node.tagId).then(res => {
            if (res.data.code === 200) {
              this.$notify({
                title: '成功',
                message: '删除成功',
                type: 'success'
              })
            }
            this.init()
          })
        })
      }
    },
    edit() {
      if (this.node.isSysGroup === 1) {
        this.$message.error('该标签不能编辑')
        return
      } else {
        this.groupTitle = '编辑标签'
        this.type = 'edit'
        this.addVisible = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  $mainColor: #579EF8;

  .tag-tree {
    .title {
      width: 95%;
      text-align: center;
      height: 4.6300vh;
      line-height: 4.6300vh;
      background-color: $mainColor;
      color: #fff;
      position: relative;
    }

    .tree {
      width: 95%;
      height: 55.5600vh;
      overflow-y: scroll;
    }

    .edit {
      width: 95%;
      height: 3.7040vh;
      background-color: #93C1FB;
      /* border-bottom: 1px solid #ddd; */
      display: flex;
      align-items: center;
      justify-content: space-around;

      .el-icon {
        font-size: 2.1298vh;
        color: #fff;
        cursor: pointer;
      }
    }
  }

  .icon-style {
    position: absolute;
    right: 0.7408vh;
    top: 0.7408vh;
    cursor: pointer;
    width: 1.4816vh;
    height: 1.4816vh;
  }

  .showname {
    width:80%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: block;
  }
  .tree /deep/ .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content{
    background-color: #F5F7FA;
  }
  .tree /deep/ .el-tree-node__content{
    height: 2.7780vh;
  }
</style>
