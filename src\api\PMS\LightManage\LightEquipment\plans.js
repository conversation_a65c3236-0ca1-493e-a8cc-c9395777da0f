import request from '@/utils/request'
// 列表
export function LampScenePlanList(data) {
  return request({
    url: '/energyService/api/lampScenePlan/LampScenePlanList',
    method: 'post',
    data
  })
}
// 删除
export function deleteLampScenePlan(data) {
  return request({
    url: '/energyService/api/lampScenePlan/deleteLampScenePlan',
    method: 'post',
    data
  })
}
// 编辑
export function updateLampScenePlan(data) {
  return request({
    url: '/energyService/api/lampScenePlan/updateLampScenePlan',
    method: 'post',
    data
  })
}
// 详情
export function getLampScenePlanInfo(data) {
  return request({
    url: '/energyService/api/lampScenePlan/getLampScenePlanInfo',
    method: 'get',
    params: data
  })
}
// 添加
export function insertLampScenePlan(data) {
  return request({
    url: '/energyService/api/lampScenePlan/insertLampScenePlan',
    method: 'post',
    data
  })
}
