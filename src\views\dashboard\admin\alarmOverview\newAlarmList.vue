<template>
  <div style="margin-top: 1vh;display: flex;width: 100%">
    <div class="jcjk-div">
      <div class="jcjk-table" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
        <!-- 固定表头 -->
        <table class="zhcd table-header" cellspacing="0" cellpadding="2">
          <thead>
          <tr class="th">
              <th style="width: 8%">序号</th>
              <th style="width: 18%">设备名称</th>
            <th style="width: 22%;">设备类型</th>
            <th style="width: 35%;">告警时间</th>
            <th style="width: 15%;">告警状态</th>
            <th style="width: 8%;">操作</th>
          </tr>
          </thead>
        </table>
        <!-- 滚动内容区 -->
        <div class="scroll-container" 
             @wheel.prevent="handleWheel"
             @mouseenter="handleMouseEnter" 
             @mouseleave="handleMouseLeave">
          <div class="scroll-wrapper" :class="{ 'scrolling': isScrolling }">
            <table class="zhcd table-body" cellspacing="0" cellpadding="2">
              <tbody>
                <tr v-for="(item, index) in displayList" :key="item.alarmId" style="cursor: pointer" @click.stop="handleDetail(item)">
                  <td style="width: 8%">{{ calculateIndex(index) }}</td>
                  <td style="width: 18%">{{ showWords(item.deviceName, 3) }}</td>
                  <td style="width: 22%">{{ showWords(item.deviceTypeName, 3) }}</td>
                  <td style="width: 35%">{{ item.alarmDate }}</td>
                  <td style="width: 15%">{{ item.deviceStatus == 0 ? '未恢复' : '已恢复' }}</td>
                  <td class="detail-cell" style="width: 8%">详情</td>
                </tr>
                <tr v-if="displayList.length === 0" style="background: none;">
                  <td colspan="6">恭喜您，暂无异常</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <!-- 添加手动滚动控制按钮 -->
        <div v-if="needScroll" class="scroll-controls">
          <div class="scroll-btn up" @click="handleManualScroll('up')" :class="{ disabled: !canScrollUp }">
            <i class="el-icon-arrow-up"></i>
          </div>
          <div class="scroll-btn down" @click="handleManualScroll('down')" :class="{ disabled: !canScrollDown }">
            <i class="el-icon-arrow-down"></i>
          </div>
        </div>
      </div>
      <div class="bg1">
        <el-dialog ref="dialog" v-dialogDrag append-to-body class="layerManager dashboard-dialog" title="告警详情"
          :visible.sync="dialogFormVisible" width="74.07vh">
          <!-- <alarmAllList :collectionId='recordId' :show-dialog="dialogFormVisible" @handCancle="handCancle"></alarmAllList> -->
          <detail :is-dark-bg="true" :device-id="deviceId" :record-id="recordId" :show-dialog="dialogFormVisible"
            @handCancle="handCancle" @positionMap="positionMap" />
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import detail from '@/views/PMS/alarmManagement/realtimeAlarm/details'
// import detail from '@/views/system/alarm/detail'
export default {
  components: {
    detail
  },
  props: {
    sysType: {
      type: Number,
      default: null
    },
  },
  inject: ['getRightOne'],
  data() {
    return {
      recordId: 0,
      deviceId: 0,
      dialogFormVisible: false,
      isHovered: false,
      scrollTimer: null,
      currentIndex: 0,
      isScrolling: false,
      displayData: [],
      animationTimer: null,
      canScrollUp: true,
      canScrollDown: true
    }
  },
  computed: {
    alarmList() { 
      return this.$store.state.user.alarmData || []
    },
    needScroll() {
      return this.alarmList && this.alarmList.length > 10
    },
    displayList() {
      if (!this.alarmList || !this.alarmList.length) return []
      if (this.displayData.length > 0) {
        return this.displayData
      }
      return this.alarmList.slice(0, Math.min(10, this.alarmList.length))
    }
  },
  watch: {
    alarmList: {
      immediate: true,
      handler(val) {
        if (val && val.length > 0) {
          this.displayData = val.slice(0, Math.min(10, val.length))
          if (this.needScroll) {
            this.startScroll()
          }
        } else {
          this.displayData = []
          this.stopScroll()
        }
      }
    }
  },
  beforeDestroy() {
    this.stopScroll()
    if (this.animationTimer) {
      clearTimeout(this.animationTimer)
    }
  },
  methods: {
    calculateIndex(index) {
      const actualIndex = this.currentIndex + index + 1
      if (actualIndex > this.alarmList.length) {
        return actualIndex - this.alarmList.length
      }
      return actualIndex
    },
    showWords(content, length) {
      if (!content) return ''
      if (content.length > length) {
        return content.substr(0, length) + '...'
      }
        return content
    },
    handleWheel(event) {
      if (!this.alarmList || this.alarmList.length <= 10) return;
      
      // 停止自动滚动定时器
      this.stopScroll();
      
      // 根据滚轮方向计算新的索引
      if (event.deltaY > 0) { // 向下滚动
        this.currentIndex = (this.currentIndex + 1) % this.alarmList.length;
      } else { // 向上滚动
        this.currentIndex = (this.currentIndex - 1 + this.alarmList.length) % this.alarmList.length;
      }
      
      // 更新显示数据
      this.updateDisplayData();
    },
    handleMouseEnter() {
      this.isHovered = true;
    },
    handleMouseLeave() {
      this.isHovered = false;
      if (this.needScroll) {
        this.startScroll();
      }
    },
    updateDisplayData() {
      if (!this.alarmList || !this.needScroll) return;
      
      this.isScrolling = true;
      
      // 计算新的数据
      const newData = [];
      
      // 修改为显示10条数据
      for (let i = 0; i < 10; i++) {
        const index = (this.currentIndex + i) % this.alarmList.length;
        newData.push(this.alarmList[index]);
      }
      
      // 动画结束后更新数据
      this.animationTimer = setTimeout(() => {
        this.isScrolling = false;
        this.displayData = newData;
      }, 300);
    },
    startScroll() {
      if (!this.needScroll || this.isHovered) return;
      this.stopScroll();

      this.scrollTimer = setInterval(() => {
        if (!this.isHovered) {
          this.currentIndex = (this.currentIndex + 1) % this.alarmList.length;
          this.updateDisplayData();
        }
      }, 3000);
    },
    stopScroll() {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
        this.scrollTimer = null;
      }
      if (this.animationTimer) {
        clearTimeout(this.animationTimer);
        this.animationTimer = null;
      }
      this.isScrolling = false;
    },
    handleDetail(row) {
      this.initDialog()
      this.deviceId = row.deviceId
      this.recordId = row.id
      this.dialogFormVisible = true
    },
    initDialog() {
      setTimeout(() => {
        if (this.$refs.dialog) {
          this.$refs.dialog.$el.firstChild.style.top = 0
          this.$refs.dialog.$el.firstChild.style.left = 0
        }
      })
    },
    handCancle(reload) {
      this.getRightOne(this.sysType)
      this.dialogFormVisible = false
    },
    positionMap(value) {
      this.handCancle()
      this.selectDevice(value)
    },
    selectDevice(deviceData) {
      if (deviceData.lat != undefined && deviceData.lon != undefined) {
        deviceData.latitude = deviceData.lat
        deviceData.longitude = deviceData.lon
      }

      if (deviceData.latitude == null || deviceData.longitude == null) {
        this.$message.error('该设备未获取到经纬度坐标信息，无法定位跳转到对应告警点位，请检查！')
        // 取消选中设备
        this.$EventBus.$emit('resetChoose', deviceData)
        return
      }
      // 显示设备
      this.$EventBus.$emit('showDevice', deviceData)
      // 选择某一个设备
      this.$EventBus.$emit('deviceData', deviceData)
    },
    handleManualScroll(direction) {
      if (direction === 'up') {
        if (this.canScrollUp) {
          this.currentIndex = (this.currentIndex - 1 + this.alarmList.length) % this.alarmList.length
          this.updateDisplayData()
        }
      } else if (direction === 'down') {
        if (this.canScrollDown) {
          this.currentIndex = (this.currentIndex + 1) % this.alarmList.length
          this.updateDisplayData()
        }
      }
    }
  }
}
</script>

<style>
.jcjk-div .el-tag--medium {
  height: 2.6vh;
  font-size: 1.06667vh;
  line-height: 2.6vh;
  background-color: #377AFF;
  color: #ffffff;
}

.jcjk-div {
  width: 100%;
}

.jcjk-table {
  height: calc(11 * 2.8vh); /* 调整总高度以适应表头+10行数据 */
  clear: both;
  overflow: hidden;
  position: relative;
  margin-left: 0.88889vh;
  margin-right: 0.35556vh;
}

/* 表头样式 */
.table-header {
  position: relative;
  z-index: 2;
  background: transparent;
  height: 3vh;
}

.table-body {
  width: 100%;
}

.zhcd {
  color: #ffffff;
  width: 100%;
  text-align: center;
  line-height: 2.5vh;
  margin: 0 auto;
  border-collapse: collapse;
}

.zhcd tr {
  height: 2.8vh;
  background: url(~@/assets/img/table-line-bg.png) no-repeat;
  background-size: 100% 100%;
  transition: transform 0.3s ease;
  display: table-row; /* 确保行正确显示 */
}

.zhcd tr.th {
  background: linear-gradient(90deg, rgba(118, 175, 249, 0) 0%, rgba(118, 175, 249, 0.0933735) 23.96%, rgba(118, 175, 249, 0.15) 51.87%, rgba(118, 175, 249, 0.0714286) 78.65%, rgba(118, 175, 249, 0) 100%) !important;
  height: 3vh;
  opacity: 1;
}

.zhcd tr:not(.th):hover {
  background: url(~@/assets/img/table-line-bg-hover.png) no-repeat;
  background-size: 100% 100%;
}

.zhcd td,
.zhcd th {
  font-size: 1.06667vh;
  padding: 0.5vh 0.5vh;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 2.8vh; /* 确保单元格高度一致 */
  line-height: 2.8vh; /* 确保文字垂直居中 */
}

.scroll-container {
  position: relative;
  overflow: hidden;
  height: calc(10 * 2.8vh);
  padding-top: 0;
}

.scroll-wrapper {
  position: absolute;
  width: 100%;
  top: 0;
  transition: transform 0.3s ease;
}

.scrolling {
  transform: translateY(-2.8vh);
}

/* 移除之前的滚动动画 */
@keyframes scrollUp {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-2.8vh);
  }
}

.detail-cell {
  color: #74B0FF;
  cursor: pointer;
}
</style>
<style scopen>
table,
table tr th,
table tr td {
  border: none
}

table {
  width: 200px;
  min-height: 25px;
  line-height: 25px;
  text-align: center
}

.th {
  background: linear-gradient(90deg, rgba(118, 175, 249, 0) 0%, rgba(118, 175, 249, 0.0933735) 23.96%, rgba(118, 175, 249, 0.15) 51.87%, rgba(118, 175, 249, 0.0714286) 78.65%, rgba(118, 175, 249, 0) 100%);
  opacity: 1;
}
</style>
