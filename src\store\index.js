/*
 * @Descripttion: 
 * @Author: wangl
 * @version: 
 * @Date: 2025-03-04 09:08:12
 * @LastEditors: wangl
 * @LastEditTime: 2025-03-14 17:36:04
 */
import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'
import errorLog from './modules/errorLog'
import permission from './modules/permission'
import app from './modules/app'
import settings from './modules/settings'
import user from './modules/user'
import createPersistedState from 'vuex-persistedstate'
import formDesign from './modules/formDesign'
import cloneDeep from 'lodash/cloneDeep'
Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    settings,
    permission,
    errorLog,
    formDesign,
    user
  },
  mutations: {
    // 重置vuex本地储存状态
    resetStore(state) {
      Object.keys(state).forEach((key) => {
        state[key] = cloneDeep(window.SITE_CONFIG['storeState'][key])
      })
    }
  },
  plugins: [createPersistedState({
    storage: window.sessionStorage,
    reducer(val) {
      return {
        // 只储存state中的permission
        permission: val.permission
      }
    }
  })],
  getters
})

export default store
