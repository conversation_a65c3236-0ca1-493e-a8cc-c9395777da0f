import request from '@/utils/request'
// 亮灯、节能率、在线数…
export function lampStaticCount(data) {
  return request({
    url: '/energyService/api/lampView/lampStaticCount',
    method: 'post',
    data
  })
}
// 用能计划
export function getLampEnergyPlanChart(data) {
  return request({
    url: '/energyService/api/lampView/getLampEnergyPlanChart',
    method: 'post',
    data
  })
}
export function getLampEnergyPlanList(data) {
  return request({
    url: '/energyService/api/lampView/getLampEnergyPlanList',
    method: 'post',
    data
  })
}

// 横向对比分析、能耗同比
export function getLampContrast(data) {
  return request({
    url: '/energyService/api/lampView/getLampContrast',
    method: 'post',
    data
  })
}
export function getLampContrastDataList(data) {
  return request({
    url: '/energyService/api/lampView/getLampContrastDataList',
    method: 'post',
    data
  })
}
// 能耗总览
export function getLampTrent(data) {
  return request({
    url: '/energyService/api/lampView/getLampTrent',
    method: 'post',
    data
  })
}
export function getLampEnergyTrendList(data) {
  return request({
    url: '/energyService/api/lampView/getLampEnergyTrendList',
    method: 'post',
    data
  })
}
