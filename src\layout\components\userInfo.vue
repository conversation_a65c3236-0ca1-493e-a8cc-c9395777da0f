<template>
  <div v-loading="listLoading">
    <el-form ref="dataForm" :rules="rules" :model="childData" label-position="right" inline label-width="11.1120vh">
      <div class="repair-container">
        <el-col :span="12">
          <el-form-item :label="$t('table.userAcc')">
            <el-input v-model="childData.userAcc" style="width:20.3720vh" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职位">
            <el-input v-model="childData.posName" style="width:20.3720vh" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="部门名称">
            <el-input v-model="childData.deptName" style="width:20.3720vh" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('table.userName')" prop="userName">
            <el-input v-model="childData.userName" disabled style="width:20.3720vh" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="gender">
            <el-radio-group v-model="gender" disabled>
              <el-radio :label="0">男</el-radio>
              <el-radio :label="1">女</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('table.mobilephone')" prop="mobilephone">
            <el-input v-model="childData.mobilephone" disabled style="width:20.3720vh" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管理平台密码" prop="userPwd" style="marginBottom:2.7780vh">
            <el-input v-model="childData.userPwd" type="password" style="width:20.3720vh" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="确认密码" prop="cifrmUserPwd" style="marginBottom:2.7780vh">
            <el-input v-model="childData.cifrmUserPwd" type="password" style="width:20.3720vh" />
          </el-form-item>
        </el-col>
      </div>
    </el-form>
    <div style="text-align: center">
      <el-button type="primary" @click="handCreate">{{ $t('button.confirm') }}</el-button>
    </div>
  </div>
</template>
<script>
import {
  fetchCreate,
  fetchDetail
} from '@/api/system/user/user'
import { updateUserPassword } from '@/api/system/user/user'
import { fetchDeptTree } from '@/api/system/dept/dept'
import { fetchList } from '@/api/system/pos/pos'
import { _isKeyBoardContinuousChar, LxStr } from '@/utils/validate'
import md5 from 'js-md5'

export default {
  props: ['keyValue', 'showDialog'],
  data() {
    var validatePass = (rule, value, callback) => {
      if (!value) {
        callback()
      } else {
        var reg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[$@#!%*?&._])[A-Za-z\d$@#!%*?&._]{8,16}/
        if (value.length < 8 || value.length > 16) {
          callback(new Error('密码长度必须为8~16位'))
        } else if (!reg.test(value)) {
          callback(new Error('密码须包含大小写字母、数字、特殊字符'))
        } else if (_isKeyBoardContinuousChar(value)) {
          callback(new Error('密码设置应避免3位以上（含3位）键盘排序密码,如qwe、qaz、123'))
        } else if (!LxStr(value)) {
          callback(new Error('数字不能连续三位'))
        } else {
          callback()
        }
      }
    }
    var validatePass2 = (rule, value, callback) => {
      if (this.childData.userPwd && !value) {
        callback('请输入确认密码')
      } else if (value && value !== this.childData.userPwd) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      activeName: 'mima',
      tabRefresh: {
        mima: true,
        quanxian: false
      },
      options: [],
      postOption: [],
      userStateOption: [{
        value: 1,
        label: '禁用'
      },
      {
        value: 0,
        label: '启用'
      }
      ],
      rules: {
        userName: [
          { required: true, message: '请输入用户名', trigger: 'change' }
        ],
        mobilephone: [
          { required: true, message: '请输入手机号', trigger: 'change' }
        ],
        userState: [
          { required: true, message: '请选择用户状态', trigger: 'change' }
        ],
        gender: [
          { required: true, message: '请选择性别', trigger: 'blur' }
        ],
        userPwd: [{ required: false, message: '请输入管理平台密码', trigger: 'blur' }, { validator: validatePass, trigger: 'blur' }],
        cifrmUserPwd: [
          { required: false, message: '请输入确认密码', trigger: 'change' },
          { validator: validatePass2, trigger: 'blur' }
        ]
      },
      propstree: {
        label: 'deptName',
        value: 'deptId',
        children: 'childDept'
      },
      userId: 0,
      gender: 0,
      listLoading: false,
      childData: {}
    }
  },
  watch: {
    showDialog(n, o) {
      if (n) {
        this.options = []
        this.postOption = []
        this.$refs['dataForm'].resetFields()
        if (this.showDialog) {
          this.init()
        }
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.options = []
      this.postOption = []
      this.loadDeptTree()
      this.loadPostList()
      this.loadData()
    },
    loadData() {
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      if (this.keyValue > 0) {
        const queryData = {
          userId: this.keyValue
        }
        const upDeptIds = []
        fetchDetail(queryData).then(response => {
          this.childData = response.data.data
          this.childData.userPwd = ''
          if (this.childData.upIdList.length > 0) {
            this.childData.upIdList.forEach(e => {
              upDeptIds.push(e.toString())
            })
          }
          this.gender = this.childData.gender
          if (this.childData.deptId !== null) {
            upDeptIds.push(this.childData.deptId.toString())
          }
          this.childData.upIdList = upDeptIds
        })
      } else {
        this.userId = 0
        this.childData = {}
        this.gender = 0
      }
    },
    loadDeptTree() {
      const treeQuery = {}
      fetchDeptTree(treeQuery).then(response => {
        this.options = response.data.data
      })
    },
    loadPostList() {
      const treeQuery = {}
      fetchList(treeQuery).then(response => {
        this.postOption = response.data.data
      })
    },
    handCreate() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.childData.deptId = this.childData.upIdList[
            this.childData.upIdList.length - 1
          ]
          delete this.childData.upIdList
          this.childData.gender = this.gender
          if (this.keyValue > 0) {
            if (this.childData.userPwd) {
              const loginFormClone = JSON.parse(JSON.stringify(this.childData))
              loginFormClone.userPwd = md5(loginFormClone.userPwd)
              updateUserPassword(this.childData.userId, loginFormClone.userPwd)
              this.childData.userPwd = ''
              this.childData.cifrmUserPwd = ''
            }
            delete this.childData.userPwd
            delete this.childData.cifrmUserPwd
            this.childData.isChangePwd = 1
            this.$emit('handCancle', true)
            this.Alert({ alertType: 'update' })
            // fetchUpdate(this.childData).then(() => {
            //     this.$emit('handCancle', true)
            //     this.Alert({ alertType: 'update' })
            // })
          } else {
            fetchCreate(this.childData).then(() => {
              this.$emit('handCancle', true)
              this.Alert({ alertType: 'add' })
            })
          }
        } else {
          this.Alert({ alertType: 'valid' })
        }
      })
    },
    handCancle() {
      this.$emit('handCancle', false)
    }
  }
}
</script>

<style>
.el-checkbox + .el-checkbox {
  margin-left: 0vh;
}

.el-checkbox {
  margin-left: 0vh;
}
</style>
