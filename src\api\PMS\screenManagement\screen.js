import request from '@/utils/request'

// 查询电子屏数据列表
export function fetchList(data) {
  return request({
    url: '/szdlService/api/deviceManage/ledList',
    method: 'post',
    data
  })
}
// 详情
export function fetchDetail(data) {
  return request({
    url: '/pmsService/api/garageCarUser/get',
    method: 'post',
    data
  })
}
// 电子屏-关闭屏幕
export function fetchClose(data) {
  return request({
    url: '/szdlService/api/deviceManage/closeEleScreen',
    method: 'post',
    data
  })
}
// 电子屏-打开屏幕
export function fetchOpen(data) {
  return request({
    url: '/szdlService/api/deviceManage/openEleScreen',
    method: 'post',
    data
  })
}
// 网页端-电子屏下发节目
export function disEleScreenOnWeb(data) {
  return request({
    url: '/szdlService/api/deviceManage/disEleScreenOnWeb',
    method: 'post',
    data
  })
}
// 详情-操作记录
export function queryScreenTask(params) {
  return request({
    url: '/devService/api/deviceAttribute/queryScreenTask',
    method: 'get',
    params
  })
}
// 详情-节目列表
export function getProgramByDeviceId(data) {
  return request({
    url: '/programService/api/v1/screenrelation/getProgramByDeviceId',
    method: 'post',
    data
  })
}
