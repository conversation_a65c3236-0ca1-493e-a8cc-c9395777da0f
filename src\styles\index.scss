@import "./variables.scss";
@import "./mixin.scss";
@import "./transition.scss";
@import "./element-ui.scss";
@import "./sidebar.scss";
@import "./element-variables.scss";

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
  overflow: hidden;
  // background-color: #191E32;
  // background: url(/static/img/bgh.png) round !important;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

// main-container global css
.app-container {
  padding: 1.8520vh;
  margin: 1.8520vh;
  // background: rgba(12, 52, 117, 1);
  overflow: auto;
  height: 90vh;
  background-color: rgba(0, 142, 254, 0.2); // Updated to match Figma
  .custom{
    background-color: rgba(12, 52, 117, 1) !important; // Updated to match Figma
    .header{
      background-color: rgba(9, 72, 148, 1) !important; // Updated to match Figma
      color: #fff !important;
    }
  }
}


// 覆盖默认 css
.el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
  background: linear-gradient(180deg, rgba(0, 145, 250, 1) 0%, rgba(9, 72, 148, 1) 100%) !important;
  border: 2px solid rgba(0, 142, 254, 1) !important; // Reduced border width to prevent overflow
  color: #ffffff !important;
  box-shadow: 0 0 15px rgba(0, 142, 254, 0.8), inset 0 0 8px rgba(255, 255, 255, 0.2); // Reduced shadow intensity
  position: relative;
  z-index: 10;
  transform: translateY(-1px); // Reduced vertical movement to prevent overflow
  font-weight: bold;
  margin: 1px; // Add small margin to contain the glow effect
}

.el-tabs--card>.el-tabs__header .el-tabs__item {
  background: linear-gradient(180deg, rgba(12, 52, 117, 0.8) 0%, rgba(5, 84, 159, 0.8) 100%) !important;
  border: 1px solid rgba(0, 142, 254, 0.4) !important;
  color: #a0a3a8;
}
.el-tabs--card>.el-tabs__header .el-tabs__nav{
  border-bottom: 1px solid rgba(0, 142, 254, 0.6);
}
.el-table thead th {
  background: rgba(9, 72, 148, 1) !important; // Updated to match Figma
  color: #ffffff !important; // Changed to white as requested
  font-weight: 600 !important; // Made bolder than table content
  border: 0.0926vh solid rgba(0, 142, 254, 0.4) !important; // Updated to match Figma
}

.bg1 .el-table thead th {
  border: unset !important;
}
.bg1 .el-table tr {
  background: rgba(12, 52, 117, 1) !important; // Updated to match Figma
}
.bg1 .el-input--medium .el-input__inner {
  background-color: rgba(9, 72, 148, 1) !important; // Updated to match Figma
}
.el-button--primary {
  color: #ffffff !important;
  background: rgba(0, 142, 254, 1) !important; // Updated to match user preference
  border: 0.0926vh solid rgba(0, 142, 254, 1) !important; // Updated to match user preference
}

/* Ensure all backstage buttons use consistent blue color */
.app-container .el-button,
.calendar-list-container .el-button,
.filter-container .el-button {
  background: rgba(0, 142, 254, 1) !important;
  border-color: rgba(0, 142, 254, 1) !important;
  color: #ffffff !important;
}

/* Override for reset and clear buttons - higher specificity */
.app-container .el-button--success,
.calendar-list-container .el-button--success,
.filter-container .el-button--success {
  background: rgba(150, 191, 222, 0.4) !important;
  border-color: rgba(150, 191, 222, 0.4) !important;
  color: #fff !important;
}

/* Specific override for custom query condition buttons */
.header-search .el-button[style*="rgba(150, 191, 222, 0.4)"],
.el-button[style*="rgba(150, 191, 222, 0.4)"] {
  background: rgba(150, 191, 222, 0.4) !important;
  border-color: rgba(150, 191, 222, 0.4) !important;
  color: #fff !important;
}

/* Ultimate override for custom query buttons */
body .el-button[style*="自定义查询条件"],
body .header-search .el-button {
  &[style*="rgba(150, 191, 222, 0.4)"] {
    background-color: rgba(150, 191, 222, 0.4) !important;
    border-color: rgba(150, 191, 222, 0.4) !important;
    color: #fff !important;
  }
}

/* Specific styling for work order management buttons */
.group-search .el-button,
.table-button .el-button {
  background: rgba(0, 142, 254, 1) !important;
  border-color: rgba(0, 142, 254, 1) !important;
  color: #ffffff !important;
}
.el-button--danger {
  color: #ffffff;
}
//分页
.el-pagination.is-background .el-pager li:not(.disabled).active {
  color: #ffffff;
  background-color: rgba(0, 142, 254, 1) !important; // Changed to blue as requested
}
.el-pagination.is-background .el-pager li{
  background-color: transparent !important;
  color: rgba(255, 255, 255, 0.8) !important; // Make unselected pages more visible
}
.el-pagination.is-background .el-pager li:not(.disabled):hover {
  color: rgba(0, 142, 254, 1) !important; // Add hover effect for better UX
}
.el-pagination button:disabled{
  background-color: transparent !important;
  color: rgba(255, 255, 255, 0.4) !important; // Make disabled buttons visible but dimmed
}
.el-pager li {
  background-color: transparent !important;
  color: rgba(255, 255, 255, 0.8) !important; // Make unselected pages more visible
}
.el-pager li:not(.disabled):hover {
  color: rgba(0, 142, 254, 1) !important; // Add hover effect
}
// Ensure active page is always blue regardless of background attribute
.el-pager li.active {
  background-color: rgba(0, 142, 254, 1) !important; // Blue background for active page
  color: #ffffff !important; // White text on blue background
}
.el-pagination .el-pager li.active {
  background-color: rgba(0, 142, 254, 1) !important; // Blue background for active page
  color: #ffffff !important; // White text on blue background
}
// Global pagination text colors for better visibility
.el-pagination__total,
.el-pagination__jump {
  color: rgba(255, 255, 255, 0.9) !important;
}
.el-pagination .btn-prev,
.el-pagination .btn-next {
  color: rgba(255, 255, 255, 0.8) !important;
}
.el-pagination .btn-prev:hover,
.el-pagination .btn-next:hover {
  color: rgba(0, 142, 254, 1) !important;
}

// Additional styles for pagination without background attribute
.el-pagination:not(.is-background) .el-pager li {
  color: rgba(255, 255, 255, 0.8) !important;
}
.el-pagination:not(.is-background) .el-pager li.active {
  background-color: rgba(0, 142, 254, 1) !important;
  color: #ffffff !important;
}
.el-pagination:not(.is-background) .el-pager li:hover {
  color: rgba(0, 142, 254, 1) !important;
}

// High specificity selectors to override Element UI defaults
.el-pagination .el-pager li.number.active {
  background-color: rgba(0, 142, 254, 1) !important;
  color: #ffffff !important;
  border-color: rgba(0, 142, 254, 1) !important;
}

.el-pagination.is-background .el-pager li.number.active {
  background-color: rgba(0, 142, 254, 1) !important;
  color: #ffffff !important;
  border-color: rgba(0, 142, 254, 1) !important;
}

// Ultimate override for active pagination - highest specificity
body .el-pagination .el-pager li.active,
body .el-pagination .el-pager li.number.active,
body .el-pagination.is-background .el-pager li.active,
body .el-pagination.is-background .el-pager li.number.active {
  background-color: rgba(0, 142, 254, 1) !important;
  color: #ffffff !important;
  border-color: rgba(0, 142, 254, 1) !important;
}
.bg1 .el-table--striped .el-table__body tr.el-table__row--striped td {
  background: rgba(9, 72, 148, 1) !important; // Updated to match Figma
}
.el-textarea__inner{
  background-color: rgba(9, 72, 148, 1) !important; // Updated to match Figma
}
.el-tree{
  background-color: rgba(9, 72, 148, 1) !important; // Updated to match Figma
}
.el-transfer-panel {
  background-color: rgba(9, 72, 148, 1) !important; // Updated to match Figma
}
/* 修改指定页按钮背景颜色 */
.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev{
  background-color: transparent !important; 
}
.el-pagination .btn-prev,
.el-pagination .btn-next{
  background-color: transparent !important;
}
.el-table__body tr.current-row > td {
  background-color: rgba(9, 72, 148, 1) !important; // Updated to match Figma
}
.el-table__body tr:hover > td {
  background-color: rgba(0, 142, 254, 0.5) !important; // Updated to match Figma
}

// Removed conflicting dialog header styles - using the gradient styles below instead
.el-input__inner {
  background: linear-gradient(180deg, rgba(4, 104, 192, 0.8) 0%, rgba(5, 84, 159, 0.8) 100%);
  border: 1px solid rgba(0, 142, 254, 0.6);
  color: #eee;
}
.el-input__inner:focus {
  border-color: rgba(0, 142, 254, 1);
  box-shadow: 0 0 8px rgba(0, 142, 254, 0.5);
}
.el-select {
  //改颜色  全局下拉选择器修改
  .el-select-dropdown {
    border: 1px solid rgba(0, 142, 254, 0.6);
    background: linear-gradient(180deg, rgba(4, 104, 192, 0.9) 0%, rgba(5, 84, 159, 0.9) 100%);
    box-shadow: 0 4px 12px rgba(0, 142, 254, 0.3);
  }
  //输入框
  .el-input__inner {
    color: #eee;
    border: 1px solid rgba(0, 142, 254, 0.6);
    background: linear-gradient(180deg, rgba(4, 104, 192, 0.8) 0%, rgba(5, 84, 159, 0.8) 100%);
  }
  //聚焦时的样式
  .el-select .el-input.is-focus .el-input__inner {
    border-color: rgba(0, 142, 254, 1);
    background: linear-gradient(180deg, rgba(0, 145, 250, 0.8) 0%, rgba(9, 72, 148, 0.8) 100%);
    color: #00d3e9;
    box-shadow: 0 0 8px rgba(0, 142, 254, 0.5);
  }
  //下拉框选中
  .el-select-dropdown__item {
    color: #eee;
    background-color: transparent;
  }
  //鼠标经过下拉框
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    color: #00d3e9;
    background: linear-gradient(90deg, rgba(0, 142, 254, 0.6) 0%, rgba(0, 145, 250, 0.4) 100%);
  }
  //选中的下拉框项
  .el-select-dropdown__item.selected {
    color: #ffffff;
    background: linear-gradient(90deg, rgba(0, 145, 250, 0.8) 0%, rgba(9, 72, 148, 0.8) 100%);
  }
}
/* 增强弹窗关闭按钮的可见性和交互效果 */
.el-dialog__headerbtn .el-dialog__close,
.bg1 .el-dialog__headerbtn .el-dialog__close {
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 18px !important; // Make close button larger
  font-weight: bold !important;
  background: rgba(0, 142, 254, 0.3) !important; // Add subtle blue background
  border-radius: 50% !important; // Make it circular
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s ease !important;
  border: 1px solid rgba(0, 142, 254, 0.5) !important;
}

.el-dialog__headerbtn .el-dialog__close:hover,
.bg1 .el-dialog__headerbtn .el-dialog__close:hover {
  color: #ffffff !important;
  background: rgba(0, 142, 254, 0.6) !important; // Brighter blue on hover
  border-color: rgba(0, 142, 254, 1) !important;
  box-shadow: 0 0 8px rgba(0, 142, 254, 0.8) !important; // Add glow effect
  transform: scale(1.1) !important; // Slight scale on hover
}

.el-dialog__headerbtn {
  top: 15px !important; // Adjust position for better visibility
  right: 15px !important;
}
//后台 dialog 新修改后台样式
.el-dialog__header {
  background: linear-gradient(180deg, rgba(0, 85, 152, 0.6) 16.33%, rgba(0, 142, 254, 0.6) 100%) !important;
  .el-dialog__title {
    color: #ffffff !important;
  }
}
.el-dialog__body{
  background: linear-gradient(180deg, rgba(4, 104, 192, 1) 0%, rgba(5, 84, 159, 1) 100%) !important;
  //color: rgba(255, 255, 255, 0.9) !important; // Make dialog text lighter and more visible
}

/* 修复弹窗内深灰色文字可见性问题 */
.el-dialog__body .el-form-item__label,
.el-dialog__body .el-text,
.el-dialog__body p{
  color: rgba(255, 255, 255, 0.9) !important; // Make all dialog text lighter
}

/* 特殊处理可能的深色文字 */
.el-dialog__body .text-muted,
.el-dialog__body .text-secondary,
.el-dialog__body .el-form-item__content {
  color: rgba(255, 255, 255, 0.85) !important; // Slightly dimmed but still visible
}

/* 全面修复弹窗内所有可能的深色文字 */
.el-dialog .el-tabs__content,
.el-dialog .el-tab-pane,
.el-dialog .el-table,
.el-dialog .el-table td,
.el-dialog .el-table th,
.el-dialog .el-descriptions-item__label,
.el-dialog .el-descriptions-item__content,
.el-dialog .el-card__body,
.el-dialog .el-alert__content,
.el-dialog .el-message,
.el-dialog .el-notification {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* 确保所有弹窗内的文本都是浅色 */
.el-dialog *:not(.el-input__inner):not(.el-textarea__inner):not(.el-tag) {
  color: rgba(255, 255, 255, 0.9) !important;
}
/* 标签页样式 */
.el-tabs__item {
  color: #a0a3a8;
  background-color: rgba(12, 52, 117, 0.8);
  border: 1px solid rgba(0, 142, 254, 0.3);
}
.el-tabs__item:hover {
  color: #00d3e9;
  background-color: rgba(0, 142, 254, 0.3);
}
.el-tabs__item.is-active {
  color: #ffffff !important;
  background: linear-gradient(180deg, rgba(0, 145, 250, 1) 0%, rgba(9, 72, 148, 1) 100%) !important;
  border: 2px solid rgba(0, 142, 254, 1) !important;
  box-shadow: 0 0 15px rgba(0, 142, 254, 0.8), inset 0 0 8px rgba(255, 255, 255, 0.2); // Reduced shadow
  transform: translateY(-1px); // Reduced vertical movement
  z-index: 10;
  position: relative;
  margin: 1px; // Add margin to contain effects
}

.el-tabs__active-bar {
  background-color: transparent !important; // Hide the active bar line segment
  display: none !important; // Completely hide the ::after line segment
}

// Hide all ::after pseudo-elements that create line segments under tabs
.el-tabs__item::after {
  display: none !important;
}

.el-tabs__nav-wrap::after {
  display: none !important;
}
/* 表格样式 */
.el-table {
  background-color: rgba(12, 52, 117, 1); // Updated to match Figma
  color: #ffffff;
  border: none;
  font-size: 1.23vh !important;
  border-radius: 0.7408vh; /* 设置表格的 border-radius */
  overflow: hidden; /* 确保圆角效果 */
}
.el-table th {
  background-color: rgba(9, 72, 148, 1); // Updated to match Figma
  color: #ffffff !important; // Ensure white color
  font-weight: 600 !important; // Made bolder than table content
  font-size: 1.23vh !important;
  border-bottom: 0.0926vh solid rgba(0, 142, 254, 0.4); // Updated to match Figma
}

.el-table td {
  border-bottom: 0.0926vh solid rgba(0, 142, 254, 0.4); // Updated to match Figma
  border-left: none; /* 去除左边框 */
  border-right: none; /* 去除右边框 */
  font-weight: 400 !important; /* Normal weight for table content */
}
.el-table tr {
  background-color: rgba(12, 52, 117, 1); // Updated to match Figma
}
.el-table__body tr.hover-row>td {
  background-color: rgba(0, 142, 254, 0.5) !important; // Updated to match Figma
}
.el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: rgba(0, 142, 254, 0.5); // Updated to match Figma
}
.el-table--striped .el-table__body tr.el-table__row--striped td {
  /* background: #FAFAFA; */
  background-color: rgba(9, 72, 148, 1); // Updated to match Figma
}
.el-table::before, .el-table--group::after, .el-table--border::after{
  background-color: unset !important;
  border: none !important;
}
.el-table__fixed-right::before, .el-table__fixed::before{
  background-color: unset !important;
  border: none !important;
}
// Removed conflicting style - using the display: none rule below instead
.el-button--success {
   background-color: rgba(150, 191, 222, 0.4) !important; // Updated to match user preference
   border: 0.0926vh solid rgba(150, 191, 222, 0.4) !important; // Updated to match user preference
   color: #fff !important; // Ensure text is white
  }

/* Specific styling for reset and clear buttons - using class-based targeting */
  .el-date-editor .el-range-input {
    background: linear-gradient(180deg, rgba(4, 104, 192, 0.8) 0%, rgba(5, 84, 159, 0.8) 100%) !important;
    color: #eee !important;
  }
.el-drawer__body{
  background: linear-gradient(180deg, rgba(4, 104, 192, 1) 0%, rgba(5, 84, 159, 1) 100%) !important;
}
.el-input-number__increase,.el-input-number__decrease{
  background-color: rgba(12, 52, 117, 1) !important; // Updated to match Figma
}
.el-dialog__footer{
  background: linear-gradient(180deg, rgba(5, 84, 159, 1) 0%, rgba(4, 104, 192, 1) 100%) !important;
  border-top: 1px solid rgba(0, 142, 254, 0.6);
}
.el-form-item__content{
  color: #fff !important;
} 

.el-date-editor .el-range-input{
  color: #eee !important;
}
.el-input__inner{
  color: #eee !important;
}
.el-input.is-disabled .el-input__inner{
  border-color: rgba(0, 142, 254, 0.4); // Updated to match Figma
    background-color: rgba(12, 52, 117, 0.8); // Updated to match Figma
}
.el-textarea__inner{
  color: #eee !important;
}
// 添加加载动画的背景色
.el-loading-mask {
  background-color: rgba(0, 0, 0, 0.1) !important; // 修改为您想要的颜色
}
.el-form-item__content{
  color: #fff !important;
} 
.el-form-item__label {
  color: #fff !important;
}
//上传图片提示字体
.el-upload__tip{
  color: #fff !important;
}
.el-upload-list__item-name{
  color: #fff !important;
}
// dashboard dialog
.dashboard-dialog {
  background: linear-gradient(180deg, rgba(0, 142, 254, 0.2) 0%, rgba(0, 85, 152, 0.3) 100%);

  .el-button--primary {
    background: linear-gradient(180deg, rgba(0, 145, 250, 1) 0%, rgba(9, 72, 148, 1) 100%) !important;
    border: 1px solid rgba(0, 142, 254, 1) !important;
    box-shadow: 0 2px 8px rgba(0, 142, 254, 0.4);
  }
  .select-container {
    border: 1px solid rgba(0, 142, 254, 0.6);
    background: linear-gradient(180deg, rgba(4, 104, 192, 0.8) 0%, rgba(5, 84, 159, 0.8) 100%);
  }
  .el-dialog {
    box-shadow: 0 0 20px rgba(0, 142, 254, 0.8);
    background: linear-gradient(180deg, rgba(0, 85, 152, 0.6) 16.33%, rgba(0, 142, 254, 0.6) 100%);
    border: 1px solid rgba(0, 142, 254, 0.8);

    .el-dialog__header {
      height: 5.5560vh;
      padding-left: 2%;
      padding-top: 2.5928vh;
      background: linear-gradient(180deg, rgba(0, 85, 152, 0.8) 16.33%, rgba(0, 142, 254, 0.8) 100%) !important;
      border-bottom: 1px solid rgba(0, 142, 254, 0.6);

      .el-dialog__title {
        color: #fff !important;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
      }
    }

    .el-dialog__body {
      background: linear-gradient(180deg, rgba(4, 104, 192, 1) 0%, rgba(5, 84, 159, 1) 100%) !important;
      .el-table {
        border: none;
        color: #fff;

        tr {
          background-color: rgba(12, 52, 117, 1); // Updated to match Figma
        }
        tr:hover {
          background-color: rgba(0, 142, 254, 0.5); // Updated to match Figma
        }
        td {
          border: none;
          background-color: rgba(12, 52, 117, 1); // Updated to match Figma
        }
        .el-table__body {
          background-color: rgba(12, 52, 117, 1); // Updated to match Figma
        }
        .el-table__body tr:hover > td {
          background-color: rgba(0, 142, 254, 0.5); // Updated to match Figma
        }
        th {
          border: none !important;
          color: #fff !important;
          font-weight: 600 !important; // Made bolder than table content
          background: rgba(0, 142, 254, 0.5) !important; // Updated to match Figma
        }
        .el-table__empty-block {
          background-color: rgba(12, 52, 117, 1); // Updated to match Figma
        }
        .el-loading-mask {
          background-color: rgba(12, 52, 117, 1); // Updated to match Figma
        }
        .el-table-config {
          display: none;
        }
      }
      .el-table::before {
        height: 0vh;
      }
      .el-table--border::after {
        width: 0;
      }
      .el-radio-group {
        margin-left: 0.9260vh;
        .el-radio-button__inner {
          background: rgba(9, 72, 148, 1) !important; // Updated to match Figma
          color: #fff !important;
        }
      }

      .deviceDetailTtiles {
        color: #fff;
        padding-left: 5.5560vh;
        line-height: 3.1484vh;
        border-color: rgba(0, 142, 254, 1); // Updated to match Figma
      }
      .deviceDetailTtiles::before {
        width: 3.7966vh;
        height: 1.2964vh;
        background: url("/static/img/title-preicon.svg#svgView(preserveAspectRatio(none))")
          no-repeat;
        background-size: 100% 100%;
      }

      .el-tabs--card > .el-tabs__header {
        border: none;
      }

      .el-pagination {
        .el-pagination__total,
        .el-pagination__jump {
          color: rgba(255, 255, 255, 0.9) !important;
        }

        .el-pager li,
        .btn-prev,
        .btn-next {
          background-color: rgba(9, 72, 148, 1); // Updated to match Figma
          color: rgba(255, 255, 255, 0.8) !important; // Make unselected pages more visible
        }

        .el-pager li:not(.disabled):hover,
        .btn-prev:hover,
        .btn-next:hover {
          color: rgba(0, 142, 254, 1) !important; // Add hover effect
          background-color: rgba(0, 142, 254, 0.2) !important;
        }

        .el-pager li.active {
          border: 0.0926vh solid rgba(0, 142, 254, 1); // Updated to match Figma
          background-color: rgba(0, 142, 254, 1) !important; // Changed to blue as requested
          color: #ffffff !important; // Ensure active page is highly visible
        }

        .el-pager li.disabled {
          color: rgba(255, 255, 255, 0.3) !important; // Make disabled pages dimmed but visible
        }
      }

      input,
      textarea,
      .el-date-editor {
        background-color: transparent;
        border: 0.0926vh solid rgba(0, 142, 254, 0.4); // Updated to match Figma
        color: #fff;

        input {
          border: none;
        }
      }

      .el-input {
        .el-input-group__append {
          background-color: #00000000;
          border: 0.0926vh solid rgba(0, 142, 254, 0.4); // Updated to match Figma
        }
      }
    }

    .el-form-item__label {
      color: #fff;
    }

    .el-tabs__nav {
      border: none;

      .el-tabs__item {
        border: none !important;
        background-color: rgba(9, 72, 148, 1) !important; // Updated to match Figma
      }

      .is-active {
        // background-color: #01ffff !important;
        border: 0.0926vh solid rgba(0, 142, 254, 1) !important; // Updated to match Figma
      }
    }
  }
}
/* 级联选择器 样式*/
.el-cascader .el-input .el-input__inner{
  height: 3.33333vh !important;
  background: linear-gradient(180deg, rgba(4, 104, 192, 0.8) 0%, rgba(5, 84, 159, 0.8) 100%);
  border: 1px solid rgba(0, 142, 254, 0.6);
  color: #eee;
}

/* 级联选择器下拉面板 */
.el-cascader-panel {
  background: linear-gradient(180deg, rgba(4, 104, 192, 0.9) 0%, rgba(5, 84, 159, 0.9) 100%);
  border: 1px solid rgba(0, 142, 254, 0.6);
}

.el-cascader-menu {
  background: linear-gradient(180deg, rgba(4, 104, 192, 0.9) 0%, rgba(5, 84, 159, 0.9) 100%);
  border-right: 1px solid rgba(0, 142, 254, 0.4);
}

.el-cascader-node {
  color: #eee;
}

.el-cascader-node:hover {
  background: linear-gradient(90deg, rgba(0, 142, 254, 0.6) 0%, rgba(0, 145, 250, 0.4) 100%);
  color: #00d3e9;
}

.el-cascader-node.is-active {
  background: linear-gradient(90deg, rgba(0, 145, 250, 0.8) 0%, rgba(9, 72, 148, 0.8) 100%);
  color: #ffffff;
}

/* 下拉菜单样式 */
.el-dropdown-menu {
  background: linear-gradient(180deg, rgba(4, 104, 192, 0.9) 0%, rgba(5, 84, 159, 0.9) 100%);
  border: 1px solid rgba(0, 142, 254, 0.6);
  box-shadow: 0 4px 12px rgba(0, 142, 254, 0.3);
}

.el-dropdown-menu__item {
  color: #eee;
}

.el-dropdown-menu__item:hover {
  background: linear-gradient(90deg, rgba(0, 142, 254, 0.6) 0%, rgba(0, 145, 250, 0.4) 100%);
  color: #00d3e9;
}

/* 弹出框样式 */
.el-popover {
  background: linear-gradient(180deg, rgba(4, 104, 192, 0.9) 0%, rgba(5, 84, 159, 0.9) 100%) !important;
  border: 1px solid rgba(0, 142, 254, 0.6) !important;
  color: #eee !important;
}

/* 强化所有下拉框样式 - 确保统一的渐变蓝色 */
.el-select-dropdown,
.el-cascader-panel,
.el-dropdown-menu,
.el-popover,
.el-picker-panel,
.el-autocomplete-suggestion,
.el-time-panel,
.el-date-picker {
  background: linear-gradient(180deg, rgba(4, 104, 192, 0.9) 0%, rgba(5, 84, 159, 0.9) 100%) !important;
  border: 1px solid rgba(0, 142, 254, 0.6) !important;
  box-shadow: 0 4px 12px rgba(0, 142, 254, 0.3) !important;
}

/* 确保所有下拉框选项使用统一样式 */
.el-select-dropdown__item,
.el-cascader-node,
.el-dropdown-menu__item,
.el-autocomplete-suggestion__item,
.el-time-spinner__item,
.el-picker-panel__content .el-date-table td {
  color: #eee !important;
  background-color: transparent !important;
}

/* 悬停效果统一 */
.el-select-dropdown__item:hover,
.el-cascader-node:hover,
.el-dropdown-menu__item:hover,
.el-autocomplete-suggestion__item:hover,
.el-time-spinner__item:hover,
.el-picker-panel__content .el-date-table td:hover {
  background: linear-gradient(90deg, rgba(0, 142, 254, 0.6) 0%, rgba(0, 145, 250, 0.4) 100%) !important;
  color: #00d3e9 !important;
}

/* 选中状态统一 */
.el-select-dropdown__item.selected,
.el-cascader-node.is-active,
.el-dropdown-menu__item.is-active,
.el-autocomplete-suggestion__item.highlighted,
.el-time-spinner__item.active,
.el-picker-panel__content .el-date-table td.current {
  background: linear-gradient(90deg, rgba(0, 145, 250, 0.8) 0%, rgba(9, 72, 148, 0.8) 100%) !important;
  color: #ffffff !important;
}

/* 强化下拉框文字颜色 - 特别针对数字文本如"10s", "20s"等 */
.el-select-dropdown__item span,
.el-select-dropdown__item div,
.el-select-dropdown__item,
.el-dropdown-menu__item span,
.el-dropdown-menu__item div,
.el-dropdown-menu__item {
  color: rgba(255, 255, 255, 0.9) !important; // 确保所有下拉框文字都是浅色
}

/* 针对可能的深色数字文本 */
.el-select-dropdown__item .el-option__text,
.el-select-dropdown__item .el-option__label,
.el-dropdown-menu__item .text,
.el-dropdown-menu__item .label {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* 最高优先级选择器 - 确保覆盖所有Element UI默认样式 */
body .el-select-dropdown,
body .el-cascader-panel,
body .el-dropdown-menu,
body .el-popover,
body .el-picker-panel,
body .el-autocomplete-suggestion,
body .el-time-panel,
body .el-date-picker {
  background: linear-gradient(180deg, rgba(4, 104, 192, 0.9) 0%, rgba(5, 84, 159, 0.9) 100%) !important;
  border: 1px solid rgba(0, 142, 254, 0.6) !important;
  box-shadow: 0 4px 12px rgba(0, 142, 254, 0.3) !important;
}

/* 覆盖任何可能的白色背景 */
body .el-select-dropdown.el-popper,
body .el-cascader-panel.el-popper,
body .el-dropdown-menu.el-popper,
body .el-popover.el-popper,
body .el-picker-panel.el-popper {
  background: linear-gradient(180deg, rgba(4, 104, 192, 0.9) 0%, rgba(5, 84, 159, 0.9) 100%) !important;
  border: 1px solid rgba(0, 142, 254, 0.6) !important;
}

/* 特殊处理一些可能遗漏的下拉框组件 */
.el-select__popper,
.el-cascader__dropdown,
.el-autocomplete__popper,
.el-time-picker__popper,
.el-date-picker__popper {
  background: linear-gradient(180deg, rgba(4, 104, 192, 0.9) 0%, rgba(5, 84, 159, 0.9) 100%) !important;
  border: 1px solid rgba(0, 142, 254, 0.6) !important;
  box-shadow: 0 4px 12px rgba(0, 142, 254, 0.3) !important;
}

/* 确保所有popper类型的下拉框都使用渐变蓝色 */
.el-popper[x-placement^="bottom"],
.el-popper[x-placement^="top"],
.el-popper[x-placement^="left"],
.el-popper[x-placement^="right"] {
  background: linear-gradient(180deg, rgba(4, 104, 192, 0.9) 0%, rgba(5, 84, 159, 0.9) 100%) !important;
  border: 1px solid rgba(0, 142, 254, 0.6) !important;
}

/* 最高优先级 - 确保所有下拉框内的文字都是浅色 */
body .el-select-dropdown__item,
body .el-select-dropdown__item *,
body .el-dropdown-menu__item,
body .el-dropdown-menu__item *,
body .el-cascader-node,
body .el-cascader-node *,
body .el-autocomplete-suggestion__item,
body .el-autocomplete-suggestion__item * {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* 特别处理实时配置等数字文本 */
body .el-select-dropdown__item:not(.is-disabled),
body .el-dropdown-menu__item:not(.is-disabled) {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* 禁用状态的选项使用稍微暗一些的颜色 */
body .el-select-dropdown__item.is-disabled,
body .el-dropdown-menu__item.is-disabled {
  color: rgba(255, 255, 255, 0.5) !important;
}

/* 额外保障 - 覆盖任何可能的深色文本 */
.el-select-dropdown,
.el-dropdown-menu,
.el-cascader-panel {
  * {
    color: rgba(255, 255, 255, 0.9) !important;
  }
}
.el-cascader__tags .el-tag{
  background-color: rgba(0, 142, 254, 0.6) !important;
}
.el-button--default{
  background: rgba(150, 191, 222, 0.4); 
  border-color: rgba(150, 191, 222, 0.4);
  color: #fff;
}
.app-container .el-button--text{
  border-color: transparent !important;
  color: #409eff !important;
  background-color: transparent !important;
}
/* 特殊处理可能的内联样式或其他深色文本 */
.el-select-dropdown__item[style*="color"],
.el-dropdown-menu__item[style*="color"] {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* 确保数字文本（如时间配置）清晰可见 - 使用更通用的方法 */
.el-select-dropdown__item,
.el-dropdown-menu__item {
  color: rgba(255, 255, 255, 0.95) !important;
  font-weight: 500 !important;
}

/* 特别修复实时告警页面设置齿轮图标下拉框中的时间选项文字颜色 */
.el-popover .el-radio-group .el-radio,
.el-popover .el-radio-group .el-radio__label,
.el-popover .el-radio__label {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* 确保所有弹出框内的单选按钮文字都是浅色 */
.el-popover .el-radio,
.el-popover .el-radio *,
.el-popover .el-checkbox,
.el-popover .el-checkbox * {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* 针对设备故障管理实时告警页面的特定样式 */
.el-popover[x-placement^="bottom"] .el-radio-group .el-radio__label,
.el-popover[x-placement^="top"] .el-radio-group .el-radio__label {
  color: rgba(255, 255, 255, 0.95) !important;
  font-weight: 500 !important;
}

/* 最高优先级 - 确保所有弹出框内的单选按钮文字都是浅色 */
body .el-popover .el-radio__label,
body .el-popover .el-radio-group .el-radio__label,
body .el-popover .el-checkbox__label {
  color: rgba(255, 255, 255, 0.95) !important;
}

/* 特别针对时间设置弹出框 */
body .el-popover .el-radio__label:after,
body .el-popover .el-radio__label:before {
  color: rgba(255, 255, 255, 0.95) !important;
}

/* 覆盖任何可能的深色文本样式 */
.el-popover .el-radio__input + .el-radio__label {
  color: rgba(255, 255, 255, 0.95) !important;
}
.el-tag.el-tag--info{
  background-color: rgba(0, 142, 254, 0.6);
}
/* 日期选择器面板 */
.el-picker-panel {
  background: linear-gradient(180deg, rgba(4, 104, 192, 0.9) 0%, rgba(5, 84, 159, 0.9) 100%);
  border: 1px solid rgba(0, 142, 254, 0.6);
  color: #eee;
}

.el-date-table td.available:hover {
  background: linear-gradient(90deg, rgba(0, 142, 254, 0.6) 0%, rgba(0, 145, 250, 0.4) 100%);
  color: #00d3e9;
}

.el-date-table td.current:not(.disabled) {
  background: linear-gradient(90deg, rgba(0, 145, 250, 0.8) 0%, rgba(9, 72, 148, 0.8) 100%);
  color: #ffffff;
}
.el-pagination {
  margin-top: 0.9vh;
}

.filter-container {
  margin-bottom: 0.9vh;
}

.el-scrollbar a .is-active {
  background-color: #409eff !important;
  color: #ffffff !important;
  font-weight: bold;
}
.custom-tree-node > svg {
  color: #2ba38a !important;
}
.gztx .is-active {
  font-weight: bold !important;
  color: #b20508 !important;
}

.hide .el-upload--picture-card {
  display: none;
}

.mainBody {
  display: none;
}

.pbkc {
  width: 1.3890vh;
  height: 1.3890vh;
  background-repeat: round;
  margin-top: 0.7408vh;
  margin-bottom: -0.1852vh;
}

.pbkc1 {
  font-size: 1.5742vh;
}

.pbkc2 {
  font-size: 2.0372vh;
  padding-top: 0.0926vh;
}

// date-picker两个输入框字体颜色
.el-date-editor .el-range-input {
  color: black;
}

// 报表的 show-verflow-tooltip的提示框宽度
.el-tooltip__popper {
  max-width: 50%;
}

// 隐藏class名为dateRangePicker的时间选择器的此刻按钮
.dateRangePicker .el-picker-panel__footer .el-button--text {
  display: none !important;
}
