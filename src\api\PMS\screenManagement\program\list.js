import request from '@/utils/request'
const prefix = '/programService'
// 节目管理-分页查询
export function fetchList(data) {
  return request({
    url: prefix + '/api/v1/program/page',
    method: 'post',
    data
  })
}
// 添加节目
export function fetchCreate(data) {
  return request({
    url: prefix + '/api/v1/program/add',
    method: 'post',
    data
  })
}
// 详情节目
export function fetchDetail(id) {
  const data = { id }
  return request({
    url: prefix + '/api/v1/program/query',
    method: 'post',
    data
  })
}

// 删除节目
export function fetchDelete(ids) {
  var data = { ids }
  return request({
    url: prefix + '/api/v1/program/del',
    method: 'post',
    data
  })
}
// 编辑节目
export function fetchUpdate(data) {
  return request({
    url: prefix + '/api/v1/program/update',
    method: 'post',
    data
  })
}
