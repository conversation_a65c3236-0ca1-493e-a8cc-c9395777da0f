<template>
  <div v-loading="loading" element-loading-text="拼命加载中" element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0)" style="height:100%">
    <!-- 大屏地图start-->
    <div class="oldMapStyle" v-html="mapIframe" />
    <div class="grid-content bg-map">
      <el-tooltip class="item" effect="dark" content="地图复位" placement="left-start">
        <div :style="{ 'right': !isShowZy ? '3.55556vh' : '42vh' }"
          style="position: absolute;top:3.73334vh;right: 42vh;width:3.37778vh;cursor: pointer;" @click="backHome">
          <img src="/static/img/home_03.png" style="width:100%">
        </div>
      </el-tooltip>
      <el-tooltip class="item" effect="dark" content="图层配置" placement="left-start">
        <div :style="{ 'right': !isShowZy ? '3.55556vh' : '42vh' }"
          style="position: absolute;top: 7.64445vh;right: 42vh;width:3.37778vh;cursor: pointer;"
          @click="tcpzShow()">
          <img src="/static/img/home_01.png" style="width:100%">
        </div>
      </el-tooltip>
      <el-tooltip class="item" effect="dark" content="收缩左右侧" placement="left-start">
        <div
          :style="{ right: `${!isShowZy ? '3.55556vh' : '42vh'}`, WebkitTransform: `rotateY(${!isShowZy ? '180' : '0'}deg)` }"
          style="position: absolute;top: 11.55557vh;right: 42vh;width:3.37778vh;cursor: pointer;"
          @click="sczycShow()">
          <img src="/static/img/home_00.png" style="width:100%">
        </div>
      </el-tooltip>
      <el-tooltip v-if="checkIndex != null" class="item" effect="dark" content="返回首页" placement="left-start">
        <div :style="{ 'right': !isShowZy ? '3.55556vh' : '42vh' }"
          style="position: absolute;top: 15.46669vh;right: 42vh;width:3.37778vh;cursor: pointer;"
          @click="syShow()">
          <img src="/static/img/home_02.png" style="width:100%">
        </div>
      </el-tooltip>
      <el-tooltip v-if="checkIndex == 4" class="item" effect="dark" content="环网显示" placement="left-start">
        <div :style="{ 'right': !isShowZy ? '3.55556vh' : '42vh' }"
          style="position: absolute;top: 19.3778vh;right: 42vh;width:3.37778vh;cursor: pointer;"
          @click="transformerMapShow()">
          <img src="/static/img/home_4.png" style="width:100%">
        </div>
      </el-tooltip>
      
      <!-- 区域选择按钮组 -->
      <div class="area-selector-buttons" 
           :style="{ 
             'right': !isShowZy ? '8.55556vh' : '47vh',
           }">
        <div class="area-button-group">
          <el-button 
            v-for="area in areaButtons" 
            :key="area.code"
            :class="['area-btn', { 'area-btn-active': selectedAreaCode === area.code }]"
            size="small"
            @click="selectAreaByCode(area)"
          >
            {{ area.name }}
          </el-button>
        </div>
      </div>
      
      <DeviceListBox v-if="deviceListBoxShow" ref="deviceList" :show-dialog="deviceListBoxShow"
        :visible="deviceListBoxShow" :sys-type="sysType" :is-show-zy="isShowZy" />
      <div>
        <el-dropdown trigger="click" class="deviceMapSearch" placement="bottom"
          :style="{ 'left': !isShowZy ? '31.11115vh' : '71vh', 'top': !isShowZy ? '1vh' : '3.9vh' }"
          @visible-change="getAreaCheckList()">
          <el-button type="primary" size="small">
            区域<i class="el-icon-arrow-down el-icon--right" />
          </el-button>
          <el-dropdown-menu slot="dropdown" class="deviceMapSearchBg">
            <div v-show="areaBread.length > 0" class="checkDiv">
              <el-breadcrumb separator-class="el-icon-arrow-right">
                <el-breadcrumb-item @click.native="breadcrumbClick(-1)">首页</el-breadcrumb-item>
                <el-breadcrumb-item v-for="(item, index) in areaBread" :key="item.markerName"
                  @click.native="breadcrumbClick(index)">{{ item.markerName }}</el-breadcrumb-item>
              </el-breadcrumb>
            </div>
            <div v-show="areaBread.length > 0"
              style="border: 0.05vh #6375AB dashed;line-height: 0.1vh;height: 0.1vh;" />
            <div v-if="areaData.length > 0" style="margin:0.89vh;color:#CFCFD0;font-size:1.07vh;">选择区域：</div>
            <div v-if="areaData.length > 0" class="checkDivs">
              <div v-for="area in areaData" :key="area.dicValue"
                :style="{ 'width:': area.markerName != '辅助配套区' ? '4.44445vh' : '7.11112vh' }"
                :class="activeRoadArea == area.markerName ? 'checkItemsp' : 'checkItems'" @click="clickArea(area)">{{
                  area.markerName }}</div>
            </div>
            <div v-if="roadList.length > 0" style="margin:0.89vh;color:#CFCFD0;font-size:1.07vh;">选择道路：</div>
            <div ref="areaItemSearch" class="areaItemSearch">
              <dl>
                <ul v-loading.sync="roadLoading" class="road-item">
                  <li v-for="m in roadList" :key="m.markerDataId" @click="showRoad(m)">{{ m.markerName }}</li>
                </ul>
              </dl>
            </div>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div class="sys-btn-menu">
        <sys-type-menu :sys-type-list="sysTypeList" :check-index="checkIndex"
          @showTypeSet="(v) => { showTypeSet(v.typeValue, v.item, v.flag) }" />
      </div>
      <!-- 回传网、边缘节点、供电、交管、视频监控、道路 start-->
      <transition name="fade">
        <div v-if="shgkLeftShow" ref="shgkLeft" class="left-side">
          <div style="height: 73%">
            <equipMentObjectIndex ref="equipMentObjectIndex" :key-model="equipMentObjectData" :sys-type="sysType"
              :alarm-overview-data="alarmOverviewData" :selected-area-code="selectedAreaCode" />
          </div>
          <chart-box title="设备运行监控" style="height: 27%;">
            <equipOperationMonitor ref="equipOperationMonitor" :key-model="equipOperationMonitorData" />
          </chart-box>
        </div>
      </transition>
      <transition name="fade">
        <div v-if="shgkRightShow" ref="shgkRight" class="right-side">
          <div style="height: 100%">
            <alarmOverviewIndex ref="alarmOverviewIndex" :sys-type="checkIndex" :alarm-overview-data="alarmOverviewData"
              :warning-view-model="warningViewModel" />
          </div>
        </div>
      </transition>
      <!-- 回传网、边缘节点、供电、交管、视频监控、道路 end-->

      <!-- 照明 start-->
      <transition name="fade">
        <div v-if="zmLeftShow" ref="zmLeft" class="left-side">
          <div style="height: 45%">
            <equipMentLigntObjectIndex :suppliers-proportion-data="suppliersProportionData" :sys-type="checkIndex"
              :key-model="equipMentObjectData" />
          </div>
          <chart-box title="使用时间占比分析" style="height: 22%">
            <useTimeRadio :key-model="useTimeRadioData" />
          </chart-box>
          <chart-box title="能耗分析" style="height: 33%">
            <eneryAnalysis />
          </chart-box>
        </div>
      </transition>
      <transition name="fade">
        <div v-if="zmRightShow" ref="zmRight" class="right-side">
          <div style="height: 100%">
            <alarmOverviewIndex ref="alarmOverviewIndex" :sys-type="checkIndex" :alarm-overview-data="alarmOverviewData"
              :warning-view-model="warningViewModel" />
          </div>
        </div>
      </transition>
      <!-- 照明 end-->
    </div>
    <!-- 大屏地图end-->

    <div class="bg1">
      <el-dialog v-dialogDrag append-to-body class="layerManager dashboard-dialog" title="设备聚合详情"
        :visible.sync="clusterDataVisible" width="74.074vh" height="37.037vh">
        <clusterData :sys-type="checkIndex" :key-data="clusterDataList" :show-dialog="clusterDataVisible"
          @handCancle="clusterDialogShow" />
      </el-dialog>
      <el-dialog v-dialogDrag append-to-body class="layerManager dashboard-dialog" title="图层配置"
        :visible.sync="tcpzdialogFormVisible" width="129.63vh" height="37.037vh">
        <layerConfig ref="layerConfig" :check-index="checkIndex" :show-dialog="tcpzdialogFormVisible" @handCancle="tcpzhandCancle" />
      </el-dialog>
      <!-- 设备详情 -->
      <div class="lampPoleDiv">
        <el-dialog v-dialogDrag append-to-body destroy-on-close class="layerManager dashboard-dialog"
          :title="deviceDetailModel.title" :visible.sync="sbxqdialogFormVisible" :width="dialogWidth" height="37.037vh"
          :before-close="stopInternelLamp">
          <!-- <deviceDetailView :keyValue="deviceDetailModel" :show-dialog="sbxqdialogFormVisible" @handCancle="sbxqhandCancle"></deviceDetailView> -->
          <lampPoleView v-if="sbxqdialogFormVisible" ref="lampDialog" topo-id="dashboardTopo0" destroy-on-close
            :sys-type="checkIndex" :is-need-pagination="true" :is-show-lamp-image="isShowLampImage"
            :key-value="deviceDetailModel" :device-id="lampPoleDeviceId" :show-dialog="sbxqdialogFormVisible"
            @tabChange="tabChange" @handCancle="sbxqhandCancle" @changeVideoInfo="changeVideoInfo"
            @resetChoose="resetChoose" @positionMap="positionMap" />
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import { getRoadlistByArea } from '@/api/otherResource/cmdb/area'
import { getUserId } from '@/utils/auth'
import equipMentObjectIndex from './equipMentObject/index'
import equipMentLigntObjectIndex from './light/equipMentLightObject/index'
import equipOperationMonitor from './equipOperationMonitor'
import alarmOverviewIndex from './alarmOverview/index'
import layerConfig from './dialogViews/layerConfig'
import eneryAnalysis from './light/eneryAnalysis/index'
import lampPoleView from '@/views/dashboard/admin/dialogViews/lampPoleView'
import DeviceListBox from '@/views/dashboard/admin/dialogViews/DeviceListBox'
import clusterData from './dialogViews/clusterData'
import useTimeRadio from './light/useTimeRadio/index'
import chartBox from './chartBox'
import sysTypeMenu from './sysTypeMenu'
import { getDeviceAttribute, getLeftOne, getLeftTwo, getRightOne, getRightTwo, getNewAlarmList, getUserPoint, suppliersProportion, useTimeProportion, getSysTypeList, getPowerMap, getTransformerLine, getRingNetWorMapById, getTransformerMap, getDeviceTypeIconInfo, saveUserPoint } from '@/api/main/admin'
import { listPage } from '@/views/otherResource/soc/mixins'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { geAreaList } from '@/api/otherResource/cmdb/area'
import { getToken } from '@/utils/auth'

export default {
  components: {
    clusterData,
    equipMentObjectIndex,
    equipOperationMonitor,
    alarmOverviewIndex,
    layerConfig,
    equipMentLigntObjectIndex,
    eneryAnalysis,
    lampPoleView,
    DeviceListBox,
    useTimeRadio,
    chartBox,
    sysTypeMenu
  },
  mixins: [listPage],
  provide() {
    return {
      getRightOne: this.loadData
    }
  },
  data() {
    return {
      clusterDataVisible: false,
      clusterDataList: [],
      showcheckList: [],
      sysTypeList: [],
      showType: 0,
      deviceData: {},
      sysType: -1,
      mapData: [],
      mapDataList: {},
      sysTypeForList: -1,
      deviceId: 0,
      deviceName: '照明设备',
      checkType: null,
      checkIndex: null,
      mapIframe: '<iframe style="height: 100%;width: 100%;border: 0;" id="map1" name="map1" src="/static/baiduMap.html?from=1&t="' + Date.now() + '></iframe>',
      t: null,
      tHight: null,
      loading: false, // 是否正在加载
      tcpzdialogFormVisible: false,
      sbxqdialogFormVisible: false,
      warningViewModel: {},
      equipMentObjectData: {},
      suppliersProportionData: [],
      equipOperationMonitorData: [],
      alarmOverviewData: {},
      deviceDetailModel: {},
      dialogWidth: '800px',
      lampPoleDeviceId: 0,
      isShowZy: true,
      chooseId: -1, // 选中的设备id
      showDeviceType: -1,
      checkedArea: [],
      areaCheckList: [],
      isAreaIndeterminate: true,
      deviceListBoxShow: true,
      roadList: [],
      activeRoadArea: '',
      useTimeRadioData: [],
      activeTab: null,
      isShowLampImage: false, // 判断属性卡左侧是否显示杆柱图片
      shgkLeftShow: true,
      shgkRightShow: true,
      zmLeftShow: false,
      zmRightShow: false,
      roadLoading: false,
      areaBread: [], // 区域下拉面包屑
      eventSource: null,
      areaButtons: [
        { 
          code: 'all', 
          name: '全域',
          deviceData: {
            searchType: 3,
            deviceId: '1',
            deviceName: '雄安新区',
            deviceAddress: '雄安新区',
            areaCode: '1'
          }
        },
        { 
          code: 'rongxi', 
          name: '容西',
          deviceData: {
            searchType: 3,
            deviceId: '12',
            deviceName: '容西片区',
            deviceAddress: '容西片区',
            areaCode: '12'
          }
        },
        { 
          code: 'zanang', 
          name: '昝岗',
          deviceData: {
            searchType: 3,
            deviceId: '13', 
            deviceName: '昝岗片区',
            deviceAddress: '昝岗片区',
            areaCode: '13'
          }
        },
        { 
          code: 'xiongdong', 
          name: '雄东',
          deviceData: {
            searchType: 3,
            deviceId: '15',
            deviceName: '雄东片区',
            deviceAddress: '雄东片区',
            areaCode: '15'
          }
        },
        { 
          code: 'start', 
          name: '启动区',
          deviceData: {
            searchType: 3,
            deviceId: '14',
            deviceName: '启动区',
            deviceAddress: '启动区',
            areaCode: '14'
          }
        },
      ],
      selectedAreaCode: null // 不设置默认选中区域
    }
  },
  computed: {
    areaData() { // 区域下拉展示数据
      return this.areaBread.length > 0 ? this.areaBread[this.areaBread.length - 1].childLayerMessage ? this.areaBread[this.areaBread.length - 1].childLayerMessage.filter(ele => ele.markerDataType === 1) : [] : this.areaCheckList
    }
  },
  mounted() {
    console.log('开始加载数据')
    this.t = setInterval(() => {
      this.loadData(this.checkIndex)
    }, 300000)
    this.$EventBus.$on('showDevice', (deviceData) => { // 选择设备告警信息显示单个设备
      this.showDevice(this.sysType, -1, false)
    })
    this.$EventBus.$on('deviceData', (deviceData) => { // 子组件数据改变之后传递给父组件
      // 调用地图的子方法
      window.frames['map1'].postMessage({ type: 'selectDevice', data: deviceData, lastChooseId: this.chooseId }, '*')
      this.chooseId = deviceData.deviceId
      this.clearAreaColor()
    })
    this.$EventBus.$on('resetChoose', (deviceData) => { // 子组件数据改变之后传递给父组件
      window.frames['map1'].postMessage({ type: 'resetChoose', chooseId: this.chooseId }, '*')
      this.clearAreaColor()
    })
    this.$EventBus.$on('visible', (visible) => { // 子组件数据改变之后传递给父组件
      this.deviceListBoxShow = visible
    })
    console.log('数据加载完成，不包括异步')
  },
  beforeDestroy() {
    this.destroy()
  },
  created() {
    console.log('开始加载页面')
    this.checkIndex = null
    // this.hideSqgk()
    // this.showZm()
    var that = this
    that.mapIframe = '<iframe style="height: 100%;width: 100%;border: 0;" id="map1" name="map1" src="/static/baiduMap.html?from=1&t="' + Date.now() + '></iframe>'
    setTimeout(function () {
      that.showSqgk()
      that.hideZm()
    }, 500)
    // 监听子页面传递的数据
    window.addEventListener('message', this.listenerEvent)
    // 加载真实的区域数据
    this.loadRealAreaData()
    // 初始化默认配置 - 已注释，不设置默认区域选择
    // this.initDefaultAreaConfig()
    console.log('页面加载完成')
  },
  methods: {
    destroy() {
      clearInterval(this.t)
      clearInterval(this.tHight)
      if (window.frames['map1']) {
        window.frames['map1'].postMessage({ type: 'closeMap' }, '*')
        this.mapIframe = ''
        // 清空地图控件
        console.log('关闭地图')
        this.clearAreaColor()
      }
      // 清理监听事件
      window.removeEventListener('message', this.listenerEvent)
      this.$EventBus.$off('showDevice')
      this.$EventBus.$off('deviceData')
      this.$EventBus.$off('resetChoose')
      this.$EventBus.$off('visible')
      this.eventSource.abort() // 中断请求
    },
    showRoad(data) {
      window.frames['map1'].postMessage({ type: 'setPolyline', data: data }, '*',)
    },

    clusterDialogShow(reload, deviceData) {
      if (reload) {
        this.showDevice(this.sysType, -1, false)
        this.chooseId = deviceData.deviceId
        window.frames['map1'].postMessage({ type: 'selectDevice', data: deviceData, lastChooseId: this.chooseId }, '*')
        this.clearAreaColor()
      }
      this.clusterDataVisible = false
    },
    getAreaCheckList() {
      geAreaList({
        page: 1,
        limit: 999
      }).then((res) => {
        this.areaCheckList = res.data.data
        this.getRoadList(0)
      })
    },
    breadcrumbClick(index) {
      this.areaBread = this.areaBread.splice(0, index + 1)
      const area = this.areaBread[this.areaBread.length - 1] || {}
      this.getRoadList(area.markerDataId || '0')
      area.point && window.frames['map1'].postMessage({ type: 'setAreaColor', area }, '*')
    },
    clickArea(area) {
      if (this.hasChildArea(area)) {
        this.areaBread.push(area)
      }
      this.getRoadList(area.markerDataId)
      area.point && window.frames['map1'].postMessage({ type: 'setAreaColor', area }, '*')
    },
    hasChildArea(area) {
      return area.childLayerMessage && area.childLayerMessage.filter(ele => ele.markerDataType === 1).length > 0
    },
    getRoadList(markerDataId) {
      this.roadLoading = true
      getRoadlistByArea({
        markerDataId
      }).then(res => {
        this.roadList = res.data.data
        this.roadLoading = false
      }).catch(() => {
        this.roadLoading = false
      })
    },
    handleAreaCheckedChange(data) {
      this.backHome()
      // 选择的对应区域，需高亮显示为不同的颜色
      window.frames['map1'].postMessage({ type: 'setAreaColor', area: data }, '*')
    },
    showDeviceSingle(deviceData, status) { // 在地图上展示单个设备（一般用于告警）
      var that = this
      this.chooseId = deviceData.deviceId
      this.status = status
      this.showDeviceType = deviceData.deviceType
      this.mapData = [{ sysType: this.sysType }]
      // 根据类型重新赋值数据
      // 状态全是告警
      // this.mapDataList[this.sysType] = [deviceData]
      window.frames['map1'].postMessage({ type: 'fromView', data: that.mapData, dataList: that.mapDataList, showcheckList: that.showcheckList, url: process.env.BASE_API, showType: that.showType, pointList: [], chooseId: this.chooseId, status: that.status, isZoom: false }, '*')
      this.clearAreaColor()
    },
    stopInternelLamp() {
      this.$refs['lampDialog'].clearLampToggleMenu()
      this.$refs.lampDialog.close()
      clearInterval(this.tHight)
      this.tHight = null
      this.sbxqdialogFormVisible = false
    },
    listenerEvent(e) {
      var that = this
      if (e.data.type === 'getDevicePoint') { // 点击地图设备图标，打开设备详情弹窗
        this.deviceId = e.data.data.deviceId
        if (e.data.data.deviceType === '6' || e.data.data.deviceType === '12' || e.data.data.deviceType === '8' || e.data.data.deviceType === '9' || e.data.data.deviceType === '10') { // 杆柱
          this.isShowLampImage = true
        } else {
          this.isShowLampImage = false
        }
        this.sbxqShow(this.deviceId)
        if (this.tHight !== null) {
          clearInterval(this.tHight)
          this.tHight = null
        } else {
          this.sbxqdialogFormVisible = false
        }
        this.tHight = setInterval(() => {
          if (that.tHight != null && that.sbxqdialogFormVisible === true) {
            that.sbxqShow(that.deviceId)
          } else {
            clearInterval(that.tHight)
            that.tHight = null
            that.sbxqdialogFormVisible = false
          }
        }, 5000)
      } else if (e.data.type === 'loadOver') { // 点击地图设备图标，打开设备详情弹窗
        getDeviceTypeIconInfo().then(res => {
          window.frames['map1'].postMessage({ type: 'deviceTypeList', data: res.data.data }, '*')
        })
        this.loadData(null)
        console.log('加载完毕')
      } else if (e.data.type === 'showClusterData') { // 点击聚合点，展示所有设备的数据
        this.clusterDataList = e.data.data
        this.clusterDataVisible = true
      } else if (e.data.type === 'hiddenSearchDevice') { // 点击地图，因此设备搜索控件
        this.$refs.deviceList.showOff()
      } else if (e.data.type === 'weatherInfo') { // 高德天气接口信息
        this.$EventBus.$emit('weatherInfo', e.data.data)
      } else if (e.data.type === 'getTransformerMap') { // 获取环网
        if (!e.data.data) {
          this.transformerMapShow()
          return
        }
        getRingNetWorMapById({ networkId: e.data.data }).then(res => {
          if (res.data.code === 200) {
            window.frames['map1'].postMessage({ type: 'showTransformerMap', data: res.data.data, showType: 'detail' }, '*',)
          }
          this.getMapData(this.sysType, null, null, null, false)
        })
      } else if (e.data.type === 'getPowerMap') { // 获取电力地图
        const api = e.data.data.deviceType === '587' ? getPowerMap : getTransformerLine
        const { deviceId } = e.data.data
        api({ deviceId }).then(res => {
          if (res.data.code === 200) {
            window.frames['map1'].postMessage({ type: 'showPowerMap', deviceType: e.data.data.deviceType, data: res.data.data }, '*',)
          }
        })
      }
    },
    getMapData(sysType, areaCodes, deviceStates, deviceTypes, isZoom) {
      console.log('加载地图数据', sysType)
      this.loading = true
      this.mapDataList = {} // 清空操作
      const ctrl = new AbortController()
      this.eventSource = ctrl
      fetchEventSource('/spotService/api/basisSpot/getFluxDevicePoint', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer ' + getToken(),
          'Content-Type': 'application/json',
          'Accept-Encoding': 'gzip, deflate, br, zstd',
          'Accept': '*/*'
        },
        body: JSON.stringify({
          sysType,
          userId: '1'
        }),
        signal: ctrl.signal,
        onmessage: (msg) => {
          console.log('msg', msg)
          const eventData = JSON.parse(msg.data)
          this.loading = false
          if (!this.mapDataList[sysType]) {
            this.mapDataList[sysType] = [] // 非空校验
          }
          if (!this.showcheckList[sysType]) {
            this.showcheckList[sysType] = [] // 非空校验
          }

          if (eventData !== null && eventData.length > 0) {
            eventData.forEach(item => {
              this.mapDataList[sysType].push(item)
            })
          } else {
            this.mapDataList[sysType] = []
          }
          this.showcheckList[sysType] = []
          if (this.mapDataList[sysType] !== null && this.mapDataList[sysType].length > 0) {
            this.mapDataList[sysType].forEach(element => {
              this.showcheckList[sysType].push(element.deviceId)
            })
          }
          this.showDevice(sysType, -1, isZoom)
          console.log('加载完成')
        },
        onerror: (err) => {
          console.log(err)
          this.loading = false
        },
        onclose: () => {
          this.loading = false
        }
      }).catch(() => {
        this.loading = false
      })
      // 获取是否显示设备名称
      getUserPoint({
        sysType: this.checkIndex,
        userId: getUserId()
      }).then((r) => {
        if (r.data.data != null) {
          window.frames['map1'].postMessage({ type: 'setShowDeviceName', data: r.data.data.showDeviceName || 0 }, '*')
        }
      })
    },
    showDevice(sysType, status, isZoom) {
      var that = this
      that.status = status
      that.sysTypeForList = sysType
      window.frames['map1'].postMessage({ type: 'resetChoose', chooseId: -1 }, '*')
      that.mapData = [{ sysType: sysType }]
      that.sysType = sysType
      window.frames['map1'].postMessage({ type: 'fromView', data: that.mapData, dataList: that.mapDataList, showcheckList: that.showcheckList, url: process.env.BASE_API, showType: that.showType, pointList: [], chooseId: -1, status: that.status, isZoom: isZoom }, '*')
    },
    hideSqgk() { // 隐藏设备概况左右边模块内容
      this.shgkLeftShow = false
      this.shgkRightShow = false
    },
    showSqgk() { // 显示设备概况左右边模块内容
      this.shgkLeftShow = true
      this.shgkRightShow = true
    },
    hideZm() { // 隐藏照明概况左右边模块内容
      this.zmLeftShow = false
      this.zmRightShow = false
    },
    showZm() { // 显示照明概况左右边模块内容
      this.zmLeftShow = true
      this.zmRightShow = true
    },
    backHome() {
      window.frames['map1'].postMessage({ type: 'backHome' }, '*')
      // 清空区域选择状态，不设置默认选中
      this.selectedAreaCode = null
      this.clearAreaColor()
    },
    loadData(sysType) {
      console.log('开始加载data')
      // 1:回传网，2：边缘节点，3：照明，4：供电，5：视频，6：交管，7：道路
      getSysTypeList({
        isSpot: 1
      }).then(response => {
        !response.data.code && (this.sysTypeList = response.data.data)
      })
      getLeftOne({
        sysType: sysType
      }).then(response => {
        this.equipMentObjectData = response.data.data
      })
      suppliersProportion().then(response => {
        this.suppliersProportionData = response.data.data
      })
      useTimeProportion().then(response => {
        this.useTimeRadioData = response.data.data
      })
      getLeftTwo({
        sysType: sysType
      }).then(response => {
        this.equipOperationMonitorData = response.data.data
      })
      getRightOne({
        sysType: sysType
      }).then(response => {
        this.alarmOverviewData = response.data.data
        // 触发事件将告警数据传递给父组件
        this.$emit('update-alarm-data', this.alarmOverviewData)
      })
      getRightTwo({
        sysType: sysType
      }).then(response => {
        this.warningViewModel = response.data.data
        //this.$store.commit('SET_ALARM_DATA', this.warningViewModel.alarmList)
      })
      //更换实时告警
      getNewAlarmList({}).then(r => {
        if (r.data.data != null) {
          this.$store.commit('SET_ALARM_DATA', r.data.data)
        }
      })
      this.getMapData(sysType, null, null, null, false)
      console.log('data加载完毕')
    },
    showTypeSet(type, typeObj, isBackHome) {
      if (isBackHome) {
        this.backHome()
      }
      this.checkIndex = type
      this.checkType = typeObj
      this.isShowZy = true
      if (typeObj?.bak === '1') { // 照明菜单
        this.hideSqgk()
        this.showZm()
      } else { // 其他菜单
        this.hideZm()
        this.showSqgk()
      }
      this.eventSource.abort() // 中断请求
      this.loadData(type)
    },
    tcpzhandCancle(reload) { // 图层配置弹框
      if (reload) {
        this.eventSource.abort() // 中断请求
        this.mapDataList[this.sysType] = [] // 清空操作
        this.loadData(this.checkIndex)
        this.clearAreaColor()
      }
      this.tcpzdialogFormVisible = false
    },
    clearAreaColor() {
      // 清空地图高亮的区域颜色
      window.frames['map1'].postMessage({ type: 'clearMap' }, '*')
      this.checkedArea = []
      // 不重置区域选择状态，保持当前选中状态
      // 由于渲染区域颜色会清空地图上的所有设备标志，所以这里再重新渲染
      this.showDevice(this.sysType, -1, false)
    },
    tcpzShow() { // 图层配置弹框
      this.tcpzdialogFormVisible = true
      this.clearAreaColor()
    },
    sbxqhandCancle() { // 设备详情弹框
      clearInterval(this.tHight)
      this.tHight = null
      this.sbxqdialogFormVisible = false
    },
    async sbxqShow(deviceId, flag) { // 设备详情弹框
      if (!flag) {
        this.lampPoleDeviceId = deviceId
      }
      await getDeviceAttribute({
        deviceId: deviceId
      }).then(response => {
        const data = response.data.data
        data.deviceData.sort((a, b) => a.index - b.index)
        this.deviceDetailModel = data
      })
      if (this.activeTab !== 4) {
        if (this.deviceDetailModel.deviceType !== 6 && this.deviceDetailModel.deviceType !== 7 && this.deviceDetailModel.deviceType !== 12 && (this.deviceDetailModel.deviceType !== 15 || this.isShowLampImage === false) && this.deviceDetailModel.deviceType !== 8 && this.deviceDetailModel.deviceType !== 9 && this.deviceDetailModel.deviceType !== 10) {
          this.dialogWidth = '111.11vh'
        } else {
          const w = document.documentElement.clientWidth || document.body.clientWidth * 0.75 + 'px'
          this.dialogWidth = (w * 0.9) + 'px'
        }
      }
      if (this.sbxqdialogFormVisible === false && this.tHight != null) {
        this.sbxqdialogFormVisible = true
      }

      this.clearAreaColor()
    },
    syShow() { // 回到首页
      var that = this
      that.checkIndex = null
      setTimeout(function () {
        that.showSqgk()
        that.hideZm()
      }, 500)
      this.checkType = null
      window.frames['map1'].postMessage({ type: 'clearMap' }, '*',)
      this.loadData(null)
    },
    transformerMapShow() {
      getTransformerMap().then(res => {
        if (res.data.code === 200) {
          window.frames['map1'].postMessage({ type: 'showTransformerMap', data: res.data.data }, '*',)
        }
      })
      this.getMapData(this.sysType, null, null, null, false)
    },
    sczycShow() {
      if (!this.isShowZy) {
        this.showTypeSet(this.checkIndex, this.checkType, false)
      } else {
        this.hideSqgk()
        this.hideZm()
        this.isShowZy = !this.isShowZy
        this.clearAreaColor()
      }
    },
    changeVideoInfo(id) {
      clearInterval(this.tHight)
      this.sbxqShow(id, true)
      this.clearAreaColor()
    },
    // 取消选中设备
    resetChoose(alarmInfo) {
      this.sbxqhandCancle()
      // 取消选中设备
      window.frames['map1'].postMessage({ type: 'resetChoose', chooseId: this.chooseId }, '*')
      this.clearAreaColor()
    },
    positionMap(alarmInfo) {
      this.sbxqhandCancle()
      // // 显示设备
      // this.$parent.$emit('showDevice', alarmInfo);
      // // 选择某一个设备
      // this.$parent.$emit('deviceData', alarmInfo);
      this.showDevice(this.sysType, -1, false)
      this.chooseId = alarmInfo.deviceId
      // 调用地图的子方法
      window.frames['map1'].postMessage({ type: 'selectDevice', data: alarmInfo, lastChooseId: this.chooseId }, '*')
      this.clearAreaColor()
    },
    tabChange(val) {
      this.activeTab = ~~val

      if (this.activeTab === 4) {
        this.dialogWidth = '95%'
      } else if (this.deviceDetailModel.deviceType !== 6 && this.deviceDetailModel.deviceType !== 7 && this.deviceDetailModel.deviceType !== 12 && (this.deviceDetailModel.deviceType !== 15 || this.isShowLampImage === false) && this.deviceDetailModel.deviceType !== 8 && this.deviceDetailModel.deviceType !== 9 && this.deviceDetailModel.deviceType !== 10) {
        this.dialogWidth = '111.11vh'
      } else {
        const w = document.documentElement.clientWidth || document.body.clientWidth * 0.75 + 'px'
        this.dialogWidth = (w * 0.9) + 'px'
      }
    },
    selectAreaByCode(area) {
      this.selectedAreaCode = area.code
      this.clearAreaColor()
      
      // 如果选择全域，则回到地图首页显示所有设备
      if (area.code === 'all') {
        window.frames['map1'].postMessage({ type: 'backHome' }, '*')
        this.showDevice(this.sysType, -1, false)
        // 同步更新用户配置 - 清空区域选择
        this.syncUserConfigForAllArea()
        return
      }
      
      // 构造符合真实数据结构的区域数据对象
      const areaData = {
        ...area.deviceData,
        // 确保必要字段存在
        markerDataId: area.deviceData.deviceId,
        markerName: area.deviceData.deviceName,
        // 添加地图需要的额外属性（如果API数据中没有）
        mapDeviceId: area.deviceData.mapDeviceId || area.deviceData.deviceId,
        descInfo: area.deviceData.descInfo || null,
        deviceSerno: area.deviceData.deviceSerno || null,
        deviceType: area.deviceData.deviceType || null,
        deviceTypeName: area.deviceData.deviceTypeName || null,
        latitude: area.deviceData.latitude || null,
        longitude: area.deviceData.longitude || null,
        mapType: area.deviceData.mapType || null
      }
      
      // 1. 清空地图高亮
      window.frames['map1'].postMessage({ type: 'clearMap' }, '*')
      
      // 2. 高亮显示选中区域
      window.frames['map1'].postMessage({ 
        type: 'setAreaColor', 
        area: areaData 
      }, '*')
      
      // 3. 触发事件总线，通知其他组件区域已选择
      this.$EventBus.$emit('showDevice', areaData)
      
      // 4. 根据区域筛选设备
      this.filterDevicesByArea(area.deviceData.areaCode)
      
      // 5. 更新面包屑导航
      this.updateAreaBreadcrumb(areaData)
      
      // 6. 同步更新用户配置
      this.syncUserConfigForArea(area.deviceData.areaCode)
    },
    filterDevicesByArea(areaCode) {
      if (!areaCode) return
      
      // 根据区域代码筛选设备
      // 设备数据中可能包含 areaCode、belongArea 等字段来标识所属区域
      const filteredDevices = this.mapDataList[this.sysType]?.filter(device => {
        // 多种匹配方式，适应不同的数据结构
        return device.areaCode === areaCode || 
               device.belongAreaCode === areaCode ||
               device.belongArea?.includes(areaCode) ||
               device.deviceAddress?.includes(this.getAreaNameByCode(areaCode))
      }) || []
      
      // 更新地图显示的设备
      if (filteredDevices.length > 0) {
        const tempMapDataList = { ...this.mapDataList }
        tempMapDataList[this.sysType] = filteredDevices
        
        
        window.frames['map1'].postMessage({ 
          type: 'fromView', 
          data: this.mapData, 
          dataList: tempMapDataList, 
          showcheckList: this.showcheckList, 
          url: process.env.BASE_API, 
          showType: this.showType, 
          pointList: [], 
          chooseId: -1, 
          status: this.status, 
          isZoom: true 
        }, '*')
      } else {
        // 如果没有筛选到设备，仍然显示区域高亮，但不筛选设备
        this.showDevice(this.sysType, -1, true)
      }
    },
    getAreaNameByCode(areaCode) {
      const areaMap = {
        '12': '容西',
        '13': '昝岗', 
        '14': '启动区',
        '15': '雄东'
      }
      return areaMap[areaCode] || ''
    },
    updateAreaBreadcrumb(areaData) {
      this.areaBread = [{
        markerDataId: areaData.markerDataId || areaData.deviceId,
        markerName: areaData.markerName || areaData.deviceName,
        markerDataType: areaData.markerDataType || 1,
        markerType: areaData.markerType || 2,
        markerCenterPoint: areaData.markerCenterPoint,
        point: areaData.point,
        upMarkerDataId: areaData.upMarkerDataId,
        remark: areaData.remark,
        opDate: areaData.opDate,
        childLayerMessage: areaData.childLayerMessage || []
      }]
    },
    async syncUserConfig(selectedAreaCode) {
      try {
        // 使用完整的配置同步生命周期管理
        await this.executeCompleteConfigSync(selectedAreaCode)
        
      } catch (error) {
        console.error('同步用户配置失败:', error)
        this.$message.error('同步配置失败，请稍后重试')
      }
    },
    async getCurrentUserConfig() {
      try {
        const response = await getUserPoint({
          sysType: this.checkIndex,
          userId: getUserId()
        })
        
        if (response.data.data) {
          return {
            areaCodes: response.data.data.areaCodes || [],
            deviceAlarmStates: response.data.data.deviceAlarmStates || [],
            deviceOnlineStates: response.data.data.deviceOnlineStates || [],
            deviceTypes: response.data.data.deviceTypes || [],
            tagIds: response.data.data.tagIds || [],
            showDeviceName: response.data.data.showDeviceName || 0
          }
        }
        
        // 返回默认配置
        return {
          areaCodes: [],
          deviceAlarmStates: [],
          deviceOnlineStates: [],
          deviceTypes: [],
          tagIds: [],
          showDeviceName: 0
        }
      } catch (error) {
        console.error('获取用户配置失败:', error)
        throw error
      }
    },
    async saveUpdatedUserConfig(config) {
      try {
        const response = await saveUserPoint({
          sysType: this.checkIndex,
          userId: getUserId(),
          mapSettingPointSpotVo: config
        })
        
        if (response.data.code === 200) {
          return true
        } else {
          throw new Error('保存配置失败: ' + response.data.message)
        }
      } catch (error) {
        console.error('保存用户配置失败:', error)
        throw error
      }
    },
    notifyLayerConfigUpdate(config) {
      // 通过事件总线通知图层配置组件
      this.$EventBus.$emit('userConfigUpdated', {
        sysType: this.checkIndex,
        config: config
      })
      
      // 如果图层配置对话框是打开的，直接更新其数据
      if (this.tcpzdialogFormVisible && this.$refs.layerConfig) {
        this.$refs.layerConfig.updateConfigFromParent(config)
      }
    },
    syncUserConfigForAllArea() {
      this.syncUserConfig(null)
    },
    syncUserConfigForArea(areaCode) {
      this.syncUserConfig(areaCode)
    },
    // 获取区域的子区域信息（用于可能的扩展功能）
    getSubAreas(areaCode) {
      const area = this.areaButtons.find(btn => btn.deviceData.areaCode === areaCode)
      if (area && area.deviceData.childLayerMessage) {
        return area.deviceData.childLayerMessage.filter(child => child.markerDataType === 1)
      }
      return []
    },
    
    // 检查区域是否有子区域
    hasSubAreas(areaCode) {
      const subAreas = this.getSubAreas(areaCode)
      return subAreas.length > 0
    },
    
    // 从API获取真实的区域数据并更新按钮配置
    async loadRealAreaData() {
      try {
        const response = await geAreaList({
          page: 1,
          limit: 999
        })
        
        if (response.data.code === 200 && response.data.data && response.data.data.length > 0) {
          const rootAreaData = response.data.data[0] // 雄安新区根节点
          const childAreas = rootAreaData.childLayerMessage || []
          
          // 查找特定区域并更新按钮配置
          const areaMapping = {
            'rongxi': { name: '容西片区', id: '12' },
            'zanang': { name: '昝岗片区', id: '13' }, 
            'start': { name: '启动区', id: '14' },
            'xiongdong': { name: '雄东片区', id: '15' }
          }
          
          // 更新区域按钮配置
          Object.keys(areaMapping).forEach(code => {
            const targetArea = areaMapping[code]
            const realArea = childAreas.find(area => 
              area.markerDataId === targetArea.id || 
              area.markerName === targetArea.name
            )
            
            if (realArea) {
              const buttonIndex = this.areaButtons.findIndex(btn => btn.code === code)
              if (buttonIndex !== -1) {
                // 使用真实API数据更新按钮配置
                this.areaButtons[buttonIndex].deviceData = {
                  searchType: 3,
                  deviceId: realArea.markerDataId,
                  deviceName: realArea.markerName,
                  deviceAddress: realArea.markerName,
                  areaCode: realArea.markerDataId,
                  markerDataType: realArea.markerDataType,
                  markerType: realArea.markerType,
                  markerCenterPoint: realArea.markerCenterPoint,
                  point: realArea.point,
                  mapDeviceId: realArea.markerDataId,
                  upMarkerDataId: realArea.upMarkerDataId,
                  remark: realArea.remark,
                  opDate: realArea.opDate,
                  // 保存子区域信息，用于后续可能的细分
                  childLayerMessage: realArea.childLayerMessage
                }
              }
            }
          })
          
          // 更新全域按钮，使用根区域数据
          const allButtonIndex = this.areaButtons.findIndex(btn => btn.code === 'all')
          if (allButtonIndex !== -1) {
            this.areaButtons[allButtonIndex].deviceData = {
              searchType: 3,
              deviceId: rootAreaData.markerDataId,
              deviceName: rootAreaData.markerName,
              deviceAddress: rootAreaData.markerName,
              areaCode: rootAreaData.markerDataId,
              markerDataType: rootAreaData.markerDataType,
              markerType: rootAreaData.markerType,
              markerCenterPoint: rootAreaData.markerCenterPoint,
              point: rootAreaData.point,
              mapDeviceId: rootAreaData.markerDataId,
              childLayerMessage: rootAreaData.childLayerMessage
            }
          }
          
        } else {
          console.error('API返回数据格式异常:', response.data)
        }
      } catch (error) {
        console.error('获取区域数据失败:', error)
        this.$message.error('获取区域数据失败，请检查网络连接')
      }
    },
    // 执行配置保存后的后续处理逻辑（参照layerConfig.vue和tcpzhandCancle方法）
    async executePostConfigSaveLogic(newConfig) {
      try {
        console.log('执行配置保存后的后续处理逻辑...')
        
        // 1. 显示成功提示（参照layerConfig.vue的Alert调用）
        this.showConfigUpdateAlert()
        
        // 2. 中断当前的设备数据请求（参照tcpzhandCancle方法）
        if (this.eventSource) {
          this.eventSource.abort()
        }
        
        // 3. 清空当前地图数据（参照tcpzhandCancle方法）
        if (this.mapDataList[this.sysType]) {
          this.mapDataList[this.sysType] = []
        }
        
        // 4. 更新地图显示设备名称设置（参照layerConfig.vue的getetUserPointData方法）
        if (typeof newConfig.showDeviceName === 'number') {
          window.frames['map1'].postMessage({ 
            type: 'setShowDeviceName', 
            data: newConfig.showDeviceName 
          }, '*')
        }
        
        // 5. 应用新的配置筛选条件到地图
        await this.applyConfigFiltersToMap(newConfig)
        
        // 6. 重新加载数据（参照tcpzhandCancle方法）
        this.loadData(this.checkIndex)
        
        // 7. 等待数据加载完成后，应用区域筛选
        setTimeout(() => {
          this.applyAreaFilterAfterDataLoad(newConfig)
        }, 1000) // 给数据加载一些时间
        
        console.log('配置保存后的后续处理逻辑执行完成')
        
      } catch (error) {
        console.error('执行配置保存后的后续处理逻辑失败:', error)
        throw error
      }
    },
    
    // 应用配置筛选条件到地图
    async applyConfigFiltersToMap(config) {
      try {
        // 构造筛选参数（参照layerConfig.vue的数据结构）
        const filterParams = {
          areaCodes: config.areaCodes || [],
          deviceAlarmStates: config.deviceAlarmStates || [],
          deviceOnlineStates: config.deviceOnlineStates || [],
          deviceTypes: config.deviceTypes || [],
          tagIds: config.tagIds || [],
          showDeviceName: config.showDeviceName || 0
        }
        
        // 发送筛选参数到地图组件
        window.frames['map1'].postMessage({
          type: 'applyFilters',
          filters: filterParams,
          sysType: this.checkIndex
        }, '*')
        
      } catch (error) {
        console.error('应用配置筛选条件到地图失败:', error)
      }
    },
    
    // 数据加载完成后应用区域筛选
    applyAreaFilterAfterDataLoad(config) {
      try {
        // 如果配置中有区域选择，重新应用区域筛选
        if (config.areaCodes && config.areaCodes.length > 0) {
          const selectedAreaCode = config.areaCodes[0] // 取第一个区域
          
          // 重新筛选设备
          this.filterDevicesByArea(selectedAreaCode)
          
          // 重新高亮区域（如果有对应的区域按钮）
          const selectedAreaButton = this.areaButtons.find(btn => 
            btn.deviceData.areaCode === selectedAreaCode
          )
          
          if (selectedAreaButton) {
            // 重新高亮显示选中区域
            window.frames['map1'].postMessage({ 
              type: 'setAreaColor', 
              area: selectedAreaButton.deviceData 
            }, '*')
            
            // 更新按钮选中状态
            this.selectedAreaCode = selectedAreaButton.code
          }
        }
        
      } catch (error) {
        console.error('重新应用区域筛选失败:', error)
      }
    },
    
    // 显示配置更新成功提示（参照layerConfig.vue的Alert方法）
    showConfigUpdateAlert() {
      // 使用Element UI的消息提示
      this.$message({
        message: '图层配置已更新',
        type: 'success',
        duration: 2000
      })
    },
    
    // 完整的配置同步生命周期管理（确保与layerConfig.vue行为一致）
    async executeCompleteConfigSync(selectedAreaCode) {
      try {
        // 阶段1: 保存配置前的准备工作
        const currentConfig = await this.getCurrentUserConfig()
        const updatedAreaCodes = selectedAreaCode ? [selectedAreaCode] : []
        
        const newConfig = {
          ...currentConfig,
          areaCodes: updatedAreaCodes
        }
        
        // 阶段2: 保存配置到服务器
        await this.saveUpdatedUserConfig(newConfig)
        
        // 阶段3: 执行layerConfig.vue中handCreate方法的等效逻辑
        this.showConfigUpdateAlert()
        
        // 阶段4: 执行tcpzhandCancle方法的等效逻辑（reload=true的情况）
        await this.executeLayerConfigReloadLogic(newConfig)
        
        // 阶段5: 通知图层配置组件更新
        this.notifyLayerConfigUpdate(newConfig)
        
        // 阶段6: 恢复区域选择状态
        await this.restoreAreaSelectionState(selectedAreaCode)
        
      } catch (error) {
        console.error('执行完整配置同步生命周期失败:', error)
        this.$message.error('配置同步失败，请稍后重试')
        throw error
      }
    },
    
    // 执行图层配置重载逻辑（参照tcpzhandCancle方法的reload=true逻辑）
    async executeLayerConfigReloadLogic(newConfig) {
      try {
        // 1. 中断当前请求（参照tcpzhandCancle）
        if (this.eventSource) {
          this.eventSource.abort()
        }
        
        
        // 2. 清空地图数据（参照tcpzhandCancle）
        if (this.mapDataList[this.sysType]) {
          this.mapDataList[this.sysType] = []
        }
        
        // 3. 重新加载数据（参照tcpzhandCancle）
        this.loadData(this.checkIndex)
        
        // 4. 应用新配置的设备名称显示设置
        if (typeof newConfig.showDeviceName === 'number') {
          window.frames['map1'].postMessage({ 
            type: 'setShowDeviceName', 
            data: newConfig.showDeviceName 
          }, '*')
        }
        
        console.log('图层配置重载逻辑执行完成')
        
      } catch (error) {
        console.error('执行图层配置重载逻辑失败:', error)
        throw error
      }
    },
    
    // 恢复区域选择状态
    async restoreAreaSelectionState(selectedAreaCode) {
      try {
        if (!selectedAreaCode) {
          // 如果没有选择区域，清空状态
          this.selectedAreaCode = null
          return
        }
        
        // 等待数据加载完成
        await this.waitForDataLoad()
        
        // 找到对应的区域按钮
        const selectedAreaButton = this.areaButtons.find(btn => 
          btn.deviceData.areaCode === selectedAreaCode
        )
        
        if (selectedAreaButton) {
          // 更新按钮选中状态
          this.selectedAreaCode = selectedAreaButton.code
          
          // 重新高亮显示选中区域
          window.frames['map1'].postMessage({ 
            type: 'setAreaColor', 
            area: selectedAreaButton.deviceData 
          }, '*')
          
          // 重新筛选设备
          this.filterDevicesByArea(selectedAreaCode)
          
          // 更新面包屑导航
          this.updateAreaBreadcrumb(selectedAreaButton.deviceData)
        }
        
      } catch (error) {
        console.error('恢复区域选择状态失败:', error)
      }
    },
    
    // 等待数据加载完成
    waitForDataLoad() {
      return new Promise((resolve) => {
        const checkDataLoad = () => {
          if (this.mapDataList[this.sysType] && this.mapDataList[this.sysType].length > 0) {
            resolve()
          } else {
            setTimeout(checkDataLoad, 500)
          }
        }
        checkDataLoad()
      })
    },
    // 初始化默认配置
    async initDefaultAreaConfig() {
      try {
        // 延迟执行，确保页面完全加载
        setTimeout(async () => {
          // 获取当前用户配置
          const currentConfig = await this.getCurrentUserConfig()
          
          // 如果没有区域配置，则设置默认全域配置
          if (!currentConfig.areaCodes || currentConfig.areaCodes.length === 0) {
            const defaultConfig = {
              ...currentConfig,
              areaCodes: ['1'] // 全域的区域代码
            }
            
            // 保存默认配置
            await this.saveUpdatedUserConfig(defaultConfig)
            
            // 通知图层配置组件更新
            this.notifyLayerConfigUpdate(defaultConfig)
          }
        }, 2000) // 延迟2秒执行，确保地图和其他组件已加载完成
      } catch (error) {
        console.error('初始化默认配置失败:', error)
      }
    },
  }
}
</script>

<style lang='scss' scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity .5s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.bg-map {
  -webkit-box-shadow: 0 0.0889vh 0.0889vh rgba(0, 0, 0, 0.05);
  box-shadow: 0 0.0889vh 0.0889vh rgba(0, 0, 0, 0.05);
  margin-top: 0vh;
  font-size: 1.07vh;
  padding-top: 0px !important;
  position: absolute;
  top: 7vh;
  width: 100%;
  height: 0;

  .left-side {
    position: absolute;
    width: 43vh;
    height: 91vh;
    padding:3vh 4vh 3vh 2vh;
    overflow: hidden;
    background: url(~@/assets/img/left-side-bg.png) no-repeat;
    background-size: 100% 100%;
  }

  .right-side {
    position: absolute;
    right: 0;
    width: 43vh;
    height: 91vh;
    padding:3vh 2vh 3vh 4vh;
    overflow: hidden;
    background: url(~@/assets/img/right-side-bg.png) no-repeat;
    background-size: 100% 100%;
  }

  .hidden {
    visibility: hidden
  }
}

.oldMapStyle {
  justify-content: center;
  display: flex;
  height: 100%;
}

.bg-purple {
  -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.05);
  // clear: both;
  font-size: 1.07vh;
  padding-top: 0px !important;
  margin: 0.533vh 0.533vh 0.533vh 0.533vh;
  margin-bottom: 0;
}

.bg-purple-border {
  border: 0.0889vh #2b7fc0b5 solid;
  height: 24vh;
  box-shadow: 0px 0.0889vh 0.533vh 0px #2b7fc0b5;
  /* background: rgb(36 44 80 / 90%) !important; */
  width: 30.9334vh;
  box-shadow: #2b7fc0b5 0px 0px 0.46vh inset;
  border-radius: 10px !important;
}

.bg-purple-border1 {
  height: 60vh;
  width: 30.22vh;
  border-radius: 0.46vh !important;
  /* background: rgb(36 44 80 / 90%) !important; */
  border: 0.0889vh #3D6E98 solid
}

.bg-purple-border2 {
  height: 88.5vh;
  width: 30.22vh;
  border-radius: 0.46vh !important;
  /* background: rgb(36 44 80 / 90%) !important; */
  border: 0.0889vh #3D6E98 solid;
  margin-bottom: 1vh;
}

.bg-purple-border3 {
  height: 28vh;
  width: 30.22vh;
  border-radius: 0.46vh !important;
  background: rgba(36, 44, 80, 90%) !important;
  border: 0.0889vh #3D6E98 solid;
  margin-bottom: 1vh;
}

.bg-purple-border4 {
  height: 33vh;
  width: 30.22vh;
  border-radius: 0.46vh !important;
  /* background: rgb(36 44 80 / 90%) !important; */
  border: 0.0889vh #3D6E98 solid;
  margin-bottom: 1vh;
}

.bg-purple-border5 {
  height: 32vh;
  width: 30.22vh;
  border-radius: 0.46vh !important;
  /* background: rgb(36 44 80 / 90%) !important; */
  border: 0.0889vh #3D6E98 solid;
  margin-bottom: 1vh;
}

.bg-purple-border6 {
  height: 21.5vh;
  width: 30.22vh;
  border-radius: 0.46vh !important;
  /* background: rgb(36 44 80 / 90%) !important; */
  border: 0.0889vh #3D6E98 solid;
  margin-bottom: 1vh;
}

.bg-purple-border7 {
  height: 45vh;
  width: 30.22vh;
  border-radius: 0.46vhpx !important;
  /* background: rgb(36 44 80 / 90%) !important; */
  border: 0.0889vh #3D6E98 solid;
  margin-bottom: 1vh;
}

.panel-heading {
  /* text-transform: uppercase; */
  color: #ffffff;
  font-size: 1.51111vh;
  height: 2.5vh;
  width: 100%;
}

.module-title {
  width: 100%;
  color: #FFFFFF;
  height: 100%;
  padding-top: 1vh;
  font-weight: bolder;
  /* border: 1px #3D6E98 solid; */
}

.module-bg {
  background: url(/static/img/home_titleBg.png) no-repeat;
  width: 100%;
  height: 1vh;
}

.mainSvg {
  width: 1.77778vh;
  height: 2vh;
  float: left;
  margin-right: 0.5vh;
  margin-left: 0.88889vh;
}

.sys-btn-menu {
  width: 100%;
  height: 12vh;
  position: absolute;
  top: 77vh;
  padding: 0 46.2222vh;
}

.deviceMapSearch {
  font-size: 1.422vh;
  /* z-index: 1; */
  border-radius: 0.35556vh;
  position: absolute;
  top: 3.9vh;
  left: 64.8889vh;
}

.deviceMapSearch .el-button--primary {
  background: rgba(34, 43, 69, 0.71) !important;
  border: 0.0889vh solid  rgba(0, 142, 254, 1)  !important;
  color: #D3D3D3 !important;
}

.deviceMapSearch .el-button--small {
  padding: 0.88889vh 0.8vh !important;
  font-size: 1.07vh;
  height: 4.1vh;
  margin-top: 0.1vh;
}

.checkDiv {
  min-width: 8.88889vh;
  max-width: 32vh;
  margin-left: 1.77778vh;
  height: 6vh;
  line-height: 2vh;
  margin-right: 1.77778vh;
  display: flex;
  color: #ffffff;
  flex-flow: wrap;
  font-size: 1.244vh;
  padding-top: 1.5vh
}

/deep/.checkDiv .el-breadcrumb__inner,
.checkDiv .el-breadcrumb__item:last-child .el-breadcrumb__inner,
.checkDiv .el-breadcrumb__item:last-child .el-breadcrumb__inner a {
  color: #fff;
  cursor: pointer;
}

/deep/ .checkDiv .el-breadcrumb__item:last-child .el-breadcrumb__inner:hover {
  font-weight: bold;
  color: #fff;
  cursor: pointer;
}

/deep/ .checkDiv .el-breadcrumb__item:last-child .el-breadcrumb__inner {
  font-weight: bold;
}

.checkItem {
  width: 4.44445vh;
  cursor: pointer;
}

.checkItemss {
  width: 7.11112vh;
  cursor: pointer;
  margin-right: 1.77778vh;
}

.checkDivs {
  min-width: 3.55556vh;
  max-width: 32vh;
  margin-right: 1.77778vh;
  display: flex;
  flex-flow: wrap;
  font-size: 1.07vh;
  color: #ffffff;
  margin-top: 1vh;
}

.checkItems {
  cursor: pointer;
  background-color: #2E4FA4;
  margin-left: 1.77778vh;
  text-align: center;
  margin-bottom: 0.5vh;
  padding: 0px 0.533vh;
}

.road-item {
  margin-right: 0.8vh;
  cursor: pointer;
  color: #CFCFD0
}

.road-item:hover {
  color: #409eff
}

.checkItemsp {
  cursor: pointer;
  background-color: #0f89f5;
  text-align: center;
  margin-bottom: 0.5vh;
  margin-left: 1.77778vh;
  padding: 0px 0.533vh;
}

.checkItem:hover {
  color: #F97A16;
}

.checkItemss:hover {
  color: #F97A16;
}

.checkItems:hover {
  color: #ffffff;
  background-color: #0f89f5;
}

.checkItemsp:hover {
  color: #ffffff;
  background-color: #0f89f5;
}

.checkItemSelect {
  color: #ffffff;
  background-color: #0f89f5;
}

.areaItemSearch {
  font-family: Arial, sans-serif;
  -webkit-text-size-adjust: 100%;
  font-size: 1.07vh;
  word-wrap: break-word;
  color: #ffffff;
  user-select: none;
  white-space: nowrap;
  line-height: 2vh;
  display: block;
  position: relative;
  overflow: auto;
  width: 32vh;
  height: 20vh;
  margin-left: 1.77778vh;
  margin-top: 1.5vh;
}

.areaItemSearch dl {
  margin: 0.35556vh 0;
  position: relative;
}

.areaItemSearch dl dt {
  float: left;
  font-weight: 700;
  line-height: 2vh;
}

.areaItemSearch dl ul li {
  display: inline-block;
  margin-right: 0.8vh;
  cursor: pointer;
  color: #CFCFD0;
}

.areaItemSearch dl ul {
  white-space: normal;
  line-height: 2vh;
  margin-bottom: 0.71112vh;
  padding-inline-start: 0;
}

.areaItemSearch dl ul li:hover {
  color: #0f89f5;
}

.deviceMapSearchBg {
  background: rgba(34, 43, 69, 0.71) !important;
  color: #C5D4FF !important;
  border: 0.0889vh solid #35509f !important;
}

.area-selector-buttons {
  position: absolute;
  width: auto;
  top: 3.5vh;
  z-index: 1000;
}

.area-button-group {
  display: flex;
  flex-direction: row;
  gap: 1vh;
  align-items: center;
}

.area-btn {
  background: rgba(34, 43, 69, 0.8) !important;
  border: 0.08889vh solid rgba(0, 142, 254, 0.6) !important;
  color: #C5D4FF !important;
  padding: 0.6vh 1.5vh !important;
  font-size: 1.07vh !important;
  border-radius: 0.35556vh !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  min-width: 5vh !important;
  text-align: center !important;
  white-space: nowrap !important;
  height: 3.2vh !important;
  line-height: 1.4 !important;
}

.area-btn:hover {
  background: rgba(69, 93, 220, 0.8) !important;
  border-color: rgba(0, 142, 254, 1) !important;
  color: #ffffff !important;
  transform: translateY(-0.1vh);
  box-shadow: 0 0.2vh 0.8vh rgba(0, 142, 254, 0.3);
}

.area-btn-active {
  background: rgba(15, 137, 245, 0.9) !important;
  border-color: rgba(0, 142, 254, 1) !important;
  color: #ffffff !important;
  font-weight: bold !important;
  box-shadow: 0 0.2vh 0.8vh rgba(15, 137, 245, 0.5);
  transform: scale(1.05);
}

.area-btn-active:hover {
  background: rgba(15, 137, 245, 1) !important;
  transform: scale(1.05) translateY(-0.1vh);
}

.sub-area-indicator {
  margin-left: 0.3vh;
  font-size: 0.9vh;
  opacity: 0.7;
  font-weight: bold;
}
</style>
