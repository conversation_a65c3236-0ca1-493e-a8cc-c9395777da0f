import request from '@/utils/request'
export function fetchList(data) {
  return request({
    url: '/pmslinkage/api/linkage/record/list',
    method: 'post',
    data
  })
}

export function fetchDetail(queryData) {
  return request({
    url: '/pmslinkage/api/linkage/record/detail',
    method: 'get',
    params: queryData
  })
}

export function fetchDelete(ids) {
  var data = { ids }
  return request({
    url: '/pmslinkage/api/linkage/record/delete',
    method: 'post',
    data
  })
}

export function fetchCreate(data) {
  return request({
    url: '/pmslinkage/api/linkage/record/create',
    method: 'post',
    data
  })
}

export function fetchUpdate(data) {
  return request({
    url: '/pmslinkage/api/linkage/record/update',
    method: 'post',
    data
  })
}

export function fetchPollList(data) {
  return request({
    url: '/pmslinkage/api/linkage/record/pollList',
    method: 'post',
    data
  })
}
