import request from '@/utils/request'

const url = '/flowService'

// 列表查询
export function orderRuleList(data) {
  return request({
    url: url + '/orderRule/list',
    method: 'post',
    data
  })
}
// 新增工单规则配置
export function orderRuleAddRule(data) {
  return request({
    url: url + '/orderRule/addRule',
    method: 'post',
    data
  })
}
// 更新工单规则配置
export function orderRuleUpdate(data) {
  return request({
    url: url + '/orderRule/update',
    method: 'put',
    data
  })
}

// 删除工单规则配置
export function orderRuleDelete(data) {
  return request({
    url: url + '/orderRule/delete',
    method: 'delete',
    data
  })
}
// 查询工单规则详情
export function orderRuleGet(id) {
  return request({
    url: url + '/orderRule/get/' + id,
    method: 'get'
  })
}
// 按条件查询单条规则(自动派单时使用)
export function slectRuleByCondition(data) {
  return request({
    url: url + '/orderRule/selectRuleByCondition',
    method: 'post',
    data
  })
}
// 一键开关
export function orderRuleUpdateState(params) {
  return request({
    url: url + '/orderRule/updateState',
    method: 'put',
    params
  })
}
// 查询人员ID和姓名列表(接单人)
export function selectMiniPersonList(data) {
  return request({
    url: '/userService/api/person/selectMiniPersonList',
    method: 'post',
    data
  })
}

