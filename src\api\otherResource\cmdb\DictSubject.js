import request from '@/utils/request'

const url = '/cmdbService'
export function _dictSubjectPage(data) {
  return request({
    url: '/userService/api/dict/list',
    method: 'post',
    data: data
  })
}

export function dictSubjectPage(data) {
  return request({
    url: url + '/api/sysType/page',
    method: 'post',
    data: data
  })
}

export function dictSubjectList(data) {
  return request({
    url: '/userService/api/dict/list',
    method: 'post',
    data: data
  })
}

export function getDictSubjectRel(id) {
  return request({
    url: url + '/dictSubjectRel/getCiTypeBySubjectId/' + id,
    method: 'get'
  })
}

export function updateBySusbjectId(data) {
  return request({
    url: url + '/dictSubjectRel/updateBySusbjectId',
    method: 'post',
    data: data
  })
}
