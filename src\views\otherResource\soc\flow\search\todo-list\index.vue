<template>
  <div class="app-container calendar-list-container">
    <div class="header-search" style="overflow: auto;height: unset;">
      <search v-model="query" @search="get" />
    </div>
    <div style="overflow: auto;height: unset;" class="content-table">
      <div class="table-div">
        <el-col :span="24" class="table-button">
          <el-button 
            v-waves 
            @click.stop="selectAll"
          >全选</el-button>
          <el-button 
            v-if="checkButtonPermission('324211')"
            v-waves 
            icon="el-icon-download" 
            :loading="exportLoading"
            @click.stop="handleExportExcel"
          >导出</el-button>
          <el-dropdown 
            v-if="checkButtonPermission('324212')"
            style="margin-left: 0.9260vh;"
          >
            <el-button>
              批量操作<i class="el-icon-arrow-down el-icon--right" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <el-button v-waves type="text" @click.stop="handleBatch(4)" style="background: rgba(0, 142, 254, 1); border-color: rgba(0, 142, 254, 1); color: #ffffff;">批量签单</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button v-waves type="text" @click.stop="handleBatch(5)" style="background: rgba(0, 142, 254, 1); border-color: rgba(0, 142, 254, 1); color: #ffffff;">批量回单</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button v-waves type="text" @click.stop="handleBatch(3)" style="background: rgba(0, 142, 254, 1); border-color: rgba(0, 142, 254, 1); color: #ffffff;">批量转派</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button v-waves type="text" @click="handleOperate" style="background: rgba(0, 142, 254, 1); border-color: rgba(0, 142, 254, 1); color: #ffffff;">批量审核</el-button>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-col>
        <el-table ref="table" v-loading="waiting" :data="dataList" v-bind="tableConfig"
          @selection-change="handleSelectionChange" @sort-change="sortChange">
          <el-table-column type="selection" width="55" fixed="left" />
          <el-table-column width="100" type="index" label="序号" v-bind="columnConifg" />
          <el-table-column min-width="23.1500vh" prop="alarmMsg" label="工单内容（告警信息）" sortable="custom"
            v-bind="columnConifg" />
          <el-table-column min-width="23.1500vh" prop="resourceName" label="资源名称" sortable="custom" v-bind="columnConifg" />
          <el-table-column min-width="13.8900vh" prop="assetTypeName" label="设备类型" sortable="custom"
            v-bind="columnConifg" />
          <el-table-column min-width="13.8900vh" prop="comment" label="处理意见" sortable="custom" v-bind="columnConifg" />
          <el-table-column min-width="13.8900vh" prop="createUserName" label="发起人" sortable="custom"
            v-bind="columnConifg" />
          <el-table-column min-width="13.8900vh" prop="receiverName" label="接收人" sortable="custom" v-bind="columnConifg" />
          <el-table-column min-width="13.8900vh" prop="orderLevel" label="工单级别" sortable="custom" v-bind="columnConifg" />
          <el-table-column min-width="13.8900vh" prop="influenceScope" label="影响范围" sortable="custom" v-bind="columnConifg">
            <template slot-scope="scope">
              <span>
                {{ scope.row.influenceScope === '1' ? '大' : scope.row.influenceScope === '2' ? '中'
                  : scope.row.influenceScope === '3' ? '小' : '' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column min-width="13.8900vh" prop="orderType" label="工单状态" sortable="custom" v-bind="columnConifg" />
          <el-table-column min-width="11.1120vh" prop="assetOnlineStatus" label="在线状态" sortable="custom"
            v-bind="columnConifg">
            <template slot-scope="scope">
              <span v-if="scope.row.assetOnlineStatus === null">-</span>
              <el-tag v-else :type="scope.row.assetOnlineStatus === 0 ? 'info' : 'success'">{{
                scope.row.assetOnlineStatus === 0 ? '离线' : '在线' }}</el-tag>
            </template>
          </el-table-column>
          <!-- <el-table-column min-width="11.1120vh" prop="assetAlarmStatus" label="告警信息" sortable="custom"
            v-bind="columnConifg">
            <template slot-scope="scope">
              <span v-if="scope.row.assetAlarmStatus === null">-</span>
              <el-tag v-else :type="scope.row.assetAlarmStatus === 1 ? 'danger' : 'success'">{{
                scope.row.assetAlarmStatus === 1 ? '有' : '无' }}</el-tag>
            </template>
          </el-table-column> -->
          <el-table-column min-width="13.8900vh" prop="signUserName" label="签单人" sortable="custom" v-bind="columnConifg" />
          <el-table-column min-width="13.8900vh" prop="areaName" label="设备所在地" sortable="custom" show-overflow-tooltip />
          <el-table-column min-width="13.8900vh" prop="dispatchTime" label="派单时间" sortable="custom"
            :formatter="table.formatMinute" v-bind="columnConifg" />
          <el-table-column min-width="13.8900vh" prop="updateTime" label="更新时间" sortable="custom"
            :formatter="table.formatMinute" v-bind="columnConifg" />
          <el-table-column v-bind="columnFixConifg" :width="tableButtonWidth">
            <template slot-scope="scope">
              <div v-if="hasAnyActionPermission(['324213'])">
                <template v-for="item in tableButtonList">
                  <el-button v-if="item.isShow(scope.row) && checkButtonPermission('324213')" :key="item.name + random.getUuid()" type="text"
                    @click="item.click(scope)">{{ item.name }}</el-button>
                </template>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <app-pager />
    <el-dialog v-dialogDrag title="回单方式" :visible.sync="dialogVisible" append-to-body :before-close="handleClose"
      width="17.78vh">
      <div :class="isDarkBg == true ? 'detailDiv' : 'detailDivs'">
        <div v-for="(item, index) in types" :key="index" style="margin:0.4630vh auto; text-align: center">
          <el-button type="primary" @click="dealClick(item.value)" style="background: rgba(0, 142, 254, 1); border-color: rgba(0, 142, 254, 1); color: #ffffff;">{{ item.label }}</el-button>
        </div>
      </div>
    </el-dialog>
    <handle-batch ref="handleBatch" @success="get" />
    <batch-model v-if="isShowBatch" :current-data="selected" @success="successBatch" @close="closeBatch" />
  </div>
</template>
<script>
import search from '../search'
import batchModel from './batchModel'
import handleBatch from './handleBatch'
import { webSign, flowableSign } from '@/api/otherResource/flow'
import { listPage } from '@/views/otherResource/soc/mixins'
import { flowListPage } from '@/views/otherResource/soc/flow/mixins/'
import download from '@/views/otherResource/soc/mixins/download'
import permission from '@/views/otherResource/soc/mixins/permission'
export default {
  components: {
    batchModel,
    search,
    handleBatch
  },
  mixins: [listPage, flowListPage, download, permission],
  data() {
    return {
      exportLoading: false,
      current: {},
      dialogVisible: false,
      isDarkBg: false,
      types: [{ label: '维修', value: 1 }, { label: '更换', value: 2 }, { label: '其他', value: 5 }],
      selected: [],
      query: {
        page: 1,
        limit: 10,
        orderBy: '',
        order: ''
      },
      isShowBatch: false
    }
  },
  computed: {
    tableButtonList() {
      return [
        { name: '详情', isShow: (row) => row.orderType, click: (scope) => this.openDone({ ...scope.row }) },
        { name: '签单', isShow: (row) => ['instance_service', 'technical_support'].includes(row.modelKey) && row.orderType === '待签单', click: (scope) => this.handleSign({ ...scope.row }) },
        { name: '回单', isShow: (row) => ['instance_service', 'technical_support'].includes(row.modelKey) && ['待回单', '已挂起', '驳回待处理'].includes(row.orderType), click: (scope) => this.handleReply(scope.row) },
        { name: '转派', isShow: (row) => ['instance_service', 'technical_support'].includes(row.modelKey) && ['待回单', '驳回待处理'].includes(row.orderType), click: (scope) => this.dealClick(3, scope.row) },
        { name: '挂起', isShow: (row) => ['instance_service', 'technical_support'].includes(row.modelKey) && ['待回单', '驳回待处理'].includes(row.orderType), click: (scope) => this.dealClick(4, scope.row) },
        { name: '审核', isShow: (row) => ['instance_service', 'technical_support'].includes(row.modelKey) && row.orderType === '待审核', click: (scope) => this.openTodo({ ...scope.row }) }
      ]
    }
  },
  created() {
    this.addMessageListener('refreshTaskDone', this.get)
    if (this.$route.query.id) this.$set(this.query, 'keyName', this.$route.query.id)
    this.get()
  },
  methods: {
    get() {
      const data = { ...this.query }
      if (data.areaId) {
        data.areaId = data.areaId[data.areaId.length - 1]
      }
      this.getBase({
        url: '/flowService/act-query/getNewTodoList',
        data,
        method: 'post'
      })
    },
    handleSign(data) {
      this.$confirm('确认签单吗？', '提示', { type: 'warning' })
        .then(() => {
          const type = data.modelKey === 'instance_service' ? 'instance-service-proc' : 'technical-support'
          webSign(data.procInstId, type).then(res => {
            this.$message({
              showClose: true,
              message: `签单成功，请在 ${res.data.data} 之前进行回单`,
              type: 'success',
              duration: 3000
            })
            this.get()
          })
        }).catch(() => {
        })
    },
    dealClick(index, row) {
      const data = row || this.current
      this.openTodo({ ...data, dealType: index })
      this.handleClose()
    },
    sortChange(row) {
      this.query.orderBy = row.prop
      this.query.order = row.order === 'ascending' ? 'ASC' : row.order === 'descending' ? 'DESC' : null
      this.get()
    },
    handleReply(data) {
      if (data.modelKey === 'technical_support') {
        this.dealClick(6, data) // 6 技术支持流程
        return
      }
      this.current = data
      this.dialogVisible = true
    },
    handleClose() {
      this.current = {}
      this.dialogVisible = false
    },
    // 全选及批量回单
    handleSelectionChange(val) {
      this.selected = val
    },
    selectAll() {
      for (let index = 0; index < this.dataList.length; index++) {
        const element = this.dataList[index]
        this.$refs.table.toggleRowSelection(element, true)
      }
    },
    handleBatch(dealType) {
      if (this.selected.length === 0) {
        this.Alert({ alertType: 'nodata' })
        return
      }
      // 批量回单
      if (dealType === 5) {
        // 所选列表中存在orderType不是待回单且不是已挂起的工单就提示
        if (this.selected.every(item => ['待回单', '已挂起'].includes(item.orderType))) {
          this.$refs.handleBatch.open(this.selected, dealType)
        } else {
          this.$message.error('所选工单环节不一致')
        }
        return
      }
      // 批量签单
      if (dealType === 4) {
        if (this.selected.every(item => ['待签单'].includes(item.orderType))) {
          this.$confirm('确认批量签单吗？', '提示', { type: 'warning' })
            .then(() => {
              const params = this.selected.map(item => item.procInstId)
              flowableSign(params).then(res => {
                this.$message({
                  showClose: true,
                  message: `签单成功`,
                  type: 'success',
                  duration: 3000
                })
                this.get()
              })
            }).catch(() => {
            })
        } else {
          this.$message.error('所选工单环节不一致')
        }
        return
      }

      this.$refs.handleBatch.open(this.selected, dealType)
    },
    // 列表导出
    handleExportExcel() {
      // 处理loading
      this.exportLoading = true
      const data = { ...this.query }
      if (data.areaId) {
        data.areaId = data.areaId[data.areaId.length - 1]
      }
      delete data.page
      delete data.limit
      const url = '/flowService/act-query/todoListExportExcel'
      const title = `待办工单导出报表`

      this.download({
        url,
        data,
        method: 'post',
        fileName: `${title}.xlsx`
      }, 'application/vnd.ms-excel').then(res => {
        this.exportLoading = false
      }).catch(() => {
        this.exportLoading = false
      })
    },
    handleOperate() {
      const isCheck = this.selected.every(item => item.orderType === '待审核')
      if (!isCheck) {
        this.$message.error('所选工单状态不属于待审核环节')
        return
      }
      if (this.selected.length === 0) {
        this.Alert({ alertType: 'nodata' })
        return
      }
      this.isShowBatch = true
    },
    successBatch() {
      this.isShowBatch = false
      this.get()
    },
    closeBatch() {
      this.isShowBatch = false
    }
  }
}
</script>
<style scoped>
.app-container {
  border-radius: 0.3704vh;
  box-shadow: 0 0.1852vh 1.1112vh 0 rgba(0, 0, 0, 0.1);
  padding: 0;
  background-color: inherit !important;
  box-shadow: none !important;
}

.header-search {
  /* position: relative; */
  /* min-width: 91.6740vh; */
  background-color: rgba(37, 46, 63, 0.6);
  border-radius: 0.3704vh;
  padding: 1.8520vh 0vh 1.8520vh 1.3890vh;
}

.content-table {
  border-radius: 0.3704vh;
  box-shadow: 0 0.1852vh 1.1112vh 0 rgba(0, 0, 0, 0.1);
  padding: 0.9260vh;
  margin-top: 0.9260vh;
  flex: 1;
  background-color: rgba(37, 46, 63, 0.6);
}

.table-div {
  padding-top: 1vh;
}

.table-button {
  margin-top: 0.9260vh;
  margin-bottom: 0.9260vh;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}

.bottom {
  display: flex;
  justify-content: flex-end;
  margin-top: 0.9260vh;
}

.batch-button {
  position: fixed;
  right: 5.5560vh;
  z-index: 2;
  top: 15.7420vh;
}

.el-table .small-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  width: 100%;
  height: 100%;
  text-wrap: balance;
  padding-left: 0.9260vh;
  padding-right: 0.9260vh;
}

.cell-image {
  margin-right: 1.3890vh;
  width: 0.9260vh;
  height: 0.9260vh;
}

.cell-nomal {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 详情样式 */
.detailDiv {
  background-color: var(--theme-color);
  font-size: 1.3890vh;
  max-height: 74.0800vh;
  min-height: 37.0400vh;
  overflow-y: auto;
}
.detailDiv .label-name {
  color: #98bcdb;
  display: inline-block;
  width: 11.1120vh;
  text-align: right;
  font-weight: 500;
  margin-right: 0.9260vh;
}
.detailDiv .label-value {
  color: #d8d8d8;
  display: inline-block;
  word-break: break-all;
  margin-bottom: 1.3890vh;
}
.detailDiv :deep(.el-select) {
  width: 100%;
}

.detailDivs {
  font-size: 1.3890vh;
  max-height: 74.0800vh;
  min-height: 37.0400vh;
  overflow-y: auto;
}
.detailDivs .label-name {
  color: #666;
  display: inline-block;
  width: 11.1120vh;
  text-align: right;
  font-weight: 500;
  margin-right: 0.9260vh;
}
.detailDivs .label-value {
  color: #000;
  display: inline-block;
  word-break: break-all;
  margin-bottom: 1.3890vh;
}
.detailDivs :deep(.el-select) {
  width: 100%;
}

.view_item {
  margin-bottom: 1.3890vh;
}

.form-item-texarea {
  margin-top: 0.9260vh;
}
.form-item-texarea textarea {
  min-height: 7.4080vh;
}

.row-hover {
  cursor: pointer;
}

.image-button {
  cursor: pointer;
  margin-right: 0.9260vh;
}

.tab-style {
  cursor: pointer;
  width: 9.2600vh;
  font-size: 1.3890vh;
  line-height: 2.7780vh;
  height: 5.5560vh;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.un-tab-style {
  cursor: pointer;
  width: 9.2600vh;
  font-size: 1.3890vh;
  line-height: 2.7780vh;
  height: 5.5560vh;
  font-weight: normal;
  display: flex;
  align-items: center;
  justify-content: center;
}

.batch-view {
  margin-top: 0.9260vh;
  margin-bottom: 0.9260vh;
}
</style>
