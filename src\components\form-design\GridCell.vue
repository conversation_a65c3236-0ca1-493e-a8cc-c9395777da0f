<template>
  <div class="cell" :class="{'cell-active': data.key === $store.state.formDesign.activeKey}">
    <div @click="activeCell">
      <el-form-item
        v-if="data.type !== 'title'"
        :label="data.title+`${data.options.required?'（必填）':''}`"
        :prop="data.key"
      >
        <el-input
          v-if="data.type === 'input'"
          v-model="data.options.defaultValue"
          :placeholder="data.options.placeholder"
          :disabled="data.options.disabled"
          :readonly="true"
          :style="{width: data.options.width}"
        />
        <el-input
          v-if="data.type === 'textarea'"
          v-model="data.options.defaultValue"
          :placeholder="data.options.placeholder"
          :disabled="data.options.disabled"
          :readonly="true"
          type="textarea"
          :rows="5"
          :style="{width: data.options.width}"
        />
        <el-input-number
          v-if="data.type === 'number'"
          :disabled="data.options.disabled"
          :readonly="true"
          :style="{width: data.options.width}"
        />
        <el-radio-group
          v-if="data.type === 'radio'"
          v-model="data.options.defaultValue"
          :disabled="data.options.disabled"
          :readonly="true"
          :style="{width: data.options.width}"
        >
          <el-radio
            v-for="(item, i) in data.options.option"
            :key="i"
            :label="item.value"
          >{{ item.label }}</el-radio>
        </el-radio-group>
        <el-checkbox-group
          v-if="data.type === 'checkbox'"
          v-model="data.options.defaultValue"
          :disabled="data.options.disabled"
          :readonly="true"
          :style="{width: data.options.width}"
        >
          <el-checkbox
            v-for="(item, i) in data.options.option"
            :key="i"
            :label="item.value"
          >{{ item.label }}</el-checkbox>
        </el-checkbox-group>
        <el-select
          v-if="data.type === 'select'"
          :placeholder="data.options.placeholder"
          :style="{width: data.options.width}"
          :readonly="true"
          :disabled="data.options.disabled"
        >
          <el-option
            v-for="(item, i) in data.options.option"
            :key="i"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-switch
          v-if="data.type === 'switch'"
          v-model="data.options.defaultValue"
          active-color="#13ce66"
          inactive-color="#EEEEEE"
          :style="{width: data.options.width}"
          :readonly="true"
          :disabled="data.options.disabled"
        />
        <el-date-picker
          v-if="data.type === 'datetime'"
          type="datetime"
          :placeholder="data.options.placeholder"
          :style="{width: data.options.width}"
          :disabled="data.options.disabled"
        />
      </el-form-item>
      <p
        v-if="data.type === 'title'"
        :style="{'text-align': data.options.align, 'font-size': data.options.fontSize}"
        @click="activeCell"
      >{{ data.value }}</p>
    </div>
    <i
      v-show="data.key === $store.state.formDesign.activeKey && data.type != 'grid'"
      class="action-copy"
      title="复制"
      @click="copyForm"
    />
    <i
      v-show="data.key === $store.state.formDesign.activeKey"
      class="action-delete"
      title="删除"
      @click="deleteForm"
    />
  </div>
</template>

<script>
import common from '@/utils/common'

export default {
  components: {},
  props: {
    formAttr: {
      type: Object,
      default: function() {
        return {
          align: 'top',
          size: 'medium',
          labelWidth: 80
        }
      }
    },
    data: {
      type: Object,
      default: function() {
        return {
          type: '',
          name: '',
          options: {
            width: 24,
            defaultValue: '',
            required: false,
            dataType: 'string',
            placeholder: ''
          },
          key: '1556775967000_4898'
        }
      }
    },
    isGrid: {
      type: Boolean,
      default: false
    },
    fDkey: {
      type: String,
      default: ''
    },
    fDindex: {
      type: Number
    }
  },
  methods: {
    copyForm() {
      const formListAll = common.deepClone(this.$store.state.formDesign.formList)
      const formList = common.deepClone(this.$store.state.formDesign.grid[this.FDkey])
        .cols[this.FDindex].list
      const grid = common.deepClone(this.$store.state.formDesign.grid[this.FDkey])
      let newIndex
      for (let i = 0; i < formList.length; i++) {
        const element = formList[i]
        if (element.key === this.data.key) {
          newIndex = i
          break
        }
      }
      const copyForm = common.deepClone(formList[newIndex])
      copyForm.key = 'copyForm_' + common.getGuidNew()
      formList.splice(newIndex + 1, 0, copyForm)
      this.$store.commit(
        'formDesign/updateActiveForm',
        common.deepClone(copyForm)
      )
      this.$store.commit('formDesign/updateActiveKey', copyForm.key)
      grid.cols[this.FDindex].list = formList

      this.$store.commit('formDesign/updateGrid', {
        key: this.FDkey,
        value: common.deepClone(grid)
      })
      for (let i = 0; i < this.$store.state.formDesign.formList.length; i++) {
        const element = this.$store.state.formDesign.formList[i]
        if (element.key === this.FDkey) {
          formListAll[i] = common.deepClone(grid)
        }
      }
      this.$store.dispatch(
        'formDesign/setFormList',
        common.deepClone(formListAll)
      )
      this.$emit('syncList', formList, this.FDindex)
    },
    deleteForm() {
      const formListAll = common.deepClone(this.$store.state.formDesign.formList)
      const formList = common.deepClone(this.$store.state.formDesign.grid[this.FDkey])
        .cols[this.FDindex].list
      const grid = common.deepClone(this.$store.state.formDesign.grid[this.FDkey])
      let newIndex
      for (let i = 0; i < formList.length; i++) {
        const element = formList[i]
        if (element.key === this.data.key) {
          newIndex = i
          break
        }
      }
      formList.splice(newIndex, 1)
      grid.cols[this.FDindex].list = formList
      console.log('formList', formList)
      console.log('grid', grid)
      console.log('formListAll', formListAll)
      console.log('this.FDindex', this.FDindex)

      for (let i = 0; i < this.$store.state.formDesign.formList.length; i++) {
        const element = this.$store.state.formDesign.formList[i]
        if (element.key === this.FDkey) {
          formListAll[i] = grid
        }
      }

      this.$store.dispatch(
        'formDesign/setFormList',
        common.deepClone(formListAll)
      )

      // this.$emit("syncList", formList, this.FDindex);

      this.$store.commit('formDesign/updateGrid', {
        key: this.FDkey,
        value: common.deepClone(grid)
      })

      if (newIndex !== 0) {
        this.$store.commit(
          'formDesign/updateActiveKey',
          formList[newIndex - 1].key
        )
        this.$store.commit(
          'formDesign/updateActiveForm',
          common.deepClone(formList[newIndex - 1])
        )
      } else {
        if (formList.length > 0) {
          this.$store.commit('formDesign/updateActiveKey', formList[0].key)
          this.$store.commit(
            'formDesign/updateActiveForm',
            common.deepClone(formList[0])
          )
        }
      }
    },
    activeCell() {
      this.$store.commit('formDesign/updateActiveKey', this.data.key)
      this.$store.commit('formDesign/updateShowType', this.data.type)
      this.$store.commit(
        'formDesign/updateActiveForm',
        common.deepClone(this.data)
      )
    }
  }
}
</script>

<style lang="css" scoped>
.cell {
  background-color: #2ebfb9;
  padding: 0.9260vh 0.9260vh 1.8520vh 0.9260vh;
  position: relative;
  cursor: move;
}
.cell-active {
  background-color: #50f5de;
  border-left: 0.4630vh solid #50f5de;
}
.el-form-item {
  margin-bottom: 0;
}
.action-copy {
  position: absolute;
  bottom: -1.3890vh;
  right: 5.5560vh;
  height: 2.7780vh;
  width: 2.7780vh;
  background: url("/static/img/copy.png") no-repeat center;
  background-size: 1.6668vh 1.6668vh;
  background-color: #ecf5ff;
  border-color: #409eff;
  border-radius: 50%;
  cursor: pointer;
  border: 0.0926vh solid #409eff;
  z-index: 1;
}
.action-copy:hover {
  background: url("/static/img/copy-active.png") no-repeat center;
  background-color: #409eff;
}
.action-delete {
  position: absolute;
  bottom: -1.3890vh;
  right: 1.3890vh;
  height: 2.7780vh;
  width: 2.7780vh;
  background: url("/static/img/delete.png") no-repeat center;
  background-size: 1.3890vh 1.3890vh;
  background-color: #fef0f0;
  border-radius: 50%;
  cursor: pointer;
  border: 0.0926vh solid #f56c6c;
  z-index: 1;
}
.action-delete:hover {
  background: url("/static/img/delete-active.png") no-repeat center;
  background-color: #f56c6c;
}
</style>
