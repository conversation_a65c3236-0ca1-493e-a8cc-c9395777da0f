<template>
  <!-- 电表配置弹窗 -->
  <div>
    <el-dialog ref="meterDialog" width="60.19vh" title="电表配置" :visible="dialogVisible" @close="dialogVisible = false"
      @open="handleDialogOpen">
      <!-- 弹窗内容 -->
      <div class="container">
        <div class="number2">厂商代码：</div>
        <div>
          <el-select v-model="value1" clearable placeholder="请选择厂商代码" style="width: 150%">
            <el-option v-for="(item, index) in options1" :key="item.label" :label="item.label" :value="item.value"
              @click.native="handleManufacturerConfirm(index)" />
          </el-select>
        </div>
      </div>
      <div class="container">
        <div class="number2">协议类型：</div>
        <div>
          <el-select v-model="value2" clearable placeholder="请选择协议类型" style="width: 150%">
            <el-option v-for="(item, index) in options2" :key="item.value" :label="item.label" :value="item.value"
              @click.native="handleProtocolConfirm(index)" />
          </el-select>
        </div>
      </div>
      <div class="p1">
        <p style="font-weight: bold; font-size: 1.48vh; color: red">电表编号顺序为从左往右依次编号，务必严格遵守!</p>
      </div>
      <div>
        <div v-for="(meter, index) in meters" :key="index" class="meter-item">
          <div class="number1">第 {{ index + 1 }} 路电表：</div>
          <el-input v-model="meter.config" placeholder="请输入" style="width: 50%" :on-input="handleInputChange(index)" />
          <div style="display: flex; justify-content: flex-end;">
            <el-button style="margin: 1.39vh" type="danger" icon="el-icon-minus" circle @click="deleteMeter(index)" />
          </div>
        </div>
      </div>
      <div style="display: flex; justify-content: flex-end; margin-right: 7.22vh">
        <el-button style="margin: 1.39vh" type="primary" icon="el-icon-plus" circle @click="addMeter" />
      </div>
      <div style="display: flex; justify-content: flex-start; margin-top:1.85vh; margin-left: 14.8vh">
        <el-button type="primary" @click="saveForm">提交</el-button>
        <el-button type="default" @click="resetForm">重置</el-button>
      </div>
    </el-dialog>

    <div class="app-container calendar-list-container">
      <el-row :gutter="10">
        <el-col :span="24">
          <div class="filter-container" style="display: flex">
            <!-- 引入公共查询设备组件 -->
            <deviceSearch ref="deviceSearch" :share-sys-type="shareSysType" :is-tag="true"
              :is-muti-search="isMutiSearch" :is-area="isArea" :is-sys-type="isSysType" :is-device-type="isDeviceType"
              :is-device-type-source="isDeviceTypeSource" :is-manufacturer-code="isManufacturerCode"
              @handleData="handleData">
              <template v-slot:after>
                <el-select v-if="$route.query.sysType === '7' || $route.query.sysType === '5'"
                  v-model="listQuery.reachableState" clearable placeholder="请选择IP连接状态" class="form-item-width">
                  <el-option label="是" :value="1" />
                  <el-option label="否" :value="0" />
                </el-select>
                <el-select v-model="listQuery.onlineState" clearable placeholder="请选择在线状态" class="form-item-width">
                  <el-option label="在线" :value="1" />
                  <el-option label="离线" :value="0" />
                </el-select>
                <!--                <el-select v-model="listQuery.alarmState" clearable placeholder="请选择告警信息" style="margin-right: 10px"
                  class="form-item-width">
                  <el-option label="无" :value="0" />
                  <el-option label="有" :value="1" />
                </el-select> -->
                <el-button v-waves style="height: 3.24vh" type="primary" icon="el-icon-search" @click="handleFilter">{{
                  $t("button.search") }}</el-button>
                <el-button v-waves type="primary" style="height: 3.24vh" @click="$refs.deviceSearch.clear(listQuery)">{{
                  $t("button.clear") }}</el-button>
                <el-button 
                  v-if="shareSysType > 0 " 
                  type="primary" 
                  style="height: 3.24vh"
                  icon="el-icon-circle-plus-outline" 
                  @click="handleCreate"
                >批量指令下发</el-button>
                <el-button 
                  v-if="checkButtonPermission('323101')"
                  type="primary" 
                  :loading="exportLoading" 
                  icon="el-icon-download"
                  @click.stop="handleExportExcelServer"
                >导出</el-button>
              </template>
            </deviceSearch>

            <!-- <el-button
          style="height: 3.24vh"
          @click="refresh"
          type="primary"
          icon="el-icon-refresh"
          >刷新{{ timeRemaining }}s</el-button
        > -->
            <!-- 可选择几秒刷新 -->
            <!-- <div class="setting-btn">
          <el-popover placement="bottom" width="180" trigger="hover">
            <template>
              <el-radio-group @change="changeSetTime" v-model="setTime">
                <el-radio style="margin-bottom: 10px" :label="10">10s</el-radio>
                <el-radio style="margin-bottom: 10px" :label="20">20s</el-radio>
                <el-radio style="margin-bottom: 10px" :label="30">30s</el-radio>
                <el-radio style="margin-bottom: 10px" :label="40">40s</el-radio>
                <el-radio :label="50">50s</el-radio>
                <el-radio :label="60">60s</el-radio>
              </el-radio-group>
            </template>
            <div slot="reference" class="set-time">
              <i class="el-icon-setting"></i>
            </div>
          </el-popover>
          <div class="time" @click="refresh">{{ timeRemaining }}s</div>
        </div> -->
          </div>

          <el-table v-loading="listLoading" :data="list" border stripe fit highlight-current-row style="width: 100%"
            sortable @sort-change="sortChange" @selection-change="handleSelectionChange">
            <!-- <el-table-column type="selection" width="55" /> -->
            <el-table-column type="index" align="center" :label="$t('table.id')" width="55" />
            <el-table-column label="设备名称" show-overflow-tooltip sortable="custom" prop="deviceName" min-width="120">
              <template slot-scope="scope">{{ scope.row.deviceName }}</template>
            </el-table-column>
            <el-table-column label="设备编号" show-overflow-tooltip sortable="custom" prop="deviceSerno" min-width="120">
              <template slot-scope="scope">{{ scope.row.deviceSerno }}</template>
            </el-table-column>
            <el-table-column label="区域" show-overflow-tooltip>
              <template slot-scope="scope">{{ scope.row.belongArea }}</template>
            </el-table-column>
            <el-table-column label="所属路口" show-overflow-tooltip>
              <template slot-scope="scope">{{ scope.row.interSectionName || '-' }}</template>
            </el-table-column>
            <el-table-column v-if="$route.path === '/instanceManagement/equipmentQuery'" label="专题类型"
              show-overflow-tooltip>
              <template slot-scope="scope">{{ scope.row.sysTypeName }}</template>
            </el-table-column>
            <el-table-column label="设备类型" show-overflow-tooltip>
              <template slot-scope="scope">{{ scope.row.deviceTypeName }}</template>
            </el-table-column>
            <el-table-column label="厂商" show-overflow-tooltip>
              <template slot-scope="scope">{{ scope.row.manufacturerName }}</template>
            </el-table-column>
            <el-table-column label="型号" show-overflow-tooltip>
              <template slot-scope="scope">{{ scope.row.equipType }}</template>
            </el-table-column>
            <el-table-column
              v-if="$route.query.sysType === '1' || $route.query.sysType === '7' || $route.query.sysType === '5'"
              label="IP连接" min-width="80px">
              <template slot-scope="scope">
                <span v-if="scope.row.reachableState === -1">-</span>
                <el-tag v-else :type="scope.row.reachableState === 1 ? 'success' : 'info'">
                  {{ scope.row.reachableState === 1 ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="在线状态" min-width="100px">
              <template slot-scope="scope">
                <el-tag :type="scope.row.onlineState === 0 ? 'info' : 'success'">
                  {{ scope.row.onlineState === 0 ? '离线' : '在线' }}
                </el-tag>
              </template>
            </el-table-column>
            <!--             <el-table-column label="告警信息" min-width="80px">
              <template slot-scope="scope">
                <el-tag :type="scope.row.alarmState === 1 ? 'danger' : 'success'">
                  {{ scope.row.alarmState === 1 ? '有' : '无' }}
                </el-tag>
              </template>
            </el-table-column> -->
            <el-table-column label="状态更新时间" min-width="130px" show-overflow-tooltip sortable="custom" prop="updateTime">
              <template slot-scope="scope">{{ scope.row.updateTime }}</template>
            </el-table-column>
            <el-table-column :label="$t('table.actions')" :width="shareSysType > 0 ? 240 : 180">
              <template slot-scope="scope">
                <div v-if="hasAnyActionPermission(['323102'])">
                  <el-popover 
                    v-if="shareSysType > 0" 
                    v-model="scope.row.show"
                    :disabled="scope.row.cmdOptionList.length === 0" 
                    placement="left-end" 
                    width="170" 
                    trigger="click"
                    :popper-options="{ boundariesElement: 'viewport', removeOnDestroy: true, }"
                  >
                    <div style="display: flex; flex-direction: column">
                      <el-button v-for="(item, index) in scope.row.cmdOptionList" :key="index" size="medium" type="text"
                        class="btnTop" style="margin: 0" @click="operationCommand(item, scope.row)">{{ item.cmdName }}
                      </el-button>
                      <!-- 电表配置按钮 -->
                      <div style="display: flex; justify-content: center; margin: 10px 0;">
                        <el-button size="medium" type="text" class="btnTop" @click="openDialog('电表配置', scope.row)">
                          电表配置
                        </el-button>
                      </div>
                    </div>
                    <el-button slot="reference" type="text" size="medium"
                      :disabled="scope.row.cmdOptionList.length === 0">
                      操作指令</el-button>
                  </el-popover>
                  <el-button 
                    v-if="checkButtonPermission('323102')"
                    size="medium" 
                    type="text" 
                    style="margin-left: 0px" 
                    @click="sbxqShow(scope.row.deviceId)"
                  >详情</el-button>
                  <el-button v-if="false" 
                    size="medium" 
                    type="text" 
                    @click="queryLog(scope.row)"
                  >查询日志</el-button>
                  <!-- 注意以下按钮恢复后需要动态改变操作列列宽！！！ -->
                  <!-- <el-button v-if="scope.row.deviceName.includes('摄像机') && !isSysType" size="medium" type="text" style="margin-left: 0px" @click="videoCameraPassageway(scope.row)">通道</el-button> -->
                  <el-button 
                    v-if="isVideoCamera(scope.row) && scope.row.onlineState !== '0'" 
                    size="medium" 
                    type="text"
                    style="margin-left: 0px" 
                    @click="showPlayVideoDialog(scope.row)"
                  >点播</el-button>
                  <!-- v-if="shareSysType === 6" -->
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination-container">
            <el-pagination :current-page="listQuery.page" :page-sizes="[10, 20, 30, 50, 100, 200]"
              :page-size="listQuery.limit" layout="total, sizes, prev, pager, next, jumper" :total="total"
              @size-change="handleSizeChange" @current-change="handleCurrentChange" />
          </div>
        </el-col>
      </el-row>
      <el-dialog v-dialogDrag :close-on-click-modal="false" :visible.sync="dialogAddVisible" title="批量指令下发"
        width="101.85vh">
        <add :is-public="this.isPublic" :share-sys-type="listQuery.shareSysType" :show-dialog="dialogAddVisible"
          @handCancle="handCancle" />
      </el-dialog>
      <el-dialog v-dialogDrag class="layerManager" :title="deviceDetailModel.title" destroy-on-close
        :visible.sync="sbxqdialogFormVisible" :width="dialogWidth" height="37.04vh">
        <deviceDetailView ref="deviceDetailView" topo-id="deviceTopo2" destroy-on-close :key-value="deviceDetailModel"
          :show-dialog="sbxqdialogFormVisible" :device-id="deviceId" @handCancle="sbxqhandCancle"
          @getValueAgain="sbxqShow" @tabChange="tabChange" />
      </el-dialog>

      <!-- <el-dialog v-dialogDrag
      :close-on-click-modal="false"
      :visible.sync="dialogDetailVisible"
      title="详情"
      width="900px"
    >
      <detail
        :key-value="Id"
        :show-dialog="dialogDetailVisible"
        @handCancle="handCancle"
      />
    </el-dialog> -->
      <el-dialog v-dialogDrag :close-on-click-modal="false" :visible.sync="dialogSetVisible" title="参数配置" width="41.67vh">
        <parameter-set :key-value="Id" :command-type-vo="commandTypeVo" :show-dialog="dialogSetVisible"
          @handCancle="handCancle" />
      </el-dialog>
      <!-- 摄像头、激光雷达、毫秒雷达、RUS -->
      <el-dialog v-dialogDrag :close-on-click-modal="false" :visible.sync="dialogSetPageVisible" :title="title"
        width="60.19vh">
        <set-parameter-page :row-value="rowValue" :cmd-option-list-item="commandTypeVo"
          :show-dialog="dialogSetPageVisible" @handCancle="handCancle" />
      </el-dialog>
      <!-- 交管箱 -->
      <el-dialog v-dialogDrag :close-on-click-modal="false" :visible.sync="dialogLockVisible" :title="title"
        width="60.19vh">
        <el-form ref="channelForm" :model="channelForm" style="width: 50%; margin: 0 auto" label-width="4.63vh">
          <el-form-item label="通道" prop="value" :rules="[{ required: true, message: '请选择开锁通道', trigger: 'change' }]">
            <el-select v-model="channelForm.value" placeholder="请选择开锁通道" style="width: 75%" multiple clearable>
              <el-option v-for="(item, index) in ['一', '二', '三', '四', '五', '六']" :key="item" :label="'通道' + item"
                :value="index + 1" />
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="handCancle">{{ $t("button.cancel") }}</el-button>
          <el-button type="primary" :loading="lockLoading" @click="handOpenLock">{{
            $t("button.confirm")
          }}</el-button>
        </div>
      </el-dialog>
      <!-- 摄像机的通道 -->
      <div class="videoCameraDetail">
        <el-dialog v-dialogDrag :close-on-click-modal="false" :visible.sync="videoCameraVisible" :title="title"
          :width="videoCameraWidth">
          <videoCameraDetail :row-value="rowValue" :show-dialog="videoCameraVisible" @handCancle="handCancle" />
        </el-dialog>
      </div>
      <!-- 点播 -->
      <el-dialog v-dialogDrag :close-on-click-modal="false" :visible.sync="playVideoVisible" destroy-on-close
        title="视频播放" width="106.67vh">
        <playVideo :show-dialog="playVideoVisible" :row-value="rowValue" @handCancle="handCancle" />
      </el-dialog>

      <!-- 查询日志 -->
      <el-dialog v-dialogDrag :close-on-click-modal="false" :visible.sync="dialogLogVisible" title="查询日志"
        width="111.11vh">
        <logView :is-dialog="true" :log-data="logData" :show-dialog="dialogLogVisible" @handCancle="handLogCancle" />
      </el-dialog>
      <!-- 箱体智控器告警配置 -->
      <alarmConfig ref="alarmConfig" />
      <setCoversConfig ref="setCoversConfig" />
    </div>
  </div>
</template>

<script>
import {
  getProtocolTypeList,
  getVenderCodeList,
  configurationLockNet
} from '@/api/setting'
import deviceSearch from '@/components/deviceSearch/index'
import { getDeviceAttribute } from '@/api/main/admin'
import deviceDetailView from '@/views/dashboard/admin/dialogViews/deviceDetailView'
import alarmConfig from './alarmConfig.vue'
import setCoversConfig from './setCoversConfig'
import {
  fetchList,
  fetchCreate,
  areaCodeTree,
  rebootDevice,
  openLock,
  getLockGeneral,
  getLockCurrent
} from '@/api/otherSystem/equipmentManagement/equipmentQuery'
import { setLampUpdateTime, getLampUpdateTime } from '@/utils/auth.js'
import { mapState } from 'vuex'
import { json2excel } from '@/views/otherResource/cmdb/util/excel/setMethods.js'
import download from '@/views/otherResource/soc/mixins/download'
import permission from '@/views/otherResource/soc/mixins/permission'

export default {
  components: {
    deviceSearch,
    deviceDetailView,
    alarmConfig,
    setCoversConfig,
    // 分组
    // groupTree: () => import('@/components/groupTree'),
    add: () => import('./add'),
    // detail: () => import("./detail"),
    parameterSet: () => import('./parameterSet'),
    setParameterPage: () => import('./setParameterPage/index'),
    videoCameraDetail: () => import('./component/videoCameraDetail.vue'),
    playVideo: () => import('@/views/PMS/equipmentManagement/equipmentQuery/component/playVideo.vue'),
    // 查询日志
    logView: () => import('@/views/logSystem/sendEquipmentCommand/index.vue')
  },
  mixins: [download, permission],
  data() {
    return {
      value1: '', // 选中的厂商代码值
      options1: [], // 厂商代码选项
      options2: [],
      value2: '',
      meters: [
        { config: '', isValid: true } // 添加 isValid 属性来标记输入是否有效
      ], // 电表配置列表
      manufacturerPopupShow: false,
      protocolPopupShow: false,
      manufacturerValue: null,
      protocolValue: null,
      failDialogVisible: false,
      venderCodeListCode: null,
      ProtocolTypeListCode: null,
      manufacturerCode: null,
      protocolCode: null,
      dialogVisible: false,
      dialogWidth: '1000px',
      isMutiSearch: true, // 是否显示多项选择器(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
      isArea: true, // 是否显示区域查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
      isSysType: true, // 是否显示专题类型查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
      isDeviceType: true, // 是否显示设备类型查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
      isDeviceTypeSource: true, // 是否只显示有源设备(true:只显示有源设备 false:显示全部设备类型)
      isManufacturerCode: true, // 是否显示设备厂商和型号联动查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
      listQuery: {
        page: 1,
        limit: 10,
        isSpot: 0,
        alarmState: null,
        onlineState: null,
        shareSysType: '',
        order: '',
        sortBy: ''
      },
      total: 0,
      list: [],
      listLoading: false,
      exportLoading: false,
      dialogAddVisible: false,
      // dialogDetailVisible: false,
      videoCameraVisible: false,
      dialogSetVisible: false,
      sbxqdialogFormVisible: false,
      dialogSetPageVisible: false, // 摄像头、激光雷达、毫秒雷达、RUS
      dialogLockVisible: false,
      lockLoading: false,
      playVideoVisible: false,
      title: '',
      Id: -1,
      deviceDetailModel: {
        title: '照明设备'
      },
      commandTypeVo: {},
      instructionList: [],
      multipleSelection: [],

      statis: '',
      group: {},
      setTime: +getLampUpdateTime() || 60,
      timeRemaining: +getLampUpdateTime() || 60,
      timer: null,
      isPublic: 1,
      shareSysType: '',
      deviceId: null,
      rowValue: null,
      videoCameraWidth: '',
      defaultProps: {
        children: 'areaCodeTree',
        label: 'name'
      },
      channelForm: {
        value: []
      },
      areaCodeTree,
      dialogLogVisible: false,
      logData: null
    }
  },
  computed: {
    ...mapState({
      permission_routers: (state) => state.permission.routers
    }),
    isVideoCamera() {
      return (row) => {
        if (row) {
          if (row.deviceTypeName) {
            return row.deviceTypeName.includes('摄像') && parseInt(this.shareSysType)
          }
        }
      }
    }
  },

  watch: {
    sbxqdialogFormVisible(val) {
      if (!val) {
        this.dialogWidth = '1000px'
      }
    }
  },
  created() {
    var str = location.href // 取得整个地址栏
    var data = str.split('?')
    if (data.length > 1) { // 地址栏存在sysType参数
      var zz = '{"' + data[1].replace(/=/g, '":"').replace('&', '","') + '"}'
      var json = JSON.parse(zz)
      if (json.sysType > 0) {
        console.log(json.sysType, typeof json.sysType)
        this.listQuery.shareSysType = json.sysType
        this.shareSysType = json.sysType
      }
      this.isSysType = false
    } else {
      this.isSysType = true
    }
    console.log('------------------------', this.isSysType, json)
    // 默认查询容东的数据
    // this.listQuery['areaCode'] = '10'
    this.getList()
  },

  mounted() {

    // this.changeTimeRemaining();
  },
  methods: {
    openDialog(电表配置, row) {
      console.log('row:', row)
      this.commandTypeVo = row.cmdOptionList[7]
      console.log('commandTypeVo:', this.commandTypeVo)
      this.deviceId = row.deviceId
      console.log('deviceId:', this.deviceId)
      // 在点击按钮时调用接口
      getProtocolTypeList()
        .then(response => {
          console.log(351, response.data)
          // 处理协议类型列表
          const protocolTypesData = response.data.data
          this.protocolColumns = protocolTypesData.map(item => item.description)
          this.ProtocolTypeListCode = protocolTypesData.map(item => item.code) // 存储code字段
          // 处理厂商列表
          this.options2 = protocolTypesData.map(item => ({
            value: item.code,
            label: item.description
          }))
          console.log(2551, this.options2)
        })
        .catch(error => {
          console.error('Error fetching Protocol Type List:', error)
        })
      getVenderCodeList()
        .then(response => {
          console.log(355, response.data)
          // 处理厂商列表
          const venderCodeData = response.data.data
          this.manufacturerColumns = venderCodeData.map(item => item.description)
          this.venderCodeListCode = venderCodeData.map(item => item.code) // 存储code字段
          this.options1 = venderCodeData.map(item => ({
            value: item.code,
            label: item.name
          }))
          console.log(255, this.options1)
        })
        .catch(error => {
          console.error('Error fetching Vender Code List:', error)
        })
      this.dialogVisible = true
    },
    handleDialogOpen() {
      // 打开弹窗时，重置meters数组，只包含第一路电表的内容
      this.meters = [{ config: '' }]
      this.value1 = ''
      this.value2 = ''
    },
    handleDialogClose() {
      // 关闭对话框时的逻辑，如果有的话可以在这里添加
    },
    handleInputChange(index) {
      // 获取当前输入框的值
      const inputValue = this.meters[index].config

      // 判断输入是否为整数且在0-255范围内
      if (!/^\d+$/.test(inputValue) || inputValue < 0 || inputValue > 255) {
        // 标记为无效值
        this.meters[index].isValid = false
      } else {
        // 标记为有效值
        this.meters[index].isValid = true
      }
    },
    deleteMeter(index) {
      this.meters.splice(index, 1)
    },
    addMeter() {
      this.meters.push({ config: '' }) // 在电表配置列表末尾增加一个空配置对象
    },
    resetForm() {
      this.value1 = ''
      this.value2 = ''
      this.meters = [{ config: '' }] // 重置电表配置列表git commit -m "Your commit message here"
    },
    saveForm() {
      // 检查所有输入框的值
      let isValid = true
      this.meters.forEach((meter, index) => {
        if (!meter.isValid || (meter.config !== '' && (parseInt(meter.config) < 0 || parseInt(meter.config) > 255))) {
          // 如果输入不合法，则清空当前输入框的值并标记为无效
          this.meters[index].config = ''
          this.meters[index].isValid = false
          isValid = false
        } else {
          // 如果输入合法，则将输入值保存
          this.meters[index].config = parseInt(meter.config)
        }
      })

      // 如果存在不合法值，则弹出错误提示
      if (!isValid) {
        this.$message.error('请输入整数且范围在0-255之间的值')
        return
      }
      if (isValid) {
        // 构造需要发送给后端的数据对象
        const emeterCfg = {
          emeterAddress: this.meters.map(meter => meter.config.toString()), // 使用输入的电表内容作为 emeterAddress 的值
          protocolType: this.protocolCode,
          vendorCode: this.manufacturerCode
        }

        const requestData = {
          commandTypeVo: this.commandTypeVo,
          configurationLockNetVo: {
            emeterCfg: emeterCfg
          },
          deviceIds: [this.deviceId] // 使用 openDialog 方法中设置的 deviceId
        }

        console.log('发送给后端的参数：', requestData) // 打印传参
        // 调用 configurationLockNet 接口
        configurationLockNet(requestData)
          .then(response => {
            // 处理成功响应
            console.log('Configuration locked successfully:', response)
          })
          .catch(error => {
            // 处理错误
            console.error('Error locking configuration:', error)
          })
      }
      // 关闭对话框
      this.dialogVisible = false
    },
    // 处理 manufacturer 类型的确
    handleManufacturerConfirm(index) {
      this.manufacturerValue = index
      this.manufacturerCode = this.venderCodeListCode[index]
      console.log('manufacturerCode:', this.manufacturerCode) // 添加这行代码
      this.manufacturerPopupShow = false
    },
    // 处理 protocol 类型的确认
    handleProtocolConfirm(index) {
      this.protocolValue = index
      this.protocolCode = this.ProtocolTypeListCode[index] // 根据 index 选取对应的 ProtocolTypeListCode
      console.log('protocolCode:', this.protocolCode) // 添加这行代码
      this.protocolPopupShow = false
    },
    configureMeter(row) {
      // 处理电表配置按钮点击事件
      this.dialogVisible = true // 点击电表配置按钮后弹出弹窗
    },

    // 列表导出
    handleExportExcel() {
      if (!this.list?.length) {
        this.$alert('暂无可导出的数据', { type: 'warning' })
        return
      }

      // 处理loading
      this.exportLoading = true
      setTimeout(() => {
        this.exportLoading = false
      }, 1e4)

      const obj = Object.assign({}, this.listQuery)
      obj.page = 1
      obj.limit = 6000

      this.exportExcel(obj)
    },
    // 前端导出excel
    exportExcel(obj) {
      const tableData = []
      fetchList(obj).then((res) => {
        res.data.data.forEach((ele) => {
          tableData.push(ele)
        })

        const tHeader = [
          '设备名称',
          '设备编号',
          '设备类型',
          '区域',
          '厂商',
          '型号',
          '设备状态',
          '在线状态',
          '状态更新时间'
        ]
        const filterVal = [
          'deviceName',
          'deviceSerno',
          'deviceTypeName',
          'belongArea',
          'manufacturerName',
          'equipType',
          'alarmState',
          'onlineState',
          'updateTime'
        ]
        let text = '全部'

        switch (this.$route.query.sysType) {
          case '1':
            text = '回传网'
            break
          case '2':
            text = '边缘节点'
            break
          case '3':
            text = ''
            break
          case '4':
            text = '供电'
            break
          case '5':
            text = '视频监控'
            break
          case '6':
            text = '交管'
            break
          case '7':
            text = '道路'
            break
        }
        const title = `${text}设备导出报表`
        const excelData = [
          {
            tHeader: tHeader, // sheet表一头部
            filterVal: filterVal, // 表一的数据字段
            tableDatas: tableData.map(i => {
              i.alarmState = ['正常', '告警'][i.alarmState] || '未知'
              i.onlineState = ['离线', '在线'][i.onlineState] || '未知'
              return i
            }), // 表一的整体json数据
            sheetName: title // 表一的sheet名字
          }
        ]

        json2excel(excelData, title, true, 'xlsx')
        this.exportLoading = false
      }).catch(() => {
        this.exportLoading = false
      })
    },
    // 后端导出excel
    handleExportExcelServer() {
      // 处理loading
      this.exportLoading = true
      const data = { ...this.listQuery }
      delete data.page
      delete data.limit
      !this.$route.query.sysType && (data.isOverView = 1)
      const url = '/szdlService/api/deviceManage/exportExcel'
      const title = `${['', '回传网', '边缘节点', '', '供电', '视频监控', '交管', '道路'][~~this.$route.query.sysType] || ''}设备导出报表`

      this.download({
        url,
        data,
        method: 'post',
        fileName: `${title}.xlsx`
      }, 'application/vnd.ms-excel').then(res => {
        this.exportLoading = false
      }).catch(() => {
        this.exportLoading = false
      })
    },
    handLogCancle(reload) {
      if (reload) {
        this.t = setInterval(() => {
          this.getList(false)
        }, 3000)
      } else {
        this.getList(false)
      }
      this.dialogLogVisible = false
    },
    handleData(data) {
      this.listQuery = Object.assign(this.listQuery, data) // 接收并合并公共组件查询条件
      // 备注：data的属性值如下
      // data = {
      //     shareAreaCode: '', //区域(非必填)
      //     shareSysType: '', //专题类型(非必填)
      //     shareDeviceType: '', //设备类型(非必填)
      //     shareManufacturerCode: '', //设备厂商(非必填)
      //     shareEquipType: '', //型号(非必填)，注意，型号是根据设备厂商级联
      //     shareDeviceName: '', //设备名称/设备编号(非必填)
      // }
    },
    queryLog(data) {
      this.logData = data
      this.dialogLogVisible = true
      // console.log(data);
      // var menuId = 1759;
      // var val = "/logSystem/sendEquipmentCommand";
      // this.$router.push({ path: val, query: { data: data }});
      // this.$store.dispatch("");
      // this.getMenu(val, menuId);
      // console.log("结果集：" + this.currentMenuItem);
      // this.$store
      //     .dispatch("ChooseRouters", this.currentMenuItem)
      //     .then(() => {})
      //     .catch(() => {
      //         console.log("修改左侧菜单失败！");
      //     });
    },
    getMenu(val, menuId) {
      this.permission_routers.forEach((item) => {
        this.getDistrict(item, val, menuId, null)
      })
    },
    getDistrict(item, val, menuId, parentItem) {
      if (menuId === item.menuId) {
        this.currentMenuItem = parentItem
        return false
      } else {
        if (item.children && item.children.length > 0) {
          item.children.forEach((element) => {
            this.getDistrict(element, val, menuId, item)
          })
        }
      }
    },
    refresh() {
      this.getList()
    },
    changeSetTime(val) {
      setLampUpdateTime(val)
      this.timeRemaining = val
      window.clearInterval(this.timer)
      this.timer = null
      this.changeTimeRemaining()
      this.$nextTick(() => {
        this.setTime = val
      })
    },

    changeTimeRemaining() {
      window.clearInterval(this.timer)
      this.timer = null
      this.timer = setInterval(() => {
        this.timeRemaining = this.timeRemaining - 1
        if (this.timeRemaining === 0) {
          this.timeRemaining = this.setTime
          this.getList()
        }
      }, 1000)
    },
    getList() {
      this.listQuery['areaCode'] = this.listQuery.shareAreaCode
      this.listLoading = true
      fetchList(this.listQuery).then((res) => {
        this.list = res.data.data
        this.total = res.data.total
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    handleSizeChange(val) {
      this.listQuery.limit = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.getList()
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    sortChange(row) {
      this.listQuery.sortBy = row.prop
      this.listQuery.order = row.order === 'ascending' ? 0 : row.order === 'descending' ? 1 : null
      this.handleCurrentChange(1)
    },
    handCancle(reload) {
      if (reload) {
        this.getList()
      }
      this.dialogAddVisible = false
      // this.dialogDetailVisible = false;
      this.dialogSetVisible = false
      this.dialogSetPageVisible = false
      this.dialogLockVisible = false
      this.videoCameraVisible = false
      this.playVideoVisible = false
    },
    handleCreate() {
      this.title = '批量指令下发'
      this.isPublic = 1
      this.dialogAddVisible = true
    },
    sbxqhandCancle() {
      this.dialogWidth = '1000px'
      this.sbxqdialogFormVisible = false
    },

    sbxqShow(val) {
      if (this.timer !== null) {
        clearInterval(this.timer)
        this.timer = null
      }
      this.deviceId = val
      getDeviceAttribute({
        deviceId: val
      }).then((response) => {
        const data = response.data.data
        data.deviceData.sort((a, b) => a.index - b.index)
        this.deviceDetailModel = data
        this.sbxqdialogFormVisible = true
      })
    },
    // handleDetail(val) {
    //   this.title = "照明设备";
    //   this.Id = val;
    //   this.dialogDetailVisible = true;
    // },
    operationCommand(item, row) {
      const val = row.deviceId
      console.log({ ...item })
      if (item.cmdCode === '1023') {
        this.handleExecutionContract(val, item)
      } else if (item.cmdCode === '10235' || item.cmdCode === '10236' || item.cmdCode === '102378') {
        this.handleConfigurationData(row, item)
      } else if (item.cmdCode === '4001') { // 交管箱开锁
        this.handleOpenLock(row, item)
      } else if (item.cmdCode > 102380 && item.cmdCode < 102388) { // 智能井盖阈值
        this.$refs.setCoversConfig.open(item, row)
      } else if (item.cmdCode === '102591') { // 红绿灯信号感知终端使/失能
        this.$refs.setCoversConfig.open(item, row)
      } else {
        let msg = ''
        let api = fetchCreate
        const data = {
          commandTypeVo: item,
          deviceIds: [val]
        }
        if (item.cmdCode === '100061' || item.cmdCode === '100062' || item.cmdCode === '100063') {
          msg = item.cmdName + '成功！可于详情的' + item.cmdName + '查看详细信息'
        } else if (item.cmdCode === '100072' || item.cmdCode === '100073') {
          msg = item.cmdName + '成功！可于操作-查询日志中查看详细信息'
          if (item.cmdCode === '100072') {
            api = getLockCurrent
            data.lockCmds = [{
              lchnNo: 0
            }]
          } else {
            api = getLockGeneral
          }
        } else if (item.cmdCode === '100071') {
          this.$refs.alarmConfig.open({ deviceId: val })
          return
        } else {
          msg = item.cmdName + '成功！'
        }
        if (item.cmdCode === '4002') {
          api = rebootDevice
        }
        this.$confirm('是否' + item.cmdName + '?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            api(data).then((res) => {
              if (res.data.code === 200) {
                this.$message({
                  type: 'success',
                  message: msg
                })
                this.getList()
              }
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '取消' + item.cmdName
            })
          })
      }
      //                 else if (item.cmdCode === "100061" || item.cmdCode === "100062" || item.cmdCode === "100063") {
      //   console.log(item.cmdCode, item)
      // }
    },
    handOpenLock() {
      this.$refs.channelForm.validate(val => {
        if (val) {
          console.log(this.channelForm, this.commandTypeVo, this.rowValue)
          const data = {
            commandTypeVo: this.commandTypeVo,
            deviceIds: [this.rowValue.deviceId],
            lockCmds: this.channelForm.value.map(ele => {
              return {
                lchnNo: ele,
                op: 1
              }
            })
          }
          this.lockLoading = true
          openLock(data).then(res => {
            this.$message({
              type: 'success',
              message: '开锁成功'
            })
            this.lockLoading = false
            this.dialogLockVisible = false
          }).catch(() => {
            this.lockLoading = false
          })
        }
      })
    },
    handleExecutionContract(val, item) {
      this.Id = val
      console.log('item', item)
      this.commandTypeVo = item
      // if (val === 0) {
      this.title = '参数配置'
      this.dialogSetVisible = true
      // } else {
      //   this.title = "XXXXXXX参数配置";
      //   this.dialogSetPageVisible = true;
      // }
    },
    /**
             * 毫米波、激光雷达参数配置
             * @param rowDeviceId: 被点击的行ID
             * @param item 被点击的按钮数据
             */
    handleConfigurationData(row, item) {
      this.rowValue = row
      this.commandTypeVo = item
      this.title = row.deviceName + '参数配置'
      this.dialogSetPageVisible = true
    },
    /** 交管箱开锁 */
    handleOpenLock(row, item) {
      this.rowValue = row
      this.commandTypeVo = item
      this.title = row.deviceName + '智能开锁'
      this.dialogLockVisible = true
    },
    /**
             * 树形菜单点击事件
             * @param nodeTree: 被点击的属性菜单
             */
    handleNodeClick(nodeTree) {
      this.listQuery.areaCode = nodeTree.id
      this.listQuery.limit = 10
      this.listQuery.page = 1
      this.getList()
    },
    /**
             * 摄像机通道按钮点击事件
             * @param row: 被点击行的数据  @type Object
             *
             */
    videoCameraPassageway(row) {
      this.rowValue = row
      this.title = row.deviceName + '通道信息'
      this.videoCameraWidth = '75vw'
      this.videoCameraVisible = true
    },

    showPlayVideoDialog(row) {
      this.rowValue = row
      this.playVideoVisible = true
    },
    tabChange(val) {
      this.dialogWidth = ~~val === 4 ? '95%' : '1000px'
    }
  }
}
</script>
<style scoped>
.setting-btn {
  margin-left: 10px;
  height: 36px;
  width: 100px;
  font-size: 14px;
  background: #409EFF;
  border-radius: 4px;
  display: inline-block;
  vertical-align: top;
  color: #fff;
  line-height: 36px;
}

.set-time {
  width: 33%;
  text-align: center;
  float: left;
  border-right: 1px solid #fff;
}

.time {
  display: inline-block;
  width: 66%;
  text-align: center;
  cursor: pointer;
}

.btnTop+.btnTop {
  margin-top: 5px !important;
}

.videoCameraDetail .el-dialog__body {
  padding: 20px;
}

.el-dialog__body {
  padding: 20px;
}

.meter-item {
  display: flex;
  align-items: center;
}

.meter-item el-input {
  margin-right: 10px;
  /* 调整按钮和输入框之间的间距 */

}

.number1 {
  margin-right: 10px;
  margin-left: 65px;
}

.container {
  display: flex;
  align-items: center;
  margin-top: 1.39vh;
}

.number2 {
  margin-right: 20px;
  margin-left: 72px;

}

.p1 {
  margin-left: 45px;
  margin-top: 30px;
}
</style>
