import request from '@/utils/request'

// 设备查询列表
export function fetchList(data) {
  return request({
    url: '/szdlService/api/deviceManage/list',
    method: 'post',
    data
  })
}
/** 设备指令 */
// 通用
export function fetchCreate(data) {
  return request({
    url: '/szdlService/api/deviceManage/issuedInstruction',
    method: 'post',
    data
  })
}
// 重启
export function rebootDevice(data) {
  return request({
    url: '/szdlService/api/deviceManage/LockReboot',
    method: 'post',
    data
  })
}
// 开锁
export function openLock(data) {
  return request({
    url: '/szdlService/api/deviceManage/OpenLock',
    method: 'post',
    data
  })
}

// 参数配置
export function fetchUpdate(data) {
  return request({
    url: '/szdlService/api/deviceManage/configurationInfo',
    method: 'post',
    data
  })
}
// 通道号
export function fetchChannel(data) {
  return request({
    url: '/szdlService/api/publicUse/chnnoList',
    method: 'post',
    data
  })
}
// 指令下拉
export function fetchCmdUpList(data) {
  return request({
    url: '/szdlService/api/deviceManage/cmdUpList',
    method: 'post',
    data
  })
}

// 区域树
export function areaCodeTree() {
  return request({
    url: '/szdlService/api/deviceManage/areaCodeTree',
    method: 'post'
  })
}

// 获取激光雷达参数配置
export function laserRadarInfo(data) {
  return request({
    url: '/szdlService/api/deviceManage/laserRadarInfo',
    method: 'post',
    data
  })
}

// 保存激光雷达参数配置
export function issuedInstruction(data) {
  return request({
    url: '/szdlService/api/deviceManage/issuedInstruction',
    method: 'post',
    data
  })
}

// 获取毫米波雷达参数配置
export function waveRadarInfo(data) {
  return request({
    url: '/szdlService/api/deviceManage/waveRadarInfo',
    method: 'post',
    data
  })
}

// 获取RSU参数配置
export function rsuConfigInfo(data) {
  return request({
    url: '/szdlService/api/deviceManage/rsuConfigInfo',
    method: 'post',
    data
  })
}

// 查询视频设备通道号
export function findVideoChno(data) {
  return request({
    url: '/devService/api/dev/findVideoChno',
    method: 'post',
    data
  })
}

// 摄像机 点播下行指令
export function playIn(data) {
  return request({
    url: '/services/camera/cmd/playIn',
    method: 'post',
    data
  })
}

// 获取摄像头的属性点
export function queryDeviceAllAttr(data) {
  return request({
    url: '/devService/api/deviceAttribute/queryDeviceAllAttr',
    method: 'post',
    data
  })
}

// 获取摄像头的属性点
export function queryCameraMsg(data) {
  return request({
    url: '/devService/api/deviceAttribute/queryCameraMsg',
    method: 'post',
    data
  })
}
// 获取历史在线率
export function getOnlinPercentList(data) {
  return request({
    url: '/spotService/api/basisSpot/getOnlineRateHistory',
    method: 'post',
    data
  })
}

// 获取箱体智控器基础属性
export function getLockGeneral(data) {
  return request({
    url: '/szdlService/api/deviceManage/lockGeneral',
    method: 'post',
    data
  })
}
// 获取箱体智控器实时属性
export function getLockCurrent(data) {
  return request({
    url: '/szdlService/api/deviceManage/lockCurrent',
    method: 'post',
    data
  })
}
// 获取箱体智控器告警配置
export function getLockAlarmConfig(params) {
  return request({
    url: '/szdlService/api/deviceManage/lockAlarmConfig',
    method: 'post',
    params
  })
}
// 获取智能井盖阈值配置
export function getSmaConfig(data) {
  return request({
    url: '/szdlService/api/deviceManage/querySmaConfig',
    method: 'post',
    data
  })
}
// 获取智能井盖阈值配置
export function setSmaConfig(data, url) {
  return request({
    url: '/szdlService/api/deviceManage/' + url,
    method: 'post',
    data
  })
}
// 获取智能井盖阈值配置
export function setTscEnable(data) {
  return request({
    url: '/szdlService/api/deviceManage/tscEnable',
    method: 'post',
    data
  })
}
