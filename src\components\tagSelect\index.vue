<template>
  <div>
    <!--     <el-select
      placeholder="标签"
      :value="value"
      clearable
      @change="handleInput"
    >
      <el-option v-for="item in tagOptions" :key="item.tagId" :value="item.tagId" :label="item.tagName"></el-option>
    </el-select> -->
  </div>
</template>

<script>
import { deviceTagList } from '@/api/otherSystem/equipmentManagement/tag'
export default {
  props: {
    value: {
      type: Number,
      default: undefined
    }
  },
  data() {
    return {
      tagId: undefined,
      tagOptions: []
    }
  },
  created() {
    this.getTagOptions()
  },
  methods: {
    handleInput(v) {
      this.$emit('input', v)
    },
    getTagOptions() {
      deviceTagList({ type: 0 }).then(res => {
        if (res.data.code === 200) {
          this.tagOptions = res.data.data
        }
      })
    },
  }
}
</script>
