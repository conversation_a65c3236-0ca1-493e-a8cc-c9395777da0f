import request from '@/utils/request'

// 获取楼宇层级树(areagroupId)
export function fetchTreeList(data) {
  return request({
    url: '/pmsService/api/pmsTree/selectPmsTree',
    method: 'post',
    data
  })
}

// 获取楼宇层级树(areaId)
export function fetchTree(data) {
  return request({
    url: '/pmsService/api/pmsTree/selectPmsTreeWithArea',
    method: 'post',
    data
  })
}

export function selectPmsTreeNumber(data) {
  return request({
    url: '/pmsService/api/pmsTree/selectPmsTreeNumber',
    method: 'post',
    data
  })
}

export function selectManagerArea() {
  return request({
    url: '/pmsService/api/area/selectManagerArea',
    method: 'post'
  })
}

export function selectAreaClassify(data) {
  return request({
    url: '/pmsService/api/area/selectAreaClassify',
    method: 'post',
    data
  })
}

export function publicDicTreeList(data) {
  return request({
    url: '/pmsService/api/publicDic/treeList',
    method: 'post',
    data
  })
}
