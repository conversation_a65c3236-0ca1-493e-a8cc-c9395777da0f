export function formatDate(date, fmt) {
  if (typeof date === 'string') date = new Date(date)
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  const o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds()
  }
  if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length))
  for (var k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
    }
  }
  // for (var k in o) {
  //   if (new RegExp(`(${k})`).test(fmt)) {
  //     var str = o[k] + ''
  //     fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? str : padLeftZero(str))
  //   }
  // }
  return fmt
}

// function padLeftZero(str) {
//   return ('00' + str).substr(str.length)
// }

export function getDateBeforeOrAfter(num, type, timeType) { // type: 0后几天，1，前几天. timeType: 1：年，2：月，3：日
  var date1 = new Date()
  var date2 = new Date(date1)
  var month = ''
  var monthstr = ''
  var time2 = ''
  if (timeType === 3) { // 获取前几天或后几天日期
    if (type === 0) {
      date2.setDate(date1.getDate() + num)
    } else {
      date2.setDate(date1.getDate() - num)
    }
    month = date2.getMonth() + 1
    monthstr = month.toString()
    if (month < 10) {
      monthstr = '0' + monthstr
    }
    time2 = date2.getFullYear() + '-' + (monthstr) + '-' + date2.getDate()
    return time2
  }
  if (timeType === 2) { // 获取前几月后几月日期
    if (type === 0) {
      date2.setMonth(date1.getMonth() + num)
    } else {
      date2.setMonth(date1.getMonth() - num)
    }
    month = date2.getMonth() + 1
    monthstr = month.toString()
    if (month < 10) {
      monthstr = '0' + monthstr
    }
    time2 = date2.getFullYear() + '-' + (monthstr)
    return time2
  }
  if (timeType === 1) { // 获取前几年后几年日期
    if (type === 0) {
      time2 = date2.getFullYear() + num
    } else {
      time2 = date2.getFullYear() - num
    }
    return time2.toString()
  }
}
// 计算天数差的函数，通用
export function DateDiffDay(sDate1, sDate2) { // sDate1和sDate2是2017-9-25格式
  var aDate, oDate1, oDate2, iDays
  aDate = sDate1.split('-')
  oDate1 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0]) // 转换为9-25-2017格式
  aDate = sDate2.split('-')
  oDate2 = new Date(aDate[1] + '-' + aDate[2] + '-' + aDate[0])
  iDays = parseInt(Math.abs(oDate1 - oDate2) / 1000 / 60 / 60 / 24) // 把相差的毫秒数转换为天数
  return iDays
}

// 计算日期之间的月数
export function DateDiffMonth(date1, date2) {
  // 拆分年月日
  date1 = date1.split('-')
  // 得到月数
  date1 = parseInt(date1[0]) * 12 + parseInt(date1[1])
  // 拆分年月日
  date2 = date2.split('-')
  // 得到月数
  date2 = parseInt(date2[0]) * 12 + parseInt(date2[1])
  var m = Math.abs(date1 - date2)
  return m
}

// 获取当月第一天
export function getCurrentMonthFirst() {
  var date = new Date()
  date.setDate(1)
  var month = parseInt(date.getMonth() + 1)
  var day = date.getDate()
  if (month < 10) {
    month = '0' + month
  }
  if (day < 10) {
    day = '0' + day
  }
  return date.getFullYear() + '-' + month + '-' + day // 201-01-01
}
// 获取当月最后一天
export function getCurrentMonthLast() {
  var date = new Date()
  var currentMonth = date.getMonth()
  var nextMonth = ++currentMonth
  var nextMonthFirstDay = new Date(date.getFullYear(), nextMonth, 1)
  var oneDay = 1000 * 60 * 60 * 24
  var lastTime = new Date(nextMonthFirstDay - oneDay)
  var month = parseInt(lastTime.getMonth() + 1)
  var day = lastTime.getDate()
  if (month < 10) {
    month = '0' + month
  }
  if (day < 10) {
    day = '0' + day
  }
  return date.getFullYear() + '-' + month + '-' + day // 2019-01-31
}

// 计算两个日期之间的年数
export function DateDiffYear(startDateStr, endDateStr) {
  var sDate = new Date(Date.parse(startDateStr.replace(/-/g, '/')))
  var eDate = new Date(Date.parse(endDateStr.replace(/-/g, '/')))
  var sY = sDate.getFullYear()
  var eY = eDate.getFullYear()
  // var day = 24 * 60 * 60 * 1000
  return eY - sY
  // // 得到前一天(算头不算尾)
  // sDate = new Date(sDate.getTime() - day)

  // // 获得各自的年、月、日
  // var sY = sDate.getFullYear()
  // var sM = sDate.getMonth() + 1
  // var sD = sDate.getDate()
  // var eY = eDate.getFullYear()
  // var eM = eDate.getMonth() + 1
  // var eD = eDate.getDate()
  // console.log(eY)
  // console.log(sY)
  // if (eY > sY && sM === eM && sD === eD) {
  //   return eY - sY
  // } else {
  //   return eY - sY
  // }
}

// 获取当前时间时分秒
export function nowTime() {
  var currentTime = ''
  var time = new Date()
  var hour = time.getHours()
  var minute = time.getMinutes()
  var second = time.getSeconds()
  hour = checkTimeNum(hour)
  minute = checkTimeNum(minute)
  second = checkTimeNum(second)
  currentTime = hour + ':' + minute + ':' + second
  return currentTime
}
function checkTimeNum(timeNum) {
  var num = (timeNum < 10) ? ('0' + timeNum) : timeNum
  return num
}
