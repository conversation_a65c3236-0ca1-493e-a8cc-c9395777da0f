<template>
  <div>
    <div class="app-container" style="padding-top:0">
      <div class="border" style="border-bottom:none;border-right:none;">
        <div class="box-header" :style="{ width: getWidth, 'margin-top': '-1px' }">
          <div class="item item2" style="color: white;">资源类型
            <i class="el-icon-circle-plus-outline" style="transform:scale(1.8);cursor:pointer;margin-left: 1rem"
              v-if="checkButtonPermission('322311')"
              @click="addType()" />
          </div>
          <div class="item item3" style="color: white;">
            <span>资源类型属性<i class="el-icon-circle-plus-outline"
                style="transform:scale(1.8);cursor:pointer;margin-left: 1rem" 
                v-if="checkButtonPermission('322312')"
                @click="editTypeAttr()" /></span>
            <el-input v-model="search" size="medium" placeholder="请输入资源类型名称" suffix-icon="el-icon-search"
              style="width:30%;color:white" @keyup.enter.native="queryData" />
          </div>
        </div>
        <div class="box-content">
          <div v-for="(item, index) in resourceArr" :key="index" class="box-content-item">
            <div class="item item2">
              <div class="first" style="height: '9.26vh'">
                <!-- <el-scrollbar :ref="`item2-scroll-${index}`" style="height:100%" class="scroll-div-show"> -->
                <div class="list md-oneRow" :class="{ active: item.typeCode === selectType.typeCode }"
                  @contextmenu.prevent="openMenu(item, $event)" @click="typeCodeSelect(item)">
                  <el-tooltip effect="light" :content="item.cname" placement="top">
                    <span>{{ item.cname }}</span>
                  </el-tooltip>
                </div>
                <!-- </el-scrollbar> -->
              </div>
            </div>
            <div class="item item3" style="width:calc(100% - 12vh);top: '0.93vh';height: '100%'">
              <!-- <el-scrollbar :ref="`item3-scroll-${index}`" class="scroll-div scroll-div-show" style="height:100%"> -->
              <div class="attr" :style="{ height: '100%' }">
                <div v-for="(attr, aInx) in item.newAttrList" :key="aInx" class="attr-item">
                  <el-tooltip :disabled="attr.cname.length < 7" effect="light" :content="attr.cname" placement="top">
                    <span class="attr-list md-oneRow" style="margin: 2px 0 0 1.39vh">{{ attr.cname + (attr.unit ?
                      '(' + attr.unit + ')' : '') }}</span>
                  </el-tooltip>
                </div>
              </div>
              <!-- </el-scrollbar> -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-drawer ref="drawer" title="新增资源类型" :with-header="false" :visible.sync="addResourceDialog" direction="rtl"
      :before-close="beforeClose" :modal-append-to-body="false" append-to-body>
      <div class="demo-drawer__content">
        <span style="display: flex; justify-content: center; margin: 1.85vh;color:white">新增资源类型</span>
        <el-form ref="addResourceFormRef" :rules="rules" :model="addResourceForm" label-width="9.26vh">
          <el-form-item label="名称" prop="cname" placeholder="请输入名称">
            <el-input v-model="addResourceForm.cname" />
          </el-form-item>
          <el-form-item label="英文名" prop="ename" placeholder="请输入英文名"
            :rules="validate.form({ isNull: false, min: 3, max: 3 })">
            <el-input v-model="addResourceForm.ename" />
          </el-form-item>
          <el-form-item label="是否可监控" prop="isSource">
            <el-select v-model="addResourceForm.isSource" placeholder="请选择是否可监控">
              <el-option label="是" value="1" />
              <el-option label="否" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="重要程度" prop="grade">
            <el-select v-model="addResourceForm.grade" placeholder="请选择重要程度">
              <el-option label="很重要" value="1" />
              <el-option label="重要" value="2" />
              <el-option label="一般" value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="大屏图标" prop="onlineFileId">
            <uploadItem v-model="addResourceForm.onlineFileId" image-type="0" style="marginRight: 10px" />
            <uploadItem v-model="addResourceForm.offlineFileId" image-type="1" style="marginRight: 10px" />
            <uploadItem v-model="addResourceForm.alertFileId" image-type="2" />
          </el-form-item>
          <el-form-item label="拓扑图标" prop="topologyFileId">
            <uploadItem v-model="addResourceForm.topologyFileId" image-type="4" />
          </el-form-item>
        </el-form>
        <div style="text-align: center;">
          <el-button @click="cancelResourceForm">取 消</el-button>
          <el-button :loading="loading" type="primary" @click="handleSaveResource">{{
            loading ? '提交中 ...' : '确 定'
          }}</el-button>
        </div>
      </div>
    </el-drawer>
    <el-drawer v-if="editResourceDialog" title="编辑资源类型" :with-header="false" :visible.sync="editResourceDialog"
      direction="rtl" :modal-append-to-body="false" append-to-body>
      <div class="demo-drawer__content">
        <span style="display: flex; justify-content: center; margin: 1.85vh;">编辑资源类型</span>
        <el-form ref="editResourceFormRef" :rules="updateRules" :model="editResourceForm" label-width="9.26vh">
          <el-form-item label="名称" prop="cname" :rules="validate.form({ isNull: false })">
            <el-input v-model="editResourceForm.cname" />
          </el-form-item>
          <el-form-item label="英文名" prop="ename" :rules="validate.form({ isNull: false, min: 3, max: 3 })">
            <el-input v-model="editResourceForm.ename" />
          </el-form-item>
          <el-form-item label="是否可监控" prop="isSource" :rules="validate.form({ isNull: false })">
            <el-select v-model="editResourceForm.isSource" placeholder="请选择" disabled>
              <el-option label="是" value="1" />
              <el-option label="否" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="重要程度" prop="grade" :rules="validate.form({ isNull: false })">
            <el-select v-model="editResourceForm.grade" placeholder="请选择">
              <el-option label="很重要" value="1" />
              <el-option label="重要" value="2" />
              <el-option label="一般" value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="大屏图标" prop="onlineFileId">
            <uploadItem v-model="editResourceForm.onlineFileId" image-type="0" :file="selectType.onlineFileName"
              style="marginRight: 10px" />
            <uploadItem v-model="editResourceForm.offlineFileId" image-type="1" :file="selectType.offlineFileName"
              style="marginRight: 10px" />
            <uploadItem v-model="editResourceForm.alertFileId" image-type="2" :file="selectType.alertFileName" />
          </el-form-item>
          <el-form-item label="拓扑图标" prop="topologyFileId">
            <uploadItem v-model="editResourceForm.topologyFileId" image-type="4" :file="selectType.topologyFileName" />
          </el-form-item>
        </el-form>
        <div style="text-align: center;">
          <el-button @click="cancelEditResourceForm">取 消</el-button>
          <el-button :loading="loading" type="primary" @click="handleEditResource">{{
            loading ? '提交中 ...' : '确 定'
          }}</el-button>
        </div>
      </div>
    </el-drawer>
    <ul v-show="menuShow" class="contextmenu" :style="{ left: menuLeft + 'px', top: menuTop + 'px' }">
      <li v-if="checkButtonPermission('322313')" @click="editType()">编辑</li>
      <li v-if="checkButtonPermission('322314')" @click="delType()">删除</li>
    </ul>
  </div>
</template>

<script>
import { queryAllCiTypeResource, saveResource } from '@/api/otherResource/cmdb/confCiType'
import { mapGetters } from 'vuex'
import uploadItem from './upload.vue'
import permission from '@/views/otherResource/soc/mixins/permission'

export default {
  components: { uploadItem },
  mixins: [permission],
  data() {
    const validateStateImg = (rule, value, callback) => {
      console.log(rule)
      if (this.addResourceForm.offlineFileId && this.addResourceForm.onlineFileId && this.addResourceForm.alertFileId) {
        callback()
      } else {
        callback(new Error('大屏图标均不能为空'))
      }
    }
    const validateUpdateStateImg = (rule, value, callback) => {
      console.log(rule)
      if (this.editResourceForm.offlineFileId && this.editResourceForm.onlineFileId && this.editResourceForm.alertFileId) {
        callback()
      } else {
        callback(new Error('大屏图标均不能为空'))
      }
    }

    return {
      menuTop: 0,
      menuLeft: 0,
      selectType: {},
      menuShow: false,
      loading: false,
      addResourceForm: {
        cname: '',
        ename: '',
        isSource: '1',
        grade: '3',
        alertFileId: null,
        offlineFileId: null,
        onlineFileId: null,
        topologyFileId: null
      },
      addResourceDialog: false,
      editResourceForm: {},
      editResourceDialog: false,
      search: '',
      resourceArr: [],
      rules: {
        cname: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
        isSource: [{ required: true, message: '是否可控不能为空', trigger: 'change' }],
        grade: [{ required: true, message: '重要程度不能为空', trigger: 'change' }],
        onlineFileId: [{ required: true, validator: validateStateImg, trigger: 'blur' }],
        topologyFileId: [{ required: true, message: '拓扑图标不能为空', trigger: 'change' }]
      },
      updateRules: {
        cname: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
        isSource: [{ required: true, message: '是否可控不能为空', trigger: 'change' }],
        grade: [{ required: true, message: '重要程度不能为空', trigger: 'change' }],
        onlineFileId: [{ required: true, validator: validateUpdateStateImg, trigger: 'blur' }],
        topologyFileId: [{ required: true, message: '拓扑图标不能为空', trigger: 'change' }]
      }
    }
  },
  computed: {
    ...mapGetters(['permissions']),
    getWidth() {
      return `148.15vh`
    }
  },
  watch: {
    menuShow(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  created() {
    this.queryData()
  },
  mounted() {
    this.$nextTick(() => {
      const searchBtn = document.querySelectorAll('.box-header .el-input__suffix')
      searchBtn[0].addEventListener('click', () => {
        this.queryData()
      })
    })
  },
  methods: {
    editType() {
      if (!this.selectType) return
      this.getData({
        url: '/cmdbService/confCiType/' + this.selectType.id,
        success: (r) => {
          const type = r.data.data
          this.editResourceForm = {
            ...type,
            isSource: type.isSource + '',
            grade: type.grade + ''
          }
          this.editResourceDialog = true
        }
      })
    },
    delType() {
      if (this.selectType.isSystem === '1') this.$message.warning('系统类型无法删除')
      this.delData({
        url: '/cmdbService/confCiType/' + this.selectType.id,
        befoureNotifyText: '删除[' + this.selectType.cname + ']类型',
        success: () => this.queryData()
      })
    },
    cancelEditResourceForm() {
      this.editResourceDialog = false
    },
    handleEditResource() {
      this.$refs['editResourceFormRef'].validate(valid => {
        if (valid) {
          this.putData({
            url: '/cmdbService/confCiType',
            data: this.editResourceForm,
            success: () => {
              this.queryData()
              this.editResourceDialog = false
            }
          })
        }
      })
    },
    openMenu(tag, e) {
      this.selectType = tag
      this.menuShow = true
      this.menuLeft = e.clientX - 277
      this.menuTop = e.clientY - 80
      console.log(this.selectType)
    },
    closeMenu() {
      this.menuShow = false
      // this.selectType = {}
    },
    handleSaveResource() {
      this.$refs['addResourceFormRef'].validate(valid => {
        if (!valid) return false
        saveResource(this.addResourceForm).then(res => {
          this.$message.success('新增成功!')
          this.$refs['addResourceFormRef'].resetFields()
          this.queryData()
          this.addResourceDialog = false
        })
      })
    },
    beforeClose() {
      this.$confirm('是否退出新增资源类型？')
        .then(() => {
          this.addResourceDialog = false
          this.$refs['addResourceFormRef'].resetFields()
        })
        .catch(() => { })
    },
    cancelResourceForm() {
      this.beforeClose()
    },
    queryData() {
      const params = { search: this.search }
      queryAllCiTypeResource(params).then(res => {
        this.resourceArr = res.data.data
      })
    },
    addType() {
      this.addResourceDialog = true
    },
    editTypeAttr() {
      console.log(this.selectType)
      if (!this.selectType.id) {
        this.$message.info('请先选择添加资源的父类')
        return
      }
      this.$router.push({
        path: '/resource/attribute/index',
        query: { typeCode: this.selectType.id, name: this.selectType.cname }
      })
    },
    typeCodeSelect(type) {
      this.selectType = type
    }
  }
}
</script>

<style lang="scss" scoped>
.contextmenu {
  margin: 0;
  background: #fff;
  z-index: 2;
  position: absolute;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);

  li {
    margin: 0;
    padding: 7px 16px;
    cursor: pointer;

    &:hover {
      background: #eee;
    }
  }
}

.el-row {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.el-col {
  border-radius: 4px;
}

::v-deep {
  .el-input__suffix {
    cursor: pointer;
  }

  .el-scrollbar__bar {
    &.is-horizontal {
      display: none;
    }
  }

  .el-scrollbar__wrap {
    overflow-x: hidden;
  }

  .scroll-div-show {
    .el-scrollbar__wrap {
      overflow: hidden;
    }

    .is-vertical {
      display: none;
    }
  }

  .item2 {
    .el-scrollbar__wrap {
      margin-bottom: 0 !important;
    }
  }
}

.app-container {
  .box-header {
    height: 45px;
    line-height: 45px;
    // border-bottom: 1px solid rgba(0, 0, 0, 0.26);
    position: fixed;
    background-color: rgba(12, 52, 117, 1);
    z-index: 2001;

    .item3 {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .box-content {
    height: calc(100% - 45px);
    padding-right: 0 !important;
    padding-top: 45px;

    .box-content-item {

      // border-bottom: 1px solid rgba(0, 0, 0, 0.26);
      .moreImg {
        width: 12px;
        height: 12px;
        position: absolute;
        left: 50%;
        bottom: 0px;
        transform: translateX(-50%);
        cursor: pointer;
        z-index: 1999;

        img {
          width: 12px;
          height: 12px;
          margin-top: 4px;
          cursor: pointer;
        }
      }

      .item1,
      .item2 {
        display: flex;
        align-items: center;
        justify-content: center;
        border-top: 1px solid rgba(0, 0, 0, 0.26) !important;
      }

      .item3 {
        min-height: 90px;
        border-top: 1px solid rgba(0, 0, 0, 0.26) !important;
        border-left: 1px solid rgba(0, 0, 0, 0.26) !important;
      }

      .item1 {
        padding: 0 24px;
        word-break: break-all;
        color: #409eff;
        font-weight: 600;
        cursor: pointer;
      }

      .item2 {
        padding: 12px 0 0 0;
        position: relative;

        .first {
          height: 100%;
          overflow: hidden;
          width: 90px;

          &.active {
            height: calc(100% - 20px) !important;
            position: absolute;
            width: 90px;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            z-index: 2000;
            background-color: #fff;
            border: 1px solid #dcdcdc;
            border-radius: 4px;
          }

          .list {
            width: 90px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            color: #fff;
            background-color: #00ad72;
            border-color: #b3d8ff;
            border-radius: 4px;
            font-size: 13px;
            margin-bottom: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;
            padding: 0 5px;

            &:last-of-type {
              margin-bottom: 0;
            }

            &:last-child {
              margin-bottom: 0;
            }

            &.active {
              color: #fff;
              background-color: #ed7b2f;
            }
          }
        }
      }

      &:last-child {
        .item3 {
          &.active {
            bottom: 0;
          }
        }
      }

      .item3 {
        position: relative;

        &.active {
          position: absolute;
          left: 205px;
          padding: 10px 15px;
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
          height: 360px;
          z-index: 2000;
          background-color: #fff;
        }

        .attr {
          height: 100%;

          .attr-item {
            margin-bottom: 10px;
            overflow: hidden;
            height: 30px;
            display: inline;

            &:last-child {
              margin-bottom: 0;
            }

            .el-button--text {
              font-size: 13px;
              padding: 8px !important;
              border: none;

              &:hover {
                border: none;
              }
            }

            .attr-list {
              width: 100px;
              height: 30px;
              line-height: 30px;
              text-align: center;
              color: rgba(0, 0, 0, 0.6);
              background-color: #fff;
              border: 1px solid #c7c7c7;
              border-radius: 4px;
              font-size: 13px;
              display: block;
              float: left;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              padding: 0 5px;

              &.list {
                background-color: #fbdfc9;
                color: #f7a961;
                cursor: pointer;
              }

              &.active {
                color: #fff;
                background-color: #f68800cc;
              }
            }
          }
        }
      }
    }
  }

  .item {
    float: left;
    height: 100%;
  }

  .item1,
  .item2 {
    width: 60px;
    text-align: center;
    font-size: 15px;
  }

  .item2 {
    width: 130px;

  }

  .item3 {
    width: calc(100% - 130px);
    padding: 10px 15px;
    font-size: 15px;
  }

  .md-item-bottom {
    width: 100%;
    height: 15px;
    line-height: 15px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
