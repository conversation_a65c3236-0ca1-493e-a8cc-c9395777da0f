<template>
  <el-form label-position="top" label-width="7.41vh">
    <el-form-item label="标题">
      <el-input v-model="data.title" size="small" />
    </el-form-item>
    <el-form-item label="宽度(col布局规则，24为100%)">
      <el-input-number v-model="data.options.width" :max="24" :min="0" size="small" />
    </el-form-item>
    <el-form-item label="数据绑定key">
      <el-input v-model="data.key" size="small" :disabled="true" />
    </el-form-item>

    <el-form-item label="选项">
      <el-radio-group v-model="data.options.defaultValue">
        <div v-for="(item, i) in data.options.option" :key="i" style="clearfix">
          <el-radio :label="item.value" style="float: left;">
            <el-input v-model="item.label" size="small" style="width:7.4080vh;" title="删除选项" />
            <el-input v-model="item.value" size="small" style="width:7.4080vh;" title="删除选项" />
            <i class="el-icon-circle-close" style="color: red;" @click="subOption(i)" />
          </el-radio>
        </div>
      </el-radio-group>

      <div>
        <i class="el-icon-circle-plus" style="color: #17B3A3;" title="增加选项" @click="addOption" />
      </div>
    </el-form-item>
    <el-form-item label="是否必填">
      <el-switch v-model="data.options.required" active-color="#13ce66" inactive-color="#EEEEEE" />
    </el-form-item>
    <el-form-item label="是否禁用">
      <el-switch v-model="data.options.disabled" active-color="#13ce66" inactive-color="#EEEEEE" />
    </el-form-item>
    <el-form-item label="验证提示内容">
      <el-input v-model="data.options.valid_str" size="small" />
    </el-form-item>
  </el-form>
</template>

<script>
import common from '@/utils/common'
import bus from '@/utils/bus'

export default {
  props: {
    propData: {
      type: Object,
      default: function() {
        return {
          title: '单选框组',
          type: 'radio',
          icon: '/static/img/form-design/radio.png',
          options: {
            width: 24,
            required: false,
            disabled: false,
            valid_str: '验证失败！',
            defaultValue: '值1',
            option: [{
              value: '值1',
              label: '选项1'
            }, {
              value: '值2',
              label: '选项2'
            }, {
              value: '值3',
              label: '选项3'
            }]
          }
        }
      }
    }
  },
  data() {
    return {
      data: {
        title: '单选框组',
        type: 'radio',
        icon: '/static/img/form-design/radio.png',
        options: {
          width: 24,
          required: false,
          disabled: false,
          defaultValue: '值1',
          valid_str: '验证失败！',
          option: [{
            value: '值1',
            label: '选项1'
          }, {
            value: '值2',
            label: '选项2'
          }, {
            value: '值3',
            label: '选项3'
          }]
        }
      }
    }
  },
  watch: {
    data: {
      handler: function(value, oldValue) {
        const newFormList = common.deepClone(
          this.$store.state.formDesign.formList
        )
        // eslint-disable-next-line no-unused-vars
        let activeIndex
        for (let i = 0; i < this.$store.state.formDesign.formList.length; i++) {
          const element = newFormList[i]
          if (element.type !== 'grid') {
            if (element.key === this.$store.state.formDesign.activeKey) {
              activeIndex = i

              newFormList[i] = value
              this.$store.commit('formDesign/updateActiveKey', element.key)
              this.$store.dispatch('formDesign/setFormList', common.deepClone(newFormList))
              bus.$emit('formDesign.syncList', common.deepClone(newFormList))
              break
            }
          } else {
            for (let j = 0; j < element.cols.length; j++) {
              const element2 = element.cols[j]
              for (let k = 0; k < element2.list.length; k++) {
                const element3 = element2.list[k]
                if (element3.key === this.$store.state.formDesign.activeKey) {
                  activeIndex = i

                  newFormList[i].cols[j].list[k] = value
                  this.$store.commit('formDesign/updateActiveKey', element3.key)
                  this.$store.dispatch('formDesign/setFormList', common.deepClone(newFormList))
                  bus.$emit('formDesign.syncList', common.deepClone(newFormList))
                  break
                }
              }
            }
          }
        }
      },
      deep: true
    },
    propData: {
      handler: function(value) {
        this.data = common.deepClone(value)
      },
      deep: true
      // immediate: true
    }
  },
  mounted() {
    this.data = common.deepClone(this.propData)
    this.data.options.width = 24
  },
  methods: {
    addOption() { // 新增选项
      this.data.options.option.push({
        value: `值${this.data.options.option.length + 1}`,
        label: `选项${this.data.options.option.length + 1}`
      })
    },
    subOption(index) { // 删除选项
      this.data.options.option.splice(index, 1)
    }
  }
}
</script>

<style>
</style>
