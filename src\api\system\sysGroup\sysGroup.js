import request from '@/utils/request'
export function userGroupTree(data) {
  return request({
    url: '/zhdxService/api/sysGroup/userGroupTree',
    method: 'post',
    data
  })
}

export function groupList(data) {
  return request({
    url: '/zhdxService/api/sysGroup/list',
    method: 'post',
    data
  })
}

export function groupGet(data) {
  return request({
    url: '/zhdxService/api/sysGroup/get',
    method: 'post',
    data
  })
}

export function groupInsert(data) {
  return request({
    url: '/zhdxService/api/sysGroup/insert',
    method: 'post',
    data
  })
}

export function groupUpdate(data) {
  return request({
    url: '/zhdxService/api/sysGroup/update',
    method: 'post',
    data
  })
}

export function groupDelete(data) {
  return request({
    url: '/zhdxService/api/sysGroup/delete',
    method: 'post',
    data
  })
}

export function getNoUserOfGroup(data) {
  return request({
    url: '/zhdxService/api/sysGroup/getNoUserOfGroup',
    method: 'post',
    data
  })
}

export function getHaveUserOfGroup(data) {
  return request({
    url: '/zhdxService/api/sysGroup/getHaveUserOfGroup',
    method: 'post',
    data
  })
}

export function changeUserGroup(data) {
  return request({
    url: '/zhdxService/api/sysGroup/changeUserGroup',
    method: 'post',
    data
  })
}

export function setGroupOrder(data) {
  return request({
    url: '/zhdxService/api/sysGroup/setGroupOrder',
    method: 'post',
    data
  })
}
