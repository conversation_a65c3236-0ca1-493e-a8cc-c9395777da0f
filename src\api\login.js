/*
 * @Descripttion: 
 * @Author: wangl
 * @version: 
 * @Date: 2025-03-04 09:08:12
 * @LastEditors: wangl
 * @LastEditTime: 2025-03-19 10:26:01
 */
import request from '@/utils/request'

export function loginByUsername(token) {
  // 创建一个新的请求配置，不使用拦截器中的 Authorization
  const config = {
    url: '/auth/login',
    method: 'post',
    headers: {
      'Authorization': token.midToken // 直接设置 Authorization，不使用 Bearer 前缀
    },
    data: {
      loginType: '111'
    },
    skipAuthCheck: true // 添加一个标记，用于在拦截器中识别是否跳过 token 设置
  }
  return request(config)
}

export function getUserInfo() {
  return request({
    url: '/uaa/api/account',
    method: 'post'
  })
}

export function logout() {
  return request({
    url: '/auth/logout',
    method: 'post'
  })
}

// 无需参数，直接通过保存的accesstoken请求用户信息
export function getLoginUser() {
  return request({
    url: '/userService/api/user/getLoginUser',
    method: 'post'
  })
}

export function fetchCode() {
  return request({
    url: '/userService/api/user/getCode',
    method: 'get'
  })
}
