<template>
  <div class="fail-rate-rank" :style="containerStyle">
    <div ref="myEchart" class="chart-container" />
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { debounce } from '@/utils'
require('echarts/theme/macarons') // echarts theme
const colors = ['#dd7694', '#a46244', '#e18a3b', '#984f31', '#779649', '#4f6f46', '#6b798e', '#3271ae', '#79836c', '#84a729']
export default {
  props: ['keyModel'],
  data() {
    return {
      chart: null,
      xData: [],
      data: [],
      currentBatch: 0,
      scrollTimer: null,
      displayCount: 5,
      isTransitioning: false,
      isScrollPaused: false
    }
  },
  computed: {
    containerStyle() {
      return {
        height: '100%',
        width: '100%',
        position: 'relative',
        overflow: 'hidden'
      }
    },
    totalBatches() {
      return Math.ceil(this.data.length / this.displayCount)
    },
    needBatchDisplay() {
      return this.data.length > this.displayCount
    },
    displayBatchData() {
      const start = this.currentBatch * this.displayCount
      const end = Math.min(start + this.displayCount, this.data.length)
      return {
        data: this.data.slice(start, end),
        xData: this.xData.slice(start, end)
      }
    }
  },
  watch: {
    keyModel: {
      deep: true,
      handler(val) {
        if (val !== null && val.length > 0) {
          this.xData = []
          this.data = []
          
          const sortedData = [...val].sort((a, b) => b.onlineRate - a.onlineRate)
          
          sortedData.forEach((element, index) => {
            if (index < 10) {
              this.data.push({
                value: element.onlineRate,
                itemStyle: {
                  normal: {
                    color: colors[index]
                  }
                }
              })
              this.xData.push(element.deviceTypeName)
            }
          })
          
          this.$nextTick(() => {
            this.updateChart()
            this.startScroll()
          })
        } else {
          this.xData = []
          this.data = []
          this.updateChart()
        }
      }
    }
  },
  mounted() {
    this.initChart()
    this.__resizeHanlder = debounce(() => {
      if (this.chart) {
        this.updateChart()
        this.chart.resize()
      }
    }, 100)
    window.addEventListener('resize', this.__resizeHanlder)
  },
  beforeDestroy() {
    this.stopScroll()
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    window.removeEventListener('resize', this.__resizeHanlder)
  },
  methods: {
    getAutoSize(val) {
      var nowClientHeight = document.documentElement.clientHeight
      var initHeight = 1080
      return val * (nowClientHeight / initHeight)
    },
    initChart() {
      this.chart = echarts.init(this.$refs.myEchart, 'macarons')
      this.updateChart()

      // 添加鼠标事件监听
      this.chart.getZr().on('mouseover', () => {
        this.isScrollPaused = true
      })

      this.chart.getZr().on('mouseout', () => {
        this.isScrollPaused = false
      })
    },
    updateChart() {
      if (!this.chart) return

      const { data: displayData, xData: displayXData } = this.displayBatchData
      const startIndex = this.currentBatch * this.displayCount // 计算当前批次的起始序号

      this.chart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              color: '#15ecf4'
            }
          },
          textStyle: {
            fontSize: this.getAutoSize(12),
            color: '#fff'
          },
          formatter: function (params) {
            var result = params[0].name + '<br>'
            params.forEach(function (item) {
              result += '<span style="display:inline-block;margin-right:0.46vh;border-radius:0.92vh;width:0.83vh;height:0.83vh;background-color:' + item.color + '"></span>'
              result += item.value + '%</span><br>'
            })
            return result
          }
        },
        grid: {
          top: '10%',
          left: '26%',
          right: '13%',
          bottom: '0%',
          containLabel: false
        },
        xAxis: {
          type: 'value',
          splitLine: {
            show: false
          },
          axisLabel: {
            show: true,
            textStyle: {
              fontSize: this.getAutoSize(12),
              color: '#fff'
            },
            formatter: value => value + '%'
          },
          max: 100,
          axisTick: {
            show: false
          }
        },
        yAxis: {
          z: 100,
          position: 'left',
          splitLine: {
            show: false
          },
          axisLabel: {
            inside: false,
            interval: 0,
            textStyle: {
              fontSize: this.getAutoSize(12),
              color: '#fff'
            },
            align: 'right',
            padding: [0, this.getAutoSize(2), 0, 0],
            formatter: (value, index) => {
              // 计算实际序号
              const actualIndex = startIndex + index
              // 使用对应的颜色
              const noText = `{no${actualIndex}|No.${actualIndex + 1}} `
              const nameText = `{name|${value}}`
              return noText + nameText
            },
            rich: {
              name: {
                color: '#fff',
                fontSize: this.getAutoSize(12),
                padding: [0, 0, 0, this.getAutoSize(1)]
              },
              // 为每个序号定义对应的颜色样式
              ...Array.from({ length: 10 }, (_, i) => ({
                [`no${i}`]: {
                  color: colors[i],
                  fontSize: this.getAutoSize(10),
                  fontStyle: 'italic',
                  fontWeight: 'bold',
                  padding: [0, this.getAutoSize(1), 0, 0]
                }
              })).reduce((acc, curr) => ({ ...acc, ...curr }), {})
            }
          },
          inverse: true,
          animationDuration: 300,
          animationDurationUpdate: 300,
          data: displayXData
        },
        series: [{
          type: 'bar',
          barMaxWidth: this.getAutoSize(15),
          barGap: '-100%',
          label: {
            show: true,
            position: 'right',
            fontSize: this.getAutoSize(12),
            color: '#fff',
            formatter: function(params) {
              return params.value === 0 ? '' : params.value + '%'
            },
            offset: [5, 0],
            emphasis: {
              show: true
            }
          },
          itemStyle: {
            borderRadius: [0, this.getAutoSize(7), this.getAutoSize(7), 0]
          },
          data: displayData,
          animation: true,
          animationDuration: 300,
          animationDurationUpdate: 300
        }]
      })
    },
    startScroll() {
      this.stopScroll()
      if (!this.needBatchDisplay) return

      const switchBatch = () => {
        if (this.isScrollPaused || this.isTransitioning) return
        
        this.isTransitioning = true
        setTimeout(() => {
          this.currentBatch = (this.currentBatch + 1) % this.totalBatches
          this.updateChart()
          setTimeout(() => {
            this.isTransitioning = false
          }, 300)
        }, 300)
      }

      this.currentBatch = 0
      this.scrollTimer = setInterval(switchBatch, 5000) // 每5秒切换一次
    },
    stopScroll() {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer)
        this.scrollTimer = null
      }
      this.isTransitioning = false
    }
  }
}
</script>

<style lang="scss" scoped>
.fail-rate-rank {
  background: rgba(128, 128, 128, 0.1);
  border-radius: 0.8vh;
}

.chart-container {
  height: 95%;
  width: 95%;
  margin: 0 auto;
}
</style>
