import { loginByUsername, logout, getLoginUser, getUserMenu } from '@/api/login'
import { getUserInfo, setIsNewLogin, getToken, setToken, setRfleshToken, getRfleshToken, removeToken, getUserId, setUserId, setUserInfo, removeUserId, getEntSerno, setEntSerno, removeEntSerno, setEncryptUserId, getEncryptUserId, removeEncryptUserId, getIsNewLogin, getIsShowSpot } from '@/utils/auth'

import { getStore, setStore, clearStore } from '@/views/otherResource/cmdb/util/store'
import { setIsShowSpot } from '../../utils/auth'
const user = {
  state: {
    user: '',
    status: '',
    code: '',
    token: getToken(),
    refleshToken: getRfleshToken(),
    name: '',
    avatar: '',
    introduction: '',
    buildId: '',
    userId: getUserId(),
    userInfo: getUserInfo(),
    entSchool: getEntSerno(),
    menuTree: [],
    alarmData: [],
    isReal: '0',
    realFilter: null,
    setting: {
      articlePlatform: []
    },
    isYulan: '',
    encryptUserId: getEncryptUserId(),
    isNewLogin: getIsNewLogin()
  },

  mutations: {
    SET_CODE: (state, code) => {
      state.code = code
    },
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_ENTSERNO: (state, entSerno) => {
      state.entSchool = entSerno
    },
    SET_INTRODUCTION: (state, introduction) => {
      state.introduction = introduction
    },
    SET_SETTING: (state, setting) => {
      state.setting = setting
    },
    SET_STATUS: (state, status) => {
      state.status = status
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_REFLESHTOKEN: (state, refleshToken) => {
      state.refleshToken = refleshToken
    },
    SET_USER_INFO: (state, userInfo) => {
      state.userInfo = userInfo
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_USER_ID: (state, userId) => {
      state.userId = userId
    },
    SET_MENU: (state, menuTree) => {
      state.menuTree = menuTree
    },
    SET_ALARM_DATA: (state, alarmData) => {
      state.alarmData = alarmData
    },
    SET_ISREAL: (state, isReal) => {
      state.isReal = isReal
    },
    SET_REAL_FILTER: (state, realFilter) => {
      state.realFilter = realFilter
    },
    SET_BUILD_ID: (state, buildId) => {
      state.buildId = buildId
    },
    SET_IS_YULAN: (state, isYulan) => {
      state.isYulan = isYulan
    },
    SET_ENCRYPT_USER_ID: (state, encryptUserId) => {
      state.encryptUserId = encryptUserId
    },
    SET_IS_NEW_LOGIN: (state, isNewLogin) => {
      state.isNewLogin = isNewLogin
    }
  },

  actions: {
    // 用户名登录
    LoginByUsername({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        // if (process.env.NODE_ENV === 'development') {
        //   userInfo.redirect_uri = 'http://localhost:9528/newLogin'
        // }
        loginByUsername(userInfo).then(response => {
          const data = response.data.data
          console.log(data, '获取登录结果')
          commit('SET_TOKEN', data.access_token) // 新框架改动点 token->access-token
          setToken(data.access_token)
          //  新框架改动点 获取用户信息不需要使用userid，直接请求就行
          commit('SET_REFLESHTOKEN', data.refresh_token) // 新框架改动点 token->access-token
          setRfleshToken(data.refresh_token)
          console.log('保存结束')
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    }, // 获取用户信息
    GetUserInfo({ commit }) {
      return new Promise((resolve, reject) => {
        // 新框架改动点 获取用户信息
        /* getLoginUser().then(response => {
          const data = response.data.data
          commit('SET_USER_ID', data.userId)
          setUserId(data.userId)
          setEntSerno(data.userId)
          commit('SET_NAME', data.userName)
          commit('SET_ENTSERNO', data.entSerno)
          setEncryptUserId(data.encryptUserId)
          commit('SET_ENCRYPT_USER_ID', data.encryptUserId)
          // 将用户对象保存
          setUserInfo(data)
          commit('SET_USER_INFO', data) */

        // 新框架改动点 获取动态菜单
        getUserMenu().then(response2 => {
          const data = response2.data.data
          commit('SET_MENU', data)
          resolve(data)
        }).catch(err => {
          commit('SET_MENU', [{ path: '/welcome' }])
          reject(err)
        })
        /* }).catch(error => {
          reject(error)
        }) */
      })
    },
    // 登出
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_REFLESHTOKEN', '') // 新框架改动点 token->access-token
          commit('SET_MENU', [])

          removeToken()
          removeEntSerno()
          removeUserId()
          removeEncryptUserId()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    IsNewLogin({ commit }, isNewLogin) {
      console.log('保存登录方式', isNewLogin)
      return new Promise(resolve => {
        // 保存到缓存
        setIsNewLogin(isNewLogin)
        commit('SET_IS_NEW_LOGIN', isNewLogin)
        resolve()
      })
    },
    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        commit('SET_REFLESHTOKEN', '') // 新框架改动点 token->access-token
        removeToken()
        resolve()
      })
    }
  }
}

export default user
