import request from '@/utils/request'

export function fetchList(query) {
  return request({
    url: '/admin/team/page',
    method: 'get',
    params: query
  })
}

export function teamList() {
  return request({
    url: '/admin/team/list',
    method: 'get'
  })
}

export function getObj(id) {
  return request({
    url: '/admin/team/' + id,
    method: 'get'
  })
}

export function addObj(obj) {
  return request({
    url: '/admin/team',
    method: 'post',
    data: obj
  })
}

export function putObj(obj) {
  return request({
    url: '/admin/team',
    method: 'put',
    data: obj
  })
}

export function delObj(id) {
  return request({
    url: '/cmdbService/team/' + id,
    method: 'delete'
  })
}

export function fetchParentTemTree(teamId) {
  let str = ''
  if (teamId) {
    str = '?teamId=' + teamId
  }
  return request({
    url: '/admin/team/parent/tree' + str,
    method: 'get'
  })
}

export function delUser(query) {
  return request({
    url: '/cmdbService/team/user',
    method: 'delete',
    data: query
  })
}

export function addUser(obj) {
  return request({
    url: '/cmdbService/team/user',
    method: 'post',
    data: obj
  })
}
