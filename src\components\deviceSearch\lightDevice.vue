<template>
  <div class="deviceSearchDiv">
    <div class="repair-container">
      <slot name="before" />
      <el-input v-model="listQuery.shareDeviceName" clearable style="width:20.3720vh;margin-bottom:1vh;" placeholder="请输入设备名称/设备编号" @change="changeSelectSearch(false,false,false,false,true)" />
      <el-cascader v-if="isArea" ref="selectDeptRef" v-model="listQuery.dicGroupId" style="width:16.6680vh;" clearable :options="options" :props="propstree" placeholder="请选择区域" change-on-select @change="changeSelectSearch(false,false,false,true)" />
      <!-- <el-select v-if="isArea" v-model="listQuery.dicGroupId" style="width:16.6680vh;" clearable placeholder="请选择组团" class="form-item-width" @change="changeSelectSearch(false,false,false,false)">
        <el-option v-for="item in groupOptions" :key="item.dicId" :label="item.dicName" :value="item.dicValue" />
      </el-select> -->
      <el-select v-if="isArea" v-model="listQuery.dicRoadName" style="width:16.6680vh;" filterable clearable placeholder="请选择街道" class="form-item-width" @change="changeSelectSearch(false,false,false,false)">
        <el-option v-for="item in roadOptions" :key="item.dicName" :label="item.dicName" :value="item.dicName" />
      </el-select>
      <el-select v-show="shareSysTypeShow && isSysType" v-model="listQuery.shareSysType" style="width:16.6680vh;" clearable placeholder="请选择专题类型" class="form-item-width" @change="changeSelectSearch(true,false,false,false)">
        <el-option v-for="item in shareSysTypeOption" :key="item.dicValue" :label="item.dicName" :value="item.dicValue" />
      </el-select>
      <el-select v-if="isDeviceType" v-model="listQuery.shareDeviceType" style="width:14.8160vh;" clearable placeholder="请选择设备类型" class="form-item-width" @change="changeSelectSearch(false,true,false,false)">
        <el-option v-for="item in shareDeviceTypeOption" :key="item.dicValue" :label="item.dicName" :value="item.dicValue" />
      </el-select>
      <el-select v-if="isManufacturerCode" v-model="listQuery.shareManufacturerCode" style="width:14.8160vh;" clearable placeholder="请选择设备厂商" class="form-item-width" @change="changeSelectSearch(false,false,true,false)">
        <el-option v-for="item in shareManufacturerCodeOption" :key="item.key" :label="item.value" :value="item.key" />
      </el-select>
      <el-select v-if="isManufacturerCode" v-model="listQuery.shareEquipType" style="width:14.8160vh;" clearable placeholder="请选择型号" class="form-item-width" @change="handCreate()">
        <el-option v-for="item in shareEquipTypeOption" :key="item.key" :label="item.value" :value="item.key" />
      </el-select>
      <slot name="after" />
    </div>
  </div>
</template>

<script>
import { fetchDeviceTypeList, queryDicTree, queryManufacturer, queryModel, queryDeviceType, queryDicGroup, queryDicRoad } from '@/api/system/dic/dic'
import { getSysTypeList } from '@/api/main/admin'
import { geAreaList, getGroupList } from '@/api/otherResource/cmdb/area'

export default {
  props: {
    isMutiSearch: { // 是否显示多项选择器
      type: Boolean,
      default: true
    },
    isArea: { // 是否显示区域查询
      type: Boolean,
      default: true
    },
    isSysType: { // 是否显示专题类型查询
      type: Boolean,
      default: true
    },
    isDeviceType: { // 是否显示设备类型查询
      type: Boolean,
      default: true
    },
    isDeviceTypeSource: { // 是否只显示有源设备
      type: Boolean,
      default: false
    },
    isManufacturerCode: { // 是否显示设备厂商和型号联动查询
      type: Boolean,
      default: true
    },
    shareDeviceName: {
      type: String,
      default: ''
    },
    shareSysType: { // 专题类型
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      searchDisabled: false,
      xzsbdialogFormVisible: false,
      listQuery: {
        shareDeviceName: '',
        dicGroupId: '',
        dicRoadName: '',
        shareSysType: '', // 专题类型
        shareDeviceType: '', // 设备类型
        shareManufacturerCode: '', // 设备厂商
        shareEquipType: '' // 型号
      },
      shareSysTypeOption: [],
      shareDeviceTypeOption: [],
      options: [],
      roadOptions: [],
      groupOptions: [],
      propstree: {
        value: 'markerDataId',
        label: 'markerName',
        children: 'childLayerMessage',
        emitPath: false,
        checkStrictly: true,
        expandTrigger: 'hover'
      },
      shareManufacturerCodeOption: [],
      shareEquipTypeOption: []
    }
  },
  computed: {
    shareSysTypeShow() {
      const isLight = this.$route.path === '/PMS/LightManage/management'
      const isDeviceMage = this.$route.path.indexOf('/equipmentQuery')
      return !(isLight || isDeviceMage && this.$route.query.sysType)
    }
  },
  created() {
    if (this.shareDeviceName !== '') {
      this.listQuery.shareDeviceName = this.shareDeviceName
      this.deviceNameSearch()
    }
    if (this.shareSysType > 0) {
      this.listQuery.shareSysType = this.shareSysType
    } else {
      this.listQuery.shareSysType = ''
    }
    this.init()
  },
  methods: {
    deviceNameSearch() {
      this.$emit('handleData', this.listQuery)
    },
    clearSearch(noAlert) {
      this.listQuery = {
        // shareAreaCode: '', // 区域
        shareSysType: '', // 专题类型
        shareDeviceType: '', // 设备类型
        shareManufacturerCode: '', // 设备厂商
        shareEquipType: '' // 型号
      }
      if (this.shareSysType > 0) {
        this.listQuery.shareSysType = this.shareSysType
        this.changeSelectSearch(false, true, true, false)
      } else {
        this.listQuery.shareSysType = ''
      }
      if (!noAlert) {
        this.$message({
          message: '已清除选择！',
          type: 'success'
        })
      }
      this.init()
      this.$emit('handleData', this.listQuery)
    },
    clear(query, noAlert = false) {
      for (const key in query) {
        switch (key) {
        case 'page':
          query[key] = 1
          break
        case 'limit':
          break
        case 'isSpot':
        case 'order':
        case 'sortBy':
          if (this.$route.path !== '/dashboard') {
            query[key] = ''
          }
          break
        default:
          query[key] = ''
          break
        }
      }
      this.clearSearch(noAlert)
    },

    changeSelectSearch(isDeviceType, isManufacturerCode, isShareEquipType, isAreaCodeClear, isShareDeviceName) {
      if (isDeviceType) { // 设备类型联动
        this.listQuery.shareManufacturerCode = ''
        this.listQuery.shareDeviceType = ''
        this.listQuery.shareEquipType = ''
        this.loadQueryDeviceTypeOptions()
        this.loadShareManufacturerCodeOptions()
        this.loadEquipTypeOptions()
      }
      if (isManufacturerCode) { // 设备厂商联动
        this.listQuery.shareManufacturerCode = ''
        this.listQuery.shareEquipType = ''
        this.loadShareManufacturerCodeOptions()
        this.loadEquipTypeOptions()
      }
      if (isShareEquipType) { // 型号联动
        this.listQuery.shareEquipType = ''
        this.loadEquipTypeOptions()
      }
      if (isAreaCodeClear) {
        this.$refs.selectDeptRef.dropDownVisible = false
        this.clearValue()
      }
      this.handCreate()
    },
    init() {
      if (this.shareSysType > 0) {
        this.listQuery.shareSysType = this.shareSysType
        this.changeSelectSearch(true, false, true, false)
      }
      this.loadShareAreaCodeOptions()
      this.loadDictGroupOptions()
      this.loadDictRoadOptions()
      this.loadShareSysTypeOptions()
      this.loadQueryDeviceTypeOptions()
      this.loadShareManufacturerCodeOptions()
      this.loadEquipTypeOptions()
    },
    clearValue(ev) {
      this.listQuery.shareManufacturerCode = ''
      this.listQuery.shareDeviceType = ''
      this.listQuery.shareEquipType = ''
      this.listQuery.shareSysType = ''
      if (this.shareSysType > 0) {
        this.listQuery.shareSysType = this.shareSysType
        this.changeSelectSearch(true, false, false, false)
      } else {
        this.listQuery.shareSysType = ''
      }
    },
    // 获取区域
    loadShareAreaCodeOptions() {
      getGroupList().then((r) => {
        this.options = r.data.data
        this.$nextTick(() => {
          // 添加这段代码
          const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]')
          Array.from($el).map((item) => item.removeAttribute('aria-owns'))
        })
      })
    },
    // 获取组团
    loadDictGroupOptions() {
      queryDicGroup().then((r) => {
        this.groupOptions = r.data.data
      })
    },
    // 获取街道
    loadDictRoadOptions() {
      queryDicRoad().then((r) => {
        this.roadOptions = r.data.data
      })
    },
    // 获取专题类型
    loadShareSysTypeOptions() {
      getSysTypeList({}).then((r) => {
        this.shareSysTypeOption = r.data.data.map(i => {
          i.dicValue = i.typeValue
          i.dicName = i.typeName
          return i
        })
      })
    },
    // 获取设备类型
    loadShareDeviceTypeOptions() {
      fetchDeviceTypeList().then((r) => {
        this.shareDeviceTypeOption = r.data.data.map(i => ({ dicValue: i.id, dicName: i.cname })).filter(i => !this.isDeviceTypeSource || ~~i.isSource )
      })
    },
    // 获取设备厂商
    loadShareManufacturerCodeOptions() {
      queryManufacturer({
        sysType: this.listQuery.shareSysType, // 专题类型
        deviceType: this.listQuery.shareDeviceType, // 设备类型
        areaCode: this.listQuery.dicGroupId // 区域
      }).then((r) => {
        this.shareManufacturerCodeOption = r.data.data
      })
    },
    // 级联获取设备类型
    loadQueryDeviceTypeOptions() {
      queryDeviceType({
        sysType: this.listQuery.shareSysType, // 专题类型
        areaCode: this.listQuery.dicGroupId // 区域
      }).then((r) => {
        this.shareDeviceTypeOption = []
        r.data.data.forEach(element => {
          if(!this.isDeviceTypeSource || ~~element.isSource) {
            this.shareDeviceTypeOption.push({
              dicName: element.value,
              dicValue: element.key
            })
          }
        })
      })
    },
    // 获取型号
    loadEquipTypeOptions() {
      queryModel({
        sysType: this.listQuery.shareSysType, // 专题类型
        deviceType: this.listQuery.shareDeviceType, // 设备类型
        areaCode: this.listQuery.dicGroupId, // 区域
        manufacturerCode: this.listQuery.shareManufacturerCode
      }).then((r) => {
        this.shareEquipTypeOption = r.data.data
        this.listQuery.shareEquipType = ''
      })
    },
    handCreate() {
      delete this.listQuery.deviceId
      this.$emit('handleData', this.listQuery)
    }
  }
}
</script>

<style>
    .deviceSearchDiv .el-input-group__append {
        background-color: #409EFF;
        color: #ffffff;
        cursor: pointer;
        padding: 0 0.4630vh;
    }

    .deviceSearchd {
        margin-left: 0.4630vh;
        padding: 0.1852vh;
        border-radius: 0.4630vh;
    }

    .deviceSearchFg {
        width: 0.1vw;
        background-color: #e8d9d94d;
        margin-left: 0.2vw;
    }
</style>
