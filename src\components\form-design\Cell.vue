<template>
  <div class="cell" :class="{'cell-active': data.key === $store.state.formDesign.activeKey}">
    <div>
      <el-form-item v-if="data.type !== 'grid' && data.type !== 'title'" :label="data.title+`${data.options.required?'（必填）':''}`" :prop="data.key" @click.native="activeCell">
        <el-input v-if="data.type === 'input'" v-model="data.options.defaultValue" :placeholder="data.options.placeholder" :disabled="data.options.disabled" :readonly="true" :style="{width: data.options.width, pointerEvents: 'none'}" />
        <el-input v-if="data.type === 'textarea'" v-model="data.options.defaultValue" :placeholder="data.options.placeholder" :disabled="data.options.disabled" :readonly="true" type="textarea" :rows="5" :style="{width: data.options.width, pointerEvents: 'none'}" />
        <el-input-number v-if="data.type === 'number'" :disabled="data.options.disabled" :readonly="true" :style="{width: data.options.width, pointerEvents: 'none'}" />
        <el-radio-group v-if="data.type === 'radio'" v-model="data.options.defaultValue" :disabled="data.options.disabled" :readonly="true" :style="{width: data.options.width, pointerEvents: 'none'}">
          <el-radio v-for="(item, i) in data.options.option" :key="i" :label="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
        <el-checkbox-group v-if="data.type === 'checkbox'" v-model="data.options.defaultValue" :disabled="data.options.disabled" :readonly="true" :style="{width: data.options.width, pointerEvents: 'none'}">
          <el-checkbox v-for="(item, i) in data.options.option" :key="i" :label="item.value">{{ item.label }}</el-checkbox>
        </el-checkbox-group>
        <el-date-picker v-if="data.type === 'datetime'" v-model="data.options.defaultValue" :placeholder="data.options.placeholder" :disabled="data.options.disabled" :readonly="true" :type="data.options.type[0]" :format="data.options.type[1]" :style="{width: data.options.width, pointerEvents: 'none'}" />
        <el-select v-if="data.type === 'select'" v-model="data.options.defaultValue" :placeholder="data.options.placeholder" :style="{width: data.options.width, pointerEvents: 'none'}" :readonly="true" :disabled="data.options.disabled">
          <el-option v-for="(item, i) in data.options.option" :key="i" :label="item.label" :value="item.value" />
        </el-select>
        <el-switch v-if="data.type === 'switch'" v-model="data.options.defaultValue" active-color="#13ce66" inactive-color="#EEEEEE" :style="{width: data.options.width, pointerEvents: 'none'}" :readonly="true" :disabled="data.options.disabled" />
        <el-upload v-if="data.type === 'upload'" class="avatar-uploader" :action="data.options.url" :multiple="data.options.multiple" :limit="data.options.limit" :disabled="data.options.disabled" list-type="picture" :file-list="data.options.fileList">
          <el-button size="small" type="primary">点击上传</el-button>
          <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
        </el-upload>
      </el-form-item>
      <p
        v-if="data.type === 'title'"
        :style="{'text-align': data.options.align, 'font-size': data.options.fontSize, pointerEvents: 'none'}"
        @click="activeCell"
      >{{ data.value }}</p>
      <FDGridPanel
        v-if="data.type === 'grid'"
        :prop-data="data"
        :form-attr="formAttr"
        :f-dkey="data.key"
        @syncList="syncList"
      />
    </div>
    <i
      v-show="data.key === $store.state.formDesign.activeKey && data.type != 'grid'"
      class="action-copy"
      title="复制"
      @click="copyForm"
    />
    <i
      v-show="data.key === $store.state.formDesign.activeKey"
      class="action-delete"
      title="删除"
      @click="deleteForm"
    />
  </div>
</template>

<script>
import common from '@/utils/common'
import FDGridPanel from '@/components/form-design/FDGridPanel'

export default {
  components: {
    FDGridPanel
  },
  props: {
    formAttr: {
      type: Object,
      default: function() {
        return {
          align: 'top',
          size: 'medium',
          labelWidth: 80
        }
      }
    },
    data: {
      type: Object,
      default: function() {
        return {
          type: '',
          name: '',
          options: {
            width: 24,
            defaultValue: '',
            required: false,
            dataType: 'string',
            placeholder: ''
          },
          key: '1556775967000_4898'
        }
      }
    },
    isGrid: {
      type: Boolean,
      default: false
    },
    fDindex: {
      type: Number
    }
  },
  methods: {
    copyForm() { // 复制对应的表单组件
      const formList = common.deepClone(this.$store.state.formDesign.formList)
      let newIndex
      for (let i = 0; i < formList.length; i++) {
        const element = formList[i]
        if (element.key === this.data.key) {
          newIndex = i
          break
        }
      }
      const copyForm = common.deepClone(formList[newIndex])
      copyForm.key = 'copyForm_' + common.getGuidNew()
      formList.splice(newIndex + 1, 0, copyForm)
      this.$store.commit(
        'formDesign/updateActiveForm',
        common.deepClone(copyForm)
      )
      this.$store.commit('formDesign/updateActiveKey', copyForm.key)
      this.$emit('syncList', formList)
      this.$store.dispatch('formDesign/setFormList', formList)
    },
    deleteForm() { // 删除组件
      const formList = common.deepClone(this.$store.state.formDesign.formList)
      let newIndex
      for (let i = 0; i < formList.length; i++) {
        const element = formList[i]
        if (element.key === this.data.key) {
          newIndex = i
          break
        }
      }
      formList.splice(newIndex, 1)
      this.$emit('syncList', formList)
      this.$store.dispatch('formDesign/setFormList', common.deepClone(formList))
      if (newIndex !== 0) {
        this.$store.commit(
          'formDesign/updateActiveKey',
          formList[newIndex - 1].key
        )
        this.$store.commit(
          'formDesign/updateActiveForm',
          common.deepClone(formList[newIndex - 1])
        )
      } else {
        if (formList.length > 0) {
          this.$store.commit('formDesign/updateActiveKey', formList[0].key)
          this.$store.commit(
            'formDesign/updateActiveForm',
            common.deepClone(formList[0])
          )
        }
      }
    },
    activeCell() { // 点击窗口
      this.$store.commit('formDesign/updateActiveKey', this.data.key)
      this.$store.commit('formDesign/updateShowType', this.data.type)
      this.$store.commit(
        'formDesign/updateActiveForm',
        common.deepClone(this.data)
      )
    },
    syncList(value) {
      this.$emit('syncList', value)
      // bus.$emit("formDesign.syncList", value);
    }
  }
}
</script>

<style lang="css" scoped>
.cell {
  background-color: #FFE4E4;
  position: relative;
  cursor: move;
}
.cell-active {
  background-color: #F6E5E5;
  border-left: 5px solid #F6E5E5;
}
.cell .el-form-item {
  padding-bottom: 4vh !important;
  padding: 10px 10px 20px 10px;
  margin-bottom: 0px !important;
}
.action-copy {
  position: absolute;
  bottom: -15px;
  right: 60px;
  height: 30px;
  width: 30px;
  background: url("/static/img/copy.png") no-repeat center;
  background-size: 18px 18px;
  background-color: #ecf5ff;
  border-color: #409eff;
  border-radius: 50%;
  cursor: pointer;
  border: 1px solid #409eff;
  z-index: 1;
}
.action-copy:hover {
  background: url("/static/img/copy-active.png") no-repeat center;
  background-color: #409eff;
}
.action-delete {
  position: absolute;
  bottom: -15px;
  right: 15px;
  height: 30px;
  width: 30px;
  background: url("/static/img/delete.png") no-repeat center;
  background-size: 15px 15px;
  background-color: #fef0f0;
  border-radius: 50%;
  cursor: pointer;
  border: 1px solid #f56c6c;
  z-index: 1;
}
.action-delete:hover {
  background: url("/static/img/delete-active.png") no-repeat center;
  background-color: #f56c6c;
}
</style>
