<template>
  <div>
    <div class="equipMentTotalListDiv">
      <deviceSearch ref="deviceSearch" style="display: inline-block" v-bind="deviceSearchConfig"
        :share-device-name="listQuery.shareDeviceName" :is-clear="searchClear" :share-sys-type="sysType"
        @handleData="handleData">
        <template v-slot:after>
          <!--           <el-select
            v-show="stateFilterShow"
            v-model="listQuery.alarmState"
            clearable
            placeholder="请选择告警信息"
            class="form-item-width"
          >
            <el-option label="无" :value="0" />
            <el-option label="有" :value="1" />
          </el-select> -->
          <el-select v-show="stateFilterShow" v-model="listQuery.onlineState" clearable placeholder="请选择在线状态"
            style="margin-right: 10px" class="form-item-width">
            <el-option label="在线" :value="1" />
            <el-option label="离线" :value="0" />
          </el-select>
          <div style="display:inline-block">
            <el-button type="primary" :class="{ marginLeft: deviceStatus != null }" icon="el-icon-search"
              @click="searchValue" style="background: rgba(0, 142, 254, 1); border-color: rgba(0, 142, 254, 1); color: #ffffff;">搜索</el-button>
            <el-button v-waves @click="$refs.deviceSearch.clear(listQuery)" style="background: rgba(150, 191, 222, 0.4); border-color: rgba(150, 191, 222, 0.4); color: #fff;">{{ $t("button.clear")
            }}</el-button>
            <el-button type="primary" icon="el-icon-download" :loading="exportLoading"
              @click.stop="handleExportExcelServer" style="background: rgba(0, 142, 254, 1); border-color: rgba(0, 142, 254, 1); color: #ffffff;">导出</el-button>
          </div>
        </template>
      </deviceSearch>
    </div>
    <div>
      <zyk-table :list="tableList" :list-loading="listLoading" v-bind="tableConfig" :total="total" :is-stripe="isStripe"
        :page="listQuery.page" :limit="listQuery.limit" @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange">
        <template #alarmState="scope">
          <el-tag :type="scope.row.alarmState === 1 ? 'danger' : 'success'">
            {{ scope.row.alarmState === 1 ? '有' : '无' }}
          </el-tag>
        </template>
        <template #reachableState="scope">
          <span v-if="scope.row.reachableState === -1">-</span>
          <el-tag v-else :type="scope.row.reachableState === 1 ? 'success' : 'info'">
            {{ scope.row.reachableState === 1 ? '是' : '否' }}
          </el-tag>
        </template>
        <template #onlineState="scope">
          <el-tag :type="scope.row.onlineState === 0 ? 'info' : 'success'">
            {{ scope.row.onlineState === 0 ? '离线' : '在线' }}
          </el-tag>
        </template>
        <template #handler="scope">
          <el-button v-if="positionValueEmpty(scope.row)" type="primary" size="mini" plain
            icon="el-icon-location-outline" @click="positionMap(scope.row)">定位地图</el-button>
          <el-button v-if="deviceStatus === 2" type="primary" size="mini" plain icon="el-icon-search"
            @click="handleDetail(scope.row)">告警详情</el-button>
          <el-button type="primary" size="mini" plain icon="el-icon-search"
            @click="deviceHandleDetail(scope.row)">设备详情</el-button>
          <!-- <el-button v-if="deviceStatus === 1" type="primary" size="mini" plain icon="el-icon-tickets" @click="$refs.dispatchDialog.open(scope.row)">派单</el-button> -->
        </template>
      </zyk-table>
    </div>
    <el-dialog ref="dialogAlarmList" v-dialogDrag append-to-body class="alarmDialogDetail dashboard-dialog"
      title="设备告警列表" :visible.sync="warnBoxVisible" width="142.22vh">
      <dealAlarmList :show-dialog="warnBoxVisible" :query-type="1" :alarm-device-id="deviceId" :is-show-search="false"
        @resetChoose="dealAlarmListClose" @positionMap="dealAlarmListClose" />
    </el-dialog>
    <el-dialog ref="dialog" v-dialogDrag append-to-body class="dashboard-dialog" title="告警详细信息"
      :visible.sync="dialogFormVisible" width="70%">
      <detail :device-id="deviceId" :record-id="recordId" :is-show-position-map-btn="false"
        :show-dialog="dialogFormVisible" @handCancle="handCancle" @positionMap="positionMap" />
    </el-dialog>
    <el-dialog ref="dialogLamp" v-dialogDrag destroy-on-close append-to-body class="layerManager dashboard-dialog"
      :title="deviceDetailModel.title" :before-close="beforeClose" :visible.sync="sbxqdialogFormVisible"
      :width="dialogWidth" height="37.037vh">
      <lampPoleView v-if="sbxqdialogFormVisible" ref="lampPoleView" destroy-on-close topo-id="dashboardTopo01"
        :is-need-pagination="true" :key-value="deviceDetailModel" :device-id="deviceId"
        :is-show-lamp-image="isShowLampImage" :show-dialog="sbxqdialogFormVisible" @handCancle="sbxqhandCancle"
        @changeVideoInfo="changeVideoInfo" @resetChoose="lampPoleViewClose" @positionMap="lampPoleViewClose" />
    </el-dialog>
    <dispatch-dialog ref="dispatchDialog" :is-dark-bg="true" @success="getList" />
  </div>
</template>
<script>
import ZykTable from '@/components/table/index.vue'
import deviceSearch from '@/components/deviceSearch/index'
import detail from '@/views/system/alarm/detail'
import dealAlarmList from '@/views/dashboard/admin/alarmOverview/noDealAlarmList.vue'
import lampPoleView from '@/views/dashboard/admin/dialogViews/lampPoleView'
import dispatchDialog from '@/views/PMS/alarmManagement/realtimeAlarm/dispatchDialog'

import { tableConfig } from './equipMentTotalListConfig.js'
import { fetchList } from '@/api/otherSystem/equipmentManagement/equipmentQuery.js'
import { getDeviceAttribute } from '@/api/main/admin'
import download from '@/views/otherResource/soc/mixins/download'

export default {
  name: 'EquipMentTotalList',
  components: {
    ZykTable,
    deviceSearch,
    detail,
    dealAlarmList,
    lampPoleView,
    dispatchDialog
  },
  mixins: [download],
  props: {
    // 显示弹窗
    showDialog: {
      type: Boolean,
      default: false
    },
    // 是否重置数据
    isClear: {
      type: Boolean,
      default: false
    },
    sysType: {
      type: Number,
      default: null
    },
    deviceStatus: {
      type: Number,
      default: null
    },
    deviceIdList: {
      type: Array,
      default: null
    },
    stateFilterShow: {
      type: Boolean,
      default: false
    },
    // 新增：当前选中的区域代码
    selectedAreaCode: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      listQuery: {},
      deviceSearchConfig: {
        isMutiSearch: true, // 是否显示多项选择器(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
        isArea: false, // 是否显示区域查询(true:显示，true:不显示。默认显示，非必填)=》字段可根据根据需求增减
        isSysType: false, // 是否显示专题类型查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
        isDeviceType: true, // 是否显示设备类型查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
        isManufacturerCode: true, // 是否显示设备厂商和型号联动查询(true:显示，false:不显示。默认显示，非必填)=》字段可根据根据需求增减
        isClear: false
      },
      tableList: [],
      listLoading: false,
      total: 90,
      searchClear: false,
      deviceId: null,
      recordId: null,
      dialogFormVisible: false,
      warnBoxVisible: false,
      alarmSysType: '',
      tHight: null,
      deviceDetailModel: [],
      sbxqdialogFormVisible: false,
      dialogWidth: '800px',
      // 报表的配置
      tableConfig,
      isStripe: false,
      isShowLampImage: false,
      exportLoading: false
    }
  },
  computed: {
    positionValueEmpty() {
      return (row) => {
        return !!((row.lat && row.lon))
      }
    },
    // 获取当前选中区域对应的区域代码
    currentAreaCode() {
      if (!this.selectedAreaCode || this.selectedAreaCode === 'all') {
        return null
      }
      
      const areaCodeMap = {
        'rongxi': '12',    // 容西片区
        'zanang': '13',    // 昝岗片区  
        'start': '14',     // 启动区
        'xiongdong': '15'  // 雄东片区
      }
      
      return areaCodeMap[this.selectedAreaCode] || null
    }
  },
  watch: {
    showDialog(newValue, oldValue) {
      if (newValue) {
        if (this.isClear) {
          this.searchClear = true
          this.init()
        }
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.listQuery = {
        shareAreaCode: '', // 区域
        shareSysType: this.sysType, // 专题类型
        shareDeviceType: '', // 设备类型
        shareManufacturerCode: '', // 设备厂商
        shareEquipType: '', // 型号
        shareDeviceName: '', // 设备名称/设备编号
        alarmState: null,
        onlineState: null,
        isSpot: 0,
        page: 1,
        limit: 10,
        sortBy: 'updateTime',
        order: 1
      }
      if (this.$refs.deviceSearch) this.$refs.deviceSearch.clear(this.listQuery, true)
      this.total = 0
      this.tableList = []
      this.tableConfig.showPagination = false
      this.deviceSearchConfig.isSysType = !this.sysType
      this.getList()
    },
    // 后端导出excel
    handleExportExcelServer() {
      // 处理loading
      this.exportLoading = true
      const data = { ...this.listQuery }
      let nametype = ''
      if (this.deviceStatus === 0) {
        data.onlineState = 1
        nametype = '在线'
      } else if (this.deviceStatus === 1) {
        data.onlineState = 0
        nametype = '离线'
      } else if (this.deviceStatus === 2) {
        data.onlineState = 1
        data.alarmState = 1
        nametype = '当前告警'
      }
      data.sysType = this.sysType || '0'
      
      // 导出时也添加区域过滤条件
      if (this.currentAreaCode) {
        data.shareAreaCode = this.currentAreaCode
      }
      
      delete data.page
      delete data.limit
      data.isOverView = 2
      const url = '/szdlService/api/deviceManage/exportExcel'
      const title = `${nametype}设备导出报表`

      this.download({
        url,
        data,
        method: 'post',
        fileName: `${title}.xlsx`
      }, 'application/vnd.ms-excel').then(res => {
        this.exportLoading = false
      }).catch(() => {
        this.exportLoading = false
      })
    },
    getList() {
      this.listLoading = true
      if (this.deviceIdList != null && this.deviceIdList.length > 0) {
        this.listQuery.deviceIdList = this.deviceIdList
      }
      const data = { ...this.listQuery }
      if (this.deviceStatus === 0) {
        data.onlineState = 1
      } else if (this.deviceStatus === 1) {
        data.onlineState = 0
      } else if (this.deviceStatus === 2) {
        data.onlineState = 1
        data.alarmState = 1
      }
              data.sysType = this.sysType || '0'
        
        // 根据当前选中的区域添加区域过滤条件
        if (this.currentAreaCode) {
          data.shareAreaCode = this.currentAreaCode
        }
        
        fetchList(data).then(res => {
        if (res.data.code === 200) {
          this.total = res.data.total
          this.tableList = res.data.data
          this.tableConfig.showPagination = !(this.total < 10)
        }
        this.listLoading = false
      }).catch(err => {
        console.log('err', err)
        this.listLoading = false
      })
    },
    // 设备的搜索框返回的值
    handleData(data) {
      this.listQuery = Object.assign(this.listQuery, data)
      console.log('dsdas', data, this.listQuery)
    },
    searchValue() {
      this.listQuery.page = 1
      this.getList()
    },
    handleSizeChange(val) {
      // 每页显示条目个数
      this.listQuery.limit = val
      this.getList()
    },
    handleCurrentChange(val) {
      // 上一页/下一页
      this.listQuery.page = val
      this.getList()
    },
    beforeClose() {
      this.$refs.lampPoleView && this.$refs.lampPoleView.close()
      this.sbxqdialogFormVisible = false
    },
    // 定位地图
    positionMap(rowData) {
      rowData['latitude'] = rowData.lat
      rowData['longitude'] = rowData.lon
      this.startPositionMap(rowData)
    },
    startPositionMap(rowData) {
      this.handCancle()
      if (rowData.latitude == null || rowData.longitude == null) {
        this.$message.error('该设备未获取到经纬度坐标信息，无法定位跳转到对应告警点位，请检查！')
        // 取消选中设备
        this.$emit('resetChoose', rowData)
        return
      }
      // 其他的一些定位地图的操作
      this.$emit('positionMap', rowData)
    },
    handleDetail(rowData) {
      this.recordId = parseInt(rowData.recordId)
      this.deviceId = rowData.deviceId
      this.initDialog()
      this.alarmSysType = parseInt(rowData.sysType)
      if (rowData.alarmCount > 1) {
        this.warnBoxVisible = true
      } else {
        this.dialogFormVisible = true
      }
    },
    initDialog() {
      // 重置dialog的位置
      setTimeout(() => {
        if (this.$refs.dialogAlarmList) {
          this.$refs.dialogAlarmList.$el.firstChild.style.top = 0
          this.$refs.dialogAlarmList.$el.firstChild.style.left = 0
        }
        if (this.$refs.dialogLamp) {
          this.$refs.dialogLamp.$el.firstChild.style.top = 0
          this.$refs.dialogLamp.$el.firstChild.style.left = 0
        }
      })
    },
    handCancle() {
      this.dialogFormVisible = false
      this.warnBoxVisible = false
      this.sbxqhandCancle()
    },
    dialogPositionMap(rowData) {
      this.handCancle()
      this.positionMap(rowData)
    },
    dealAlarmListClose(deviceData) {
      this.startPositionMap(deviceData)
    },
    // 设备详情
    deviceHandleDetail(rowData) {
      this.recordId = parseInt(rowData.recordId)
      this.deviceId = rowData.deviceId
      this.initDialog()
      this.sbxqShow(this.deviceId, false)
      if (this.tHight !== null) {
        clearInterval(this.tHight)
        this.tHight = null
      }
      this.tHight = setInterval(() => {
        if (this.sbxqdialogFormVisible === true) {
          this.sbxqShow(this.deviceId)
        }
      }, 5000)
    },
    // 设备详情弹框关闭
    sbxqhandCancle() {
      this.sbxqdialogFormVisible = false
      clearInterval(this.tHight)
      this.tHight = null
    },
    changeVideoInfo(id) {
      clearInterval(this.tHight)
      this.sbxqShow(id, true)
      // this.clearAreaColor()
    },
    // 设备详情弹框
    async sbxqShow(deviceId, flag) {
      if (!flag) {
        this.deviceId = deviceId
      }
      await getDeviceAttribute({
        deviceId: deviceId
      }).then(response => {
        const data = response.data.data
        data.deviceData.sort((a, b) => a.index - b.index)
        this.deviceDetailModel = data
        if (this.deviceDetailModel.deviceType === 6 || this.deviceDetailModel.deviceType === 12 || this.deviceDetailModel.deviceType === 15 || this.deviceDetailModel.deviceType === 8 || this.deviceDetailModel.deviceType === 9 || this.deviceDetailModel.deviceType === 10) {
          this.isShowLampImage = true
        } else {
          this.isShowLampImage = false
        }
      })
      if (this.deviceDetailModel.deviceType !== 6 && this.deviceDetailModel.deviceType !== 7 && this.deviceDetailModel.deviceType !== 12 && this.deviceDetailModel.deviceType !== 15 && this.deviceDetailModel.deviceType !== 8 && this.deviceDetailModel.deviceType !== 9 && this.deviceDetailModel.deviceType !== 10) {
        this.dialogWidth = '111.11vh'
      } else {
        const w = document.documentElement.clientHeight || document.body.clientHeight * 0.75 * 1.78 + 'vh'
        this.dialogWidth = (w * 0.9) + 'vh'
        console.log('width', this.dialogWidth)
      }
      this.sbxqdialogFormVisible = true
      // this.clearAreaColor()
    },
    // 设备详情点击了定位地图
    lampPoleViewClose(alarmInfo) {
      this.positionMap(alarmInfo)
    }
  }
}
</script>
<style scoped>
.el-tag--dark {
  background-color: #409eff !important;
  color: #fff !important;
}

.el-tag--dark.el-tag--info {
  background-color: #909399 !important;
  border-color: #909399 !important;
  color: #fff !important;
}

.el-tag--dark.el-tag--danger {
  background-color: #f56c6c !important;
  border-color: #f56c6c !important;
  color: #fff !important;
}

.marginLeft {
  margin-left: 10px;
}

/* .el-tag.el-tag--danger {
  background-color: #f56c6c;
  border-color: #f56c6c;
  color: #fff;
}
.el-tag.el-tag--warning{
  background-color: #e6a23c;
  border-color: #e6a23c;
  color: #fff;
} */
</style>
