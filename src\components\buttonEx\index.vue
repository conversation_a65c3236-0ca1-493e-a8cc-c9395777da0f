<template>
  <el-button :type="btnType" :loading="loading" :disabled="btnDisabled" @click="btnClick">{{ this.btnText }}</el-button>
</template>
<script>
export default {
  props: {
    btnText: {
      type: String,
      default: '确 定' // 按钮文字
    },
    disabled: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'primary' // 按钮文字
    }
  },
  data() {
    return {
      loading: false,
      btnDisabled: false,
      btnType: this.type
    }
  },
  watch: {
    disabled(val) {
      this.btnDisabled = val
    },
    type(val) {
      this.btnType = val
    }
  },

  created() {

  },

  methods: {

    btnClick() {
      // 在这里调用父页面的业务方法，调用前把按钮disabled设置为true，loading改为true
      this.loading = true
      this.btnDisabled = true

      this.$emit('handle', callback => {
        // 父页面返回结果之后，把按钮disabled设置为false，loading改为false
        setTimeout(() => {
          this.loading = false
          this.btnDisabled = false
        }, 500) // 延迟半秒设置为可点击，防止鼠标双击
      })

      setTimeout(() => {
        this.loading = false
        this.btnDisabled = false
      }, 2000) // 延迟2秒，不管是否有回调，必须强行设置为可点击，防止出现业务逻辑异常导致没有执行回调
    }
  }

}
</script>
