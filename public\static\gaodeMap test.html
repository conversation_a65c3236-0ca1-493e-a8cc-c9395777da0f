<!doctype html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>图片图层</title>
    <!-- 百度地图API -->
    <script type="text/javascript"
        src="https://api.map.baidu.com/api?v=3.0&type=webgl&ak=RdSdsNm7wW2fspZRdXGVcomFOHBfhKiT"></script>
    <script src="https://unpkg.com/@bmapgl-plugin/cluster"></script>
    <!-- 百度地图聚合插件 -->
    <!-- <script type="text/javascript"
        src="https://api.map.baidu.com/library/MarkerClusterer/1.2/src/MarkerClusterer_min.js"></script> -->
    <!--     <script type="text/javascript" src="https://api.map.baidu.com/library/TextIconOverlay/1.2/src/TextIconOverlay_min.js"></script> -->
    <script src="/static/js/config.js"></script>
    <style>
        html,
        body {
            width: 100%;
            height: 100%;
            margin: 0;
            font-family: "微软雅黑";
            overflow: hidden;
        }

        .amap-logo,
        .amap-copyright {
            visibility: hidden;
        }

        #allmap {
            width: 100%;
            height: 100%;
        }

        #r-result {
            width: 100%;
            margin-top: 5px;
        }

        #mapAdress {
            position: absolute;
            background: white;
            display: none;
        }

        #deviceList {
            position: absolute;
            display: none;
        }

        .anchorBL {
            display: none;
        }

        p {
            margin: 5px;
            font-size: 14px;
        }

        .donghua1 {
            height: 32px;
            width: 32px;
            position: relative;
        }

        .donghua1:hover {
            animation: rotate 1s linear infinite;
        }

        .donghua1::before {
            content: '';
            height: 8px;
            width: 32px;
            background: #000;
            opacity: .8;
            border-radius: 50%;
            position: absolute;
            top: 35px;
            left: 0;
            animation: shadow 1s linear infinite;
        }

        .donghua2 {
            width: 32px;
            height: 32px;
            margin: 15px 0;
            animation: breath 0.8s infinite;
            animation-direction: alternate;
            -webkit-animation-direction: alternate;
            /* -webkit-transition: all 1s ease;
			-moz-transition: all 1s ease;
			-o-transition: all 1s ease; */
        }

        .donghua3 {
            height: 32px;
            width: 32px;
            position: relative;
            animation: breath 1.2s infinite;
        }

        .boxDetail {
            background-color: rgba(15, 61, 103, 0.8);
            color: white;
            font-size: 0.7vw;
            line-height: 25px;
            white-space: nowrap;
            position: absolute;
            bottom: 45px;
            left: -6.5vw;
            border: 1px solid #377aff;
            border-radius: 0.3vw;
            padding: 0.3vw 0.7vw;
            z-index: 200;
        }

        .deviceDetail {
            background: #0F3D67;
            background-color: rgba(15, 61, 103, 0.8);
            color: white;
            font-size: 0.7vw;
            line-height: 25px;
            white-space: nowrap;
            position: absolute;
            bottom: 45px;
            /* left: -6.5vw; */
            border: 1px solid #0F3D67;
            border-radius: 0.3vw;
            padding: 0.3vw;
            z-index: 101;
        }

        .closeBtn {
            color: white;
            cursor: pointer;
        }

        .borrow {
            position: absolute;
            bottom: -6px;
            left: 50%;
            margin-right: 3px;
            border-top-color: #ebeef5;
            border-bottom-width: 0;
            border-width: 6px;
            filter: drop-shadow(0 2px 12px rgba(0, 0, 0, .03));
        }

        .borrow:after {
            bottom: -6px;
            margin-left: -11px;
            border-top-color: #0F3D67 !important;
            border-bottom-width: 0;
            content: " ";
            border-width: 6px;
            position: absolute;
            display: block;
            width: 0;
            height: 0;
            border-color: transparent;
            border-style: solid;
        }

        .borrow3 {
            position: absolute;
            bottom: -6px;
            left: 55%;
            margin-right: 3px;
            border-top-color: #ebeef5;
            border-bottom-width: 0;
            border-width: 6px;
            filter: drop-shadow(0 2px 12px rgba(0, 0, 0, .03));
        }

        .borrow3:after {
            bottom: -6px;
            margin-left: -11px;
            border-top-color: #00ff97 !important;
            border-bottom-width: 0;
            content: " ";
            border-width: 6px;
            position: absolute;
            display: block;
            width: 0;
            height: 0;
            border-color: transparent;
            border-style: solid;
        }

        .borrow2 {
            position: absolute;
            top: -6px;
            left: 50%;
            margin-right: 3px;
            border-bottom-color: #ebeef5;
            border-top-width: 0;
            border-width: 6px;
            filter: drop-shadow(0 2px 12px rgba(0, 0, 0, .03));
        }

        .borrow2:after {
            bottom: -6px;
            margin-left: -11px;
            border-bottom-color: #0F3D67 !important;
            border-top-width: 0;
            content: " ";
            border-width: 6px;
            position: absolute;
            display: block;
            width: 0;
            height: 0;
            border-color: transparent;
            border-style: solid;
        }

        .lightGy {
            /* animation: myfirst 1.5s infinite;
            box-shadow: 0px -15px 20px #fc780f;
            border-radius: 50%; */
        }

        .BMap_mask {
            z-index: -1 !important;
        }

        .animate {
            -webkit-animation: ripple 4s linear infinite alternate;
            animation: ripple 4s linear infinite alternate;
        }

        @keyframes ripple {
            0% {
                -webkit-transform: scale(0.8);
                transform: scale(0.8);
            }

            100% {
                -webkit-transform: scale(1.2);
                transform: scale(1.2);
                background-color: transparent;
            }
        }

        @keyframes shadow {

            0%,
            100% {
                transform: scaleX(1);
            }

            50% {
                transform: scaleX(1.2);
            }
        }

        @keyframes rotate {
            0% {
                transform: translateY(0);
            }

            25% {
                transform: translateY(5px);
            }

            50% {
                transform: translateY(10px) scale(1.1, 0.9);
            }

            75% {
                transform: translateY(5px);
            }

            100% {
                transform: translateY(0);
            }
        }

        @keyframes breath {
            0% {
                width: 32px;
                height: 32px;
                margin-left: 0px;
            }

            25% {
                width: 36px;
                height: 36px;
                margin-left: -2.5px;
            }

            50% {
                width: 40px;
                height: 40px;
                margin-left: -5px;
            }

            75% {
                width: 36px;
                height: 36px;
                margin-left: -2.5px;
            }

            100% {
                width: 32px;
                height: 32px;
                margin-left: 0px;
            }
        }

        @keyframes myfirst {
            10% {
                transform: scale(1)
            }

            ;

            100% {
                transform: scale(8)
            }
        }

        @keyframes fadein {
            0% {
                opacity: 0
            }

            ;

            100% {
                opacity: 1
            }
        }

        @keyframes zoomin {
            0% {
                transform: scale(0)
            }

            ;

            100% {
                transform: scale(1)
            }
        }
    </style>
</head>

<body>
    <div id="allmap"></div>
    <div id="mapAdress"></div>
    <div id="deviceList"></div>
    <script>
        // 确保DOM和百度地图API完全加载后再初始化
        window.onload = function () {
            //获取地址栏参数
            function getUrlParam(name) {
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
                var r = window.location.search.substr(1).match(reg);
                if (r != null) return unescape(r[2]);
                return null;
            }

            // 初始化变量
            var markerList = [];
            var maskLayer = undefined;
            var fromView = getUrlParam('from'); //是否来自大屏
            var center = window.config ? window.config.MAP_CENTER : [116.404, 39.915];
            var zoom = window.config ? window.config.MAP_ZOOM : 13;
            var markerClusterer = null;
            var boxList = [];
            var deviceTypeList = [];
            var powerMapLayer = null;//环网地图  电力地图
            var boxList = []//聚合点的悬浮展示弹窗

            // 坐标转换函数：GCJ02(高德) -> BD09(百度)
            function gcj02ToBd09(lng, lat) {
                var z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * Math.PI);
                var theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * Math.PI);
                var bd_lng = z * Math.cos(theta) + 0.0065;
                var bd_lat = z * Math.sin(theta) + 0.006;
                return [bd_lng, bd_lat];
            }

            // 初始化地图
            var map = new BMapGL.Map("allmap");
            // 根据配置设置中心点和缩放级别
            var centerPoints = new BMapGL.Point(center[0], center[1]);
            map.centerAndZoom(centerPoints, zoom);
            map.setMapStyleV2({
                styleId: '0d52122a8286897bf013877cf362e4ad'
            });
            map.enableScrollWheelZoom(); // 允许滚轮缩放
            map.enableDragging();//允许地图拖拽

            // 添加地图控件
            //map.addControl(new BMapGL.ScaleControl()); // 比例尺
            //map.addControl(new BMapGL.NavigationControl()); // 导航控件
            // 添加标记函数 当前版本可用  非marker 使用overlay
            function addMarker(lng, lat, data, type, chooseId) {
                // 创建坐标点
                let point = new BMapGL.Point(lng, lat);
                let marker = null;
                // 如果有自定义内容，使用CustomOverlay
                if (data) {
                    let customContent = createCustomMarker(data, type, chooseId);
                    // 创建自定义覆盖物类
                    class CustomMarker extends BMapGL.Overlay {
                        constructor(point, content) {
                            super();
                            this._point = point;
                            this._content = content;
                            this._deviceData = data;
                        }

                        initialize(map) {
                            this._map = map;
                            this._div = this._content;
                            map.getPanes().markerPane.appendChild(this._div);
                            return this._div;
                        }

                        draw() {
                            const position = this._map.pointToOverlayPixel(this._point);
                            this._div.style.left = position.x - 20 + 'px';
                            this._div.style.top = position.y - 20 + 'px';
                            this._div.style.position = 'absolute';
                        }
                    }

                     marker = new CustomMarker(point, customContent);
                } else {
                    // 如果没有自定义内容，使用普通标记
                     marker = new BMapGL.Marker(point);
                }

                //map.addOverlay(marker);
                markerList.push(marker);
                return marker;
            }

            // 创建自定义标记内容  
            function createCustomMarker(deviceData, type, chooseId) {
                var div = document.createElement("div");
                div.id = "deviceBox_" + deviceData.deviceId;
                div.style.position = "absolute";
                div.style.cursor = "pointer"

                if (chooseId === deviceData.deviceId) {
                    div.style.background = "url(./img/border.png) round";
                }

                div.style.display = "flex";
                div.style.flexDirection = "column";
                div.style.alignItems = "center";

                var iconUrl = 'CAM_1.png';

                // 添加详情框
                if (fromView == 1 && type === 1) {
                    iconUrl = getIconName(deviceData);

                    var detailDiv = document.createElement("div");
                    var htmlStr = '<div id="deviceDetail_' + deviceData.deviceId + '" style="display:none; transform: translateX(-50%)" class="deviceDetail">';
                    htmlStr += '<div style="border-bottom: 1px solid #f3e47124;"><span style="font-weight:bolder;font-size: 0.8vw;color:#03EDFB;">' + deviceData.deviceName + '</span></div>';

                    if (deviceData.onlineState === 1 && deviceData.alarmState !== 1) {
                        htmlStr += '<div>状态：<span style="color:#09E42B;">在线</span></div>';
                    } else if (deviceData.onlineState == 0) {
                        htmlStr += '<div>状态：<span style="color:#B1B1B1;">离线</span></div>';
                    } else if (deviceData.onlineState == 1 && deviceData.alarmState === 1) {
                        htmlStr += '<div>状态：<span style="color:#FF0000;">告警</span></div>';
                    }

                    if (deviceData.offlineReason) {
                        htmlStr += `<div>原因：<span style="color:#B1B1B1;">${deviceData.offlineReason}</span></div>`;
                    }

                    htmlStr += '<div x-arrow="" class="borrow"></div>';
                    htmlStr += '<div x-arrow="" class="borrow2" style="display: none"></div></div>';
                    detailDiv.innerHTML = htmlStr;
                    div.appendChild(detailDiv);
                }

                // 设置图标
                const url = `/cmdbService/images/${iconUrl}`;
                div.innerHTML += `<div style='height: 40px;width: 40px;border-radius: 50%;background: url("${url}");background-size: ${iconUrl.indexOf('_') !== -1 ? '40px 40px;' : '60px 60px;background-position: -10px -10px;'}background-repeat: no-repeat' class="lightGy"></div>`;

                // 点击事件
                div.onmousedown = function (e) {
                    if (e.button === 0) {
                        if (fromView == 1 && type === 1) {
                            window.parent.postMessage({ type: 'getDevicePoint', data: deviceData }, '*');
                        }
                    }
                    else if (e.button === 2) {
                        if (deviceData.deviceType === '587' || deviceData.deviceType === '586') {
                            window.parent.postMessage({ type: 'getPowerMap', data: deviceData }, '*');
                        }
                    }
                }

                // 悬浮事件
                div.onmouseover = function (e) {
                    var dom = document.getElementById('deviceDetail_' + deviceData.deviceId);
                    if (dom && (fromView == 1 && type === 1)) {
                        dom.style.display = 'block';

                        if (dom.parentNode.parentNode.parentNode.parentNode.offsetTop <= dom.offsetHeight + 20) {
                            dom.style.bottom = 'unset';
                            dom.style.top = '45px';
                            dom.getElementsByClassName('borrow2')[0].style.display = "block";
                            dom.getElementsByClassName('borrow')[0].style.display = "none";
                        } else {
                            dom.style.bottom = '45px';
                            dom.style.top = 'unset';
                            dom.getElementsByClassName('borrow')[0].style.display = "block";
                            dom.getElementsByClassName('borrow2')[0].style.display = "none";
                        }

                        dom.parentNode.parentNode.parentNode.parentNode.style.zIndex = 100;
                    }
                }

                // 悬浮移除事件
                div.onmouseout = function () {
                    var dom = document.getElementById('deviceDetail_' + deviceData.deviceId);
                    if (dom && (fromView == 1 && type === 1)) {
                        dom.style.display = 'none';
                        dom.parentNode.parentNode.parentNode.parentNode.style.zIndex = 0;
                    }
                }

                return div;
            }

            // 图标名称获取函数
            function getIconName(deviceData) {
                // 灯亮度图标
                if (deviceData.deviceType === '6' && deviceData.onoff === 1 && deviceData.alarmState === 0 && deviceData.onlineState === 1) {
                    if (deviceData.bir >= 0 && deviceData.bir < 20) {
                        return 'img1.png';
                    } else if (deviceData.bir >= 20 && deviceData.bir < 40) {
                        return 'img2.png';
                    } else if (deviceData.bir >= 40 && deviceData.bir < 60) {
                        return 'img3.png';
                    } else if (deviceData.bir >= 60 && deviceData.bir < 80) {
                        return 'img4.png';
                    } else if (deviceData.bir >= 80 && deviceData.bir <= 100) {
                        return 'img5.png';
                    }
                }

                const deviceState = deviceData.onlineState === 0 ? 1 : deviceData.alarmState === 1 ? 2 : 0;
                return deviceTypeList[deviceData.deviceType] ? deviceTypeList[deviceData.deviceType][['onlineFileName', 'offlineFileName', 'alertFileName'][deviceState]] : 'dingwei';
            }
            // 实现点聚合
            function createMarkerClusterer(markers) {
                // 销毁已有点聚合
                if (markerClusterer) {
                    markerClusterer.destroy();
                    markerClusterer = null;
                }
                // 记录原始标记，以便在集群变化时控制显示/隐藏
                var originalMarkers = markers.slice();
                // 将markers转换为GeoJSON格式的点数据
                const points = markers.map(marker => {
                    let lng, lat;
        
                    // 检查marker类型并获取位置
                    if (marker instanceof BMapGL.Marker) {
                        // 原生标记，使用getPosition方法
                        const position = marker.getPosition();
                        lng = position.lng;
                        lat = position.lat;
                    } else if (marker instanceof BMapGL.Overlay || marker._point) {
                        // 自定义覆盖物，使用_point属性
                        lng = marker._point.lng;
                        lat = marker._point.lat;
                    } else {
                        console.error('未知类型的标记:', marker);
                        // 提供默认值避免报错
                        lng = 0;
                        lat = 0;
                    }
                    return {
                        type: "Feature",
                        properties: {
                            // 保存marker的原始信息，用于自定义渲染
                            originMarker: marker
                        },
                        geometry: {
                            type: "Point",
                            coordinates: [lng, lat]
                        }
                    };
                });

                // 创建新的点聚合实例
                markerClusterer= new Cluster.View(map, {
                    clusterMinPoints: 2,         // 最小聚合数量
                    clusterMaxZoom: 18,          // 最大聚合缩放级别
                    updateRealTime: true,        // 实时更新
                    fitViewOnClick: false,       // 点击聚合点时是否自动调整视图
                    //isAnimation:true,
                    clusterType: [
                        [3, null, Cluster.ClusterType.DIS_PIXEL, 50] // 使用像素距离聚合
                    ],
                    //点输入属性
                    clusterMap:(props)=>{
                        // 初始化设备数据对象，可以按照设备类型分类
                        const deviceData = {
                            devices: [] // 存储设备数据的数组
                        };
                        // 从特征中提取设备数据
                        let extractedData = null;
                        if (props && props.properties && props.properties.originMarker && 
                            props.properties.originMarker._deviceData) {
                            extractedData = props.properties.originMarker._deviceData;
                        } else if (props && props.originMarker && props.originMarker._deviceData) {
                            extractedData = props.originMarker._deviceData;
                        } else if (props && props._deviceData) {
                            extractedData = props._deviceData;
                        }
                        
                        // 如果找到设备数据，添加到数组中
                        if (extractedData) {
                            deviceData.devices.push(extractedData);
                        }
                        
                        return deviceData;
                    },
                    //点聚合属性
                    clusterReduce: (accumulated, props) =>{
                        console.log('Before reduce - accumulated:', JSON.stringify(accumulated));
                        console.log('Before reduce - props:', JSON.stringify(props));
                        // 如果 props 中有设备数据，添加到 accumulated 中
                                   // 确保 accumulated 中有 devices 数组
                                   if (!accumulated.devices) {
                                accumulated.devices = [];
                            }
                        if (props && props.devices && Array.isArray(props.devices)) {
                            
                            // 添加新的设备数据，避免重复
                            props.devices.forEach(device => {
                                if (!device) return; // 跳过空设备
            
                                // 检查是否已存在
                                const exists = accumulated.devices.some(item => 
                                    item && item.deviceId === device.deviceId
                                );
                                
                                // 如果不存在则添加
                                if (!exists) {
                                    accumulated.devices.push(device);
                                }
                            });
                        }
                        console.log('After reduce - accumulated:', JSON.stringify(accumulated));
                        return accumulated;
                    },
                    // 自定义聚合点样式
                    renderClusterStyle: {
                        type: Cluster.ClusterRender.DOM,
                        style: {
                            anchors: [0.5, 0.5],  // 锚点位置
                            offsetX: 0,
                            offsetY: 0
                        },
                        inject: function(context) {
                            console.log('context',context);
                            const count = context.pointCount || 1;
                            
                            // 创建自定义DOM元素
                            const div = document.createElement('div');
                            div.style.background = `radial-gradient(50% 50% at 50% 50%, rgba(1, 255, 255, 0) 0%, rgba(1, 255, 255, 0.8) 100%)`;
                            div.style.lineHeight = '18px';
                            div.style.color = '#ffffff';
                            div.style.fontSize = '12px';
                            div.style.textAlign = 'center';
                            div.style.borderRadius = '50%';
                            div.style.padding = '10px';
                            div.style.width = '18px';
                            div.style.height = '18px';
                            div.innerHTML = count;
                            
                            return div;
                        }
                    },
                    // 单个点使用原始标记
                    renderSingleStyle: {
                        type: Cluster.ClusterRender.DOM,
                        inject: function(feature) {
                            // 返回原始marker
                            return feature.originMarker._content;
                        }
                    }
                });

                // 设置聚合数据
                markerClusterer.setData(points);

                // 添加事件监听
                markerClusterer.on(Cluster.ClusterEvent.CLICK, (e) => {
                     // 只处理聚合点的点击事件，单个点的点击事件由原marker处理
                    if (e.isCluster && e.pointCount > 1) {
                        window.parent.postMessage({ 
                                type: 'showClusterData', 
                                data: e.reduces.devices 
                            }, '*');
                        // 阻止事件冒泡，防止触发地图的点击事件
                        e.domEvent && e.domEvent.stopPropagation();
                    }

                });

                markerClusterer.on(Cluster.ClusterEvent.MOUSE_OVER, (e) => {
                    if (e.isCluster && e.pointCount > 1) {
                        //console.log('聚合点鼠标悬停事件', e);
                        initBox(e.latLng, e.reduces.devices);
                    }
                });

                markerClusterer.on(Cluster.ClusterEvent.MOUSE_OUT, (e) => {
                    // 处理鼠标移出事件
                                // 只处理聚合点的移出事件
                    if (e.isCluster && e.pointCount > 1) {
                        //console.log('聚合点鼠标移出事件', e);
                        if(boxList.length > 0){
                            boxList.forEach(marker => {
                                map.removeOverlay(marker);
                            });
                            boxList = [];
                        }
                    }
                });

                markerClusterer.on(Cluster.ClusterEvent.CHANGE, (e) => {
                    //console.log('聚合变化事件', e);
                    // 处理聚合状态变化事件
                    console.log('聚合后团簇',e);
                    if(boxList.length > 0){
                            boxList.forEach(marker => {
                                map.removeOverlay(marker);
                            });
                            boxList = [];
                        }
                });

                return markerClusterer;
            }
            // 添加弹框 
            function AddBox(data) {
                var div = this._div = document.createElement("div");
                div.id = "BoxList_" + data[0].deviceId
                div.style.width = "0px";
                div.style.height = "0px";
                div.style.left = "17px";
                div.style.position = "relative";
                // div.style.display = "flex";
                // div.style.flexDirection = "column";
                // div.style.alignItems = "center";
                normal = 0
                alarm = 0
                exit = 0
                if (data.length > 0) {
                    data.forEach(item => {
                        if (item.onlineState === 1) {
                            normal++
                            if (item.alarmState === 1) {
                                alarm++
                            }
                        } else if (item.onlineState === 0) {
                            exit++
                        }
                    })
                }
                //添加详情框
                var htmlStr = '';
                htmlStr += `<div id="pointNum_${data[0].deviceId}" class="boxDetail" style="position: absolute; left: 0; transform: translateX(-50%) translateY(40px)">`;
                htmlStr += '<div style="color: #80FFFF;">在线：' + normal + '个</div><div style="color: #FFFFFF;">离线：' + exit + '个</div><div style="color: #FF451E;">告警：' + alarm + '个</div>'
                htmlStr += '<div x-arrow="" class="borrow3" ></div>';
                // htmlStr += '<div x-arrow="" class="borrow2" ></div></div>';
                div.innerHTML = htmlStr;
                return div
            }
            // 初始化设备框
            function initBox(point,clusterList) {
                // 清理之前的标记
                if (boxList) {
                    boxList.forEach(marker => {
                        map.removeOverlay(marker); // 使用百度地图的 removeOverlay 方法
                    });
                }
                const listContent = AddBox(clusterList);
                class deviceBoxList extends BMapGL.Overlay {
                        constructor(point, content) {
                            super();
                            this._point = point;
                            this._content = content;
                        }

                        initialize(map) {
                            this._map = map;
                            this._div = this._content;
                            map.getPanes().markerPane.appendChild(this._div);
                            return this._div;
                        }

                        draw() {
                            const position = this._map.pointToOverlayPixel(this._point);
                            this._div.style.left = position.x + 'px';
                            this._div.style.top = position.y - 20 + 'px';
                            this._div.style.position = 'absolute';
                        }
                    }

                     let marker = new deviceBoxList(new BMapGL.Point(point[0], point[1]), listContent);
                      // 将标记添加到地图
                    map.addOverlay(marker);
                    
                    // 将标记存储到 boxList 中
                    boxList = [marker];
                    
                    // 清理 deviceBox
                    deviceBox = null;
            }
            // 更新地图标记 
            // 百度地图API 可用
            function updateMap() {
                for (let i = 0; i < markerList.length; i++) {
                    map.removeOverlay(markerList[i]);
                }
                markerList = [];
            }
            //不同区域显示不同底图颜色
            var curArea = { text: null, content: null };
            var curRoad = null;

            //添加面
            function addArea(area) {
                // 移除已有的区域和文本
                if (curArea.text) {
                    map.removeOverlay(curArea.text);
                }
                if (curArea.content) {
                    map.removeOverlay(curArea.content);
                }

                // 如果有中心点，添加区域名称
                if (area.markerCenterPoint) {
                    AddAreaName(area);
                }

                // 创建多边形区域
                /*  var points = JSON.parse(area.point).map(ele => {
                     return new BMapGL.Point(ele[0], ele[1]);
                 }); */
                // 在创建点时使用 进行了坐标转换
                var points = JSON.parse(area.point).map(ele => {
                    var bdCoord = gcj02ToBd09(ele[0], ele[1]);
                    return new BMapGL.Point(bdCoord[0], bdCoord[1]);
                });

                curArea.content = new BMapGL.Polygon(points, {
                    strokeColor: '#FF86FF',    // 边框颜色
                    strokeWeight: 2,           // 边框宽度
                    strokeOpacity: 0,          // 边框透明度
                    strokeStyle: 'solid',      // 边框样式
                    fillColor: '#3FB3C8',      // 填充颜色
                    fillOpacity: 0.65          // 填充透明度
                });

                // 添加多边形到地图
                map.addOverlay(curArea.content);

                // 调整视图以适应多边形
                var viewport = map.getViewport(points);
                map.flyTo(viewport.center, viewport.zoom);
            }

            // 添加区域名称
            function AddAreaName(data) {
                var centerPoint = JSON.parse(data.markerCenterPoint);
                var point = new BMapGL.Point(centerPoint[0], centerPoint[1]);

                // 创建文本标注
                var label = new BMapGL.Label(data.markerName || data.deviceName, {
                    position: point,
                    offset: new BMapGL.Size(-40, 0)
                });

                // 设置样式
                label.setStyle({
                    color: '#F7FF00',
                    fontSize: '0.8vw',
                    border: 'none',
                    backgroundColor: 'transparent',
                    textAlign: 'center',
                    width: '100px',
                    height: '40px',
                    lineHeight: '40px',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center'
                });

                curArea.text = label;
                map.addOverlay(label);
            };
            // 点击地图事件
            map.addEventListener("click", function (e) {
                if (fromView === 1) {
                    // 发送点击位置消息到父页面
                    window.parent.postMessage({ type: 'hiddenSearchDevice' }, '*');
                }

                // 添加标记
                if (window.setPoint == 1 || fromView == 'test') {
                    updateMap();
                    var marker = addMarker(e.latlng.lng, e.latlng.lat, null, 0, null);

                    // 调用父页面方法(传值回去)
                    var data = {
                        type: "getPoint",
                        longitude: e.latlng.lng,
                        latitude: e.latlng.lat,
                        alt: 0
                    };
                    window.parent.postMessage(data, "*");
                }
            });

            // 地图加载完成事件
            map.addEventListener("tilesloaded", function () {
                // 告知父页面地图已加载完成
                window.parent.postMessage({ type: 'loadOver' }, '*');
            });

            // 监听事件
            var showDeviceName = null
            window.addEventListener('message', function (e) {
                var type = e.data.type
                console.log(e.data);
                // console.log(type)
                // 修改数据还原记录坐标点事件  
                // 已替换百度地图API  暂未发现应用文件
                if (type === 'editPoint') {
                   //使用百度地图API 进行改写 
                   // 清除现有标记
                    updateMap();
    
                    // 如果需要坐标转换（从GCJ02到BD09） 创建新的坐标点
                    let convertedPoint = gcj02ToBd09(e.data.longitude, e.data.latitude);
                    let point = new BMapGL.Point(convertedPoint[0], convertedPoint[1]);
                    // 创建新标记并添加到地图
                    var marker = new BMapGL.Marker(point, {
                        offset: new BMapGL.Size(-20, -20),
                        enableDragging: false
                    });
                    
                    // 设置标记的自定义内容  当前无样式的marker就是默认marker
                    //marker.setContent(AddDevice(point, null, -1, 0));
                    
                    // 将标记添加到地图和标记列表
                    markerList.push(marker);
                    map.addOverlay(marker);
                    
                    // 平移地图中心点
                    if (e.data.longitude === 0 && e.data.latitude === 0) {
                        map.flyTo(new BMapGL.Point(center[0], center[1]));
                    } else {
                        map.flyTo(point);
                    }
                    
                }
                // 百度地图API 可用
                if (type === 'reload') {
                    // 初始化地图配置，清理所有图标，将地图重新放置于中心点  
                    updateMap()
                    map.centerAndZoom(centerPoints, zoom);
                    setPoint = 0;
                }
                // 暂未发现应用文件 
                if (type === 'setPoint') {
                    setPoint = 1;
                }
                // 设置显示设备名称
                // 百度地图API 可用
                if (type === 'setShowDeviceName') {
                    showDeviceName = e.data.data;
                }
                // 从大屏进入 初始化地图 显示设备 
                // 百度地图API 可用
                if (type === 'fromView') {
                    // 处理来自大屏的数据  改写后的代码  
                    fromView = 1;
                    updateMap();

                    // 处理设备数据显示
                    var deviceData = e.data.data;
                    var dataList = e.data.dataList;
                    var markers = [];

                    if (e.data.showType === 0) {
                        for (var i = 0; i < deviceData.length; i++) {
                            var sysType = deviceData[i].sysType;
                            var devices = dataList[sysType];

                            if (devices && devices.length > 0) {
                                for (var j = 0; j < devices.length; j++) {
                                    var device = devices[j];
                                    if (device.longitude && device.latitude) {
                                        var marker = addMarker(device.longitude, device.latitude, device, 1, e.data.chooseId);
                                        markers.push(marker);
                                    }
                                }
                            }
                        }

                        // 创建点聚合
                        createMarkerClusterer(markers);

                        // 设置视图以包含所有标记
                        if (e.data.isZoom) {
                            map.setViewport(markers.map(function (marker) {
                                return marker.getPosition();
                            }));
                        }
                    }
                }
                // 百度地图API 可用
                if (type === 'backHome') {
                    /* map.panTo(center);
                    map.setZoom(zoom) */
                    //替换为 百度地图API
                    map.centerAndZoom(centerPoints, zoom);
                }
                // 暂时未进行更改
                if (type === 'selectDevice') {
                    // console.log(e.data.data)
                    // console.log(e.data.lastChooseId)
                    // map.setZoom(zooms[0])
                    /* map.flyTo(createLngLat(e.data.data.longitude, e.data.data.latitude));
                    var lastDiv = document.getElementById('deviceBox_' + e.data.lastChooseId)
                    if (lastDiv !== null && lastDiv !== undefined) {
                        lastDiv.style.background = ""
                    }
                    if (map.getZoom !== 1) {
                        map.setZoom(zooms[1])
                    }
                    setTimeout(function () {
                        var div = document.getElementById('deviceBox_' + e.data.data.deviceId)
                        if (div !== null && div !== undefined) {
                            div.style.background = "url(./img/border.png) round"
                        } else {
                            // 聚合情况下，如果设备被聚合，则自动展开
                            let clusterItem = cluster.V.find(item => (
                                item._amapMarker.originData[0].findIndex(i => (i.deviceId == e.data.data.deviceId)) != -1
                            ))
                            if (clusterItem) {
                                showMiniCluster({
                                    clusterData: clusterItem._amapMarker.originData[0],
                                    marker: {
                                        _style: {
                                            top: clusterItem._amapMarker.posContainer.y + 'px',
                                            left: clusterItem._amapMarker.posContainer.x + 'px'
                                        }
                                    }
                                })
                                setTimeout(() => {
                                    div = document.getElementById('deviceBox_' + e.data.data.deviceId)
                                    if (div !== null && div !== undefined) {
                                        div.style.background = "url(./img/border.png) round"
                                    }
                                }, 500);
                            }
                        }
                    }, 1000)*/
                    //尝试使用百度地图API
                    console.log('选中设备', e.data.data)
                    //0. 将wgs84坐标转换为gcj02坐标
                    let gcjPoint = wgs84togcj02(e.data.data.longitude, e.data.data.latitude);
                    // 1. 将高德坐标转换为百度坐标
                    let  bdPoint = gcj02ToBd09(gcjPoint[0], gcjPoint[1]);
                    let aimPoint = new BMapGL.Point(bdPoint[0], bdPoint[1]);
                    
                    // 2. 平移地图到设备位置
                    map.flyTo(aimPoint);
                    // 3. 移除上一个选中设备的高亮样式
                    let lastDiv = document.getElementById('deviceBox_' + e.data.lastChooseId);
                    if (lastDiv !== null && lastDiv !== undefined) {
                        lastDiv.style.background = "";
                    }
                    
                    // 4. 设置地图缩放级别（避免使用zooms[1]，使用具体数值）
                    if (map.getZoom() < 16) {
                        map.setZoom(16);
                    }
                    // 5. 等待地图动画结束后处理设备高亮
                    setTimeout(function() {
                        let div = document.getElementById('deviceBox_' + e.data.data.deviceId);
                        if (div !== null && div !== undefined) {
                            // 设备可见，直接添加高亮样式
                            div.style.background = "url(./img/border.png) round";
                        } else if (markerClusterer) {
                            // 设备在聚合点中，尝试找到并展开聚合点
                            // 获取所有聚合数据
                            let clusterFeatures = [];
                            markerClusterer.getLeaves().forEach(cluster => {
                                if (cluster.isCluster) {
                                    clusterFeatures.push(cluster);
                                }
                            });
                            console.log('clusterFeatures', clusterFeatures);
                            // 遍历所有聚合点查找包含目标设备的聚合
                            let targetCluster = null;
                            for (let i = 0; i < clusterFeatures.length; i++) {
                                let features = clusterFeatures[i].features || [];
                                for (let j = 0; j < features.length; j++) {
                                    let featureProperties = features[j].properties;
                                    let markerData = featureProperties.data || 
                                                    (featureProperties.originMarker && featureProperties.originMarker._content && 
                                                    featureProperties.originMarker._content.deviceData);
                                    
                                    if (markerData && markerData.deviceId == e.data.data.deviceId) {
                                        targetCluster = clusterFeatures[i];
                                        break;
                                    }
                                }
                                if (targetCluster) break;
                            }
                            
                            // 如果找到包含目标设备的聚合点，模拟点击该聚合点或手动展开
                            if (targetCluster) {
                                console.log('找到设备所在的聚合点:', targetCluster);
                                
                                // 方法1：放大地图查看详细设备
                                if (map.getZoom() < map.getMaxZoom()) {
                                    map.centerAndZoom(new BMapGL.Point(targetCluster.latLng[0], targetCluster.latLng[1]), map.getZoom() + 1);
                                    
                                    // 再次尝试找到设备并高亮
                                    setTimeout(() => {
                                        div = document.getElementById('deviceBox_' + e.data.data.deviceId);
                                        if (div !== null && div !== undefined) {
                                            div.style.background = "url(./img/border.png) round";
                                        }
                                    }, 500);
                                }
                                
                            } else {
                                console.log('未找到设备所在的聚合点');
                            }
                        }
                    }, 1000);
                }
                // 重置选择 初始时会加载 暂时不需要更改 百度地图API 可用
                if (type === 'resetChoose') {
                    let lastDiv = document.getElementById('deviceBox_' + e.data.chooseId)
                    if (lastDiv !== null && lastDiv !== undefined) {
                        lastDiv.style.background = ""
                    }
                }
                // 百度地图API 可用
                if (type === 'closeMap') {
                    map.destroy();
                }
                // 百度地图API 可用
                if (type === 'setAreaColor') {
                    addArea(e.data.area)
                }
                //百度地图API 可用
                if (type === 'clearMap') {
                    //map.clearMap()
                    map.clearOverlays();//替换为百度地图
                    curArea = { text: null, content: null }
                    curRoad = null
                }
                //道路选中
                /* if (type === 'setPolyline') {
                    if (e.data.data.point != null) {
                        curRoad && map.remove(curRoad)
                        var pathInfo = JSON.parse(e.data.data.point)
                        // var pathInfo = [[113.222194,23.083962],[113.222205,23.08379],[113.221878,23.08227]]
                        curRoad = new AMap.Polyline({
                            map: map, //指定目标地图
                            path: pathInfo, //折线的节点坐标数组
                            showDir: true, //是否延路径显示白色方向箭头,默认false(Canvas绘制时有效,建议折线宽度大于6时使用)
                            strokeColor: "#F97A16", //线颜色
                            strokeOpacity: 1, //线透明度
                            strokeWeight: 6, //线宽
                            // strokeStyle: "solid"  //线样式
                        });
                        map.setFitView(curRoad)
                    }
                } */
                //使用百度地图API
                if (type === 'setPolyline') {
                    if (e.data.data.point != null) {
                        // 如果已存在路线，先移除
                        if (curRoad) {
                            map.removeOverlay(curRoad);
                        }

                        // 解析路径点数据
                        var pathInfo = JSON.parse(e.data.data.point);

                        // 将坐标点转换为百度地图点对象
                        var points = pathInfo.map(point => {
                            // 如果需要坐标转换，在这里进行转换
                            var bdPoint = gcj02ToBd09(point[0], point[1]);
                            return new BMapGL.Point(bdPoint[0], bdPoint[1]);
                        });

                        // 创建折线对象
                        curRoad = new BMapGL.Polyline(points, {
                            strokeColor: "#F97A16",     // 线颜色
                            strokeOpacity: 1,           // 线透明度
                            strokeWeight: 6,            // 线宽
                            strokeStyle: 'solid',       // 线样式
                            enableMassClear: true,      // 是否在清除覆盖物时，一起清除
                            enableEditing: false,       // 是否启用线编辑
                            enableClicking: true,       // 是否响应点击事件
                            geodesic: false            // 是否绘制大地线
                        });

                        // 添加折线到地图
                        map.addOverlay(curRoad);

                        // 调整视图以适应折线
                        var viewport = map.getViewport(points);
                        map.flyTo(viewport.center, viewport.zoom);
                    }
                }
                // 供电地图 
                /* if (type === 'showPowerMap') {
                    powerMapLayer && map.remove(powerMapLayer)
                    if (e.data.data == null || e.data.data.length === 0) return
                    let lines = e.data.data.map(i => {
                        let path = JSON.parse(i.pathPoint)
                        let line = new AMap.Polyline({
                            path, //折线的节点坐标数组
                            strokeColor: i.color || "#ebca26", //线颜色
                            strokeOpacity: 1, //线透明度
                            strokeWeight: 2, //线宽
                            cursor: 'pointer',
                        });
                        if (e.data.deviceType === '587') {
                            line.on('click', e => {
                                let text = new AMap.Text({
                                    map,
                                    text: i.lineName,
                                    position: e.lnglat
                                })
                                setTimeout(() => { text.remove() }, 3000)
                            })
                        }
                        return line
                    })
                    powerMapLayer = new AMap.OverlayGroup(lines)
                    map.add(powerMapLayer)
                    map.setFitView(lines)
                } */
                // 环网地图
                /* if (type === 'showTransformerMap') {
                    powerMapLayer && map.remove(powerMapLayer)
                    transformerMapLayer && map.remove(transformerMapLayer)
                    if (!e.data.data) return
                    let lines = e.data.data.lineList.map(i => {
                        let path = JSON.parse(i.pathPoint)
                        let line = new AMap.Polyline({
                            path, //折线的节点坐标数组
                            strokeColor: i.color || "#ebca26", //线颜色
                            strokeOpacity: 1, //线透明度
                            strokeWeight: 2, //线宽
                            strokeStyle: i.style,
                            strokeDasharray: [3, 3]
                        });
                        return line
                    })
                    let points = e.data.data.powerDistributionRoomList.map(i => {
                        let center = JSON.parse(i.pathPoint)[0]
                        let point = new AMap.Circle({
                            center, //折线的节点坐标数组
                            radius: 10,
                            fillOpacity: 1,
                            fillColor: i.color || "#ebca26", //线颜色
                            strokeWeight: 0, //线宽
                        });
                        return point
                    })
                    let markers = e.data.data.powerDistributionRoomList.map(i => {
                        let position = JSON.parse(i.pathPoint)[0]
                        let marker = new AMap.Marker({
                            position,
                            anchor: 'bottom-center',
                            topWhenClick: true,
                            offset: [0, -10],
                            content: `<div  onclick="window.parent.postMessage({ type: 'getTransformerMap', data: ${e.data.showType === 'detail' ? undefined : i.networkId} }, '*')"
                                        style="height:30px;line-height:30px;white-space:nowrap;font-size:16px;
                                        padding:0 7px;background-color:rgba(15,61,103,0.8);
                                        border-radius:6px;border: 2px solid ${i.color};color:${i.color}">
                                        ${i.name.slice(0, 2)}
                                    </div>`
                        });
                        return marker
                    })
                    transformerMapLayer = new AMap.OverlayGroup([...lines, ...points, ...markers])
                    map.add(transformerMapLayer)
                    // map.panTo(center);
                    // map.setZoom(zoom)
                    map.setFitView([...lines], false, [70, 10, 350, 350])
                } */
                // 设备类型列表 在初次加载时会调用  不涉及地图API  当前可以复用
                if (type === 'deviceTypeList') {
                    deviceTypeList = e.data.data
                    const ary = []
                    for (let index = 0; index < deviceTypeList.length; index++) {
                        const element = deviceTypeList[index];
                        ary[parseInt(element.deviceType)] = element
                    }
                    deviceTypeList = ary

                }
            })

            function createLngLat(a, b, type) {
                return type ? wgs84togcj02(a, b) : new AMap.LngLat(...wgs84togcj02(a, b))
            }
            // 坐标转换
            const PI = 3.1415926535897932384626
            const a = 6378245.0
            const ee = 0.00669342162296594323

            //撰写
            // GCJ02坐标转百度坐标
            function gcj02ToBd09(lng, lat) {
            const x_PI = 3.14159265358979324 * 3000.0 / 180.0;
            const z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * x_PI);
            const theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * x_PI);
            const bd_lng = (z * Math.cos(theta) + 0.0065).toFixed(6);
            const bd_lat = (z * Math.sin(theta) + 0.006).toFixed(6);
            return [bd_lng, bd_lat];
            };

            // 百度坐标转GCJ02坐标
           function  bd09ToGcj02(lng, lat) {
            const x_PI = 3.14159265358979324 * 3000.0 / 180.0;
            const x = lng - 0.0065;
            const y = lat - 0.006;
            const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_PI);
            const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_PI);
            const gcj_lng = (z * Math.cos(theta)).toFixed(6);
            const gcj_lat = (z * Math.sin(theta)).toFixed(6);
            return [gcj_lng, gcj_lat];
            };
            /**
                 * WGS84转GCj02
                 * @param lng
                 * @param lat
                 * @returns {*[]}
                 */
            function wgs84togcj02(lng, lat) {
                var wgs_lat = +lat
                var wgs_lng = +lng
                if (out_of_china(lng, wgs_lat)) {
                    return [wgs_lng, wgs_lat]
                } else {
                    var dlat = transformlat(wgs_lng - 105.0, wgs_lat - 35.0)
                    var dlng = transformlng(wgs_lng - 105.0, wgs_lat - 35.0)
                    var radlat = wgs_lat / 180.0 * PI
                    var magic = Math.sin(radlat)
                    magic = 1 - ee * magic * magic
                    var sqrtmagic = Math.sqrt(magic)
                    dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI)
                    dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI)
                    var mglat = wgs_lat + dlat
                    var mglng = wgs_lng + dlng
                    return [mglng, mglat]
                }
            }

            /**
                 * GCJ02 转换为 WGS84
                 * @param lng
                 * @param lat
                 * @returns {*[]}
                 */
            function gcj02towgs84(lng, lat) {
                var gcj_lat = +lat
                var gcj_lng = +lng
                if (out_of_china(gcj_lng, gcj_lat)) {
                    return [gcj_lng, gcj_lat]
                } else {
                    var dlat = transformlat(gcj_lng - 105.0, gcj_lat - 35.0)
                    var dlng = transformlng(gcj_lng - 105.0, gcj_lat - 35.0)
                    var radlat = gcj_lat / 180.0 * PI
                    var magic = Math.sin(radlat)
                    magic = 1 - ee * magic * magic
                    var sqrtmagic = Math.sqrt(magic)
                    dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI)
                    dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI)
                    var mglat = gcj_lat + dlat
                    var mglng = gcj_lng + dlng
                    return [gcj_lng * 2 - mglng, gcj_lat * 2 - mglat]
                }
            }

            function transformlat(lng, lat) {
                lat = +lat
                lng = +lng
                var ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng))
                ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0
                ret += (20.0 * Math.sin(lat * PI) + 40.0 * Math.sin(lat / 3.0 * PI)) * 2.0 / 3.0
                ret += (160.0 * Math.sin(lat / 12.0 * PI) + 320 * Math.sin(lat * PI / 30.0)) * 2.0 / 3.0
                return ret
            }

            function transformlng(lng, lat) {
                lat = +lat
                lng = +lng
                var ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng))
                ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0
                ret += (20.0 * Math.sin(lng * PI) + 40.0 * Math.sin(lng / 3.0 * PI)) * 2.0 / 3.0
                ret += (150.0 * Math.sin(lng / 12.0 * PI) + 300.0 * Math.sin(lng / 30.0 * PI)) * 2.0 / 3.0
                return ret
            }

            /**
                 * 判断是否在国内，不在国内则不做偏移
                 * @param lng
                 * @param lat
                 * @returns {boolean}
                 */
            function out_of_china(lng1, lat1) {
                var lat = +lat1
                var lng = +lng1
                // 纬度3.86~53.55,经度73.66~135.05
                return !(lng > 73.66 && lng < 135.05 && lat > 3.86 && lat < 53.55)
            }
        };
    </script>
</body>

</html>