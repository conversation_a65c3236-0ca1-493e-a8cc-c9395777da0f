<template>
  <div ref="myEchart" style="height: 95%;width:95%" />
</template>

<script>
import * as echarts from 'echarts'
import { debounce } from '@/utils'
require('echarts/theme/macarons') // echarts theme

export default {
  props: ['keyModel'],
  data() {
    return {
      chart: null,
      alarmTotalNum: 0,
      data: []
    }
  },
  watch: {
    keyModel: {
      deep: true,
      handler(val) {
        if (val.alarmTotalNum > 0) {
          this.alarmTotalNum = val.alarmTotalNum
        } else {
          this.alarmTotalNum = 0
        }
        if (val.accountList !== null && val.accountList.length > 0) {
          this.data = []
          val.accountList.forEach(element => {
            this.data.push({
              name: element.deviceTypeName,
              value: element.deviceNum
            })
          })
          this.setOptions(this.alarmTotalNum, this.data)
        } else {
          this.data = []
          this.setOptions(this.alarmTotalNum, [])
        }
      }
    }
  },
  mounted() {
    this.loadData()
    this.__resizeHanlder = debounce(() => {
      if (this.chart) {
        this.chart.resize()
      }
    }, 100)
    window.addEventListener('resize', this.__resizeHanlder)
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }

    this.chart.dispose()
    this.chart = null
  },
  methods: {
    getAutoSize(val) {
      // 当前视口宽度
      var nowClientHeight = document.documentElement.clientHeight
      var initHeight = 1080
      return val * (nowClientHeight / initHeight)
    },
    setOptions(alarmTotalNum, data) {
      this.chart.setOption({
        color: ['#76DAF9', '#76AFF9', '#8876F9', '#EEFAA4'],
        title: [{
          text: '{name|' + '总告警' + '}\n{val|' + alarmTotalNum + '}',
          top: '36%',
          left: 'center',
          textStyle: {
            rich: {
              name: {
                fontSize: this.getAutoSize(12),
                fontWeight: 'normal',
                color: '#ffffff',
                padding: [5, 0]
              },
              val: {
                fontSize: this.getAutoSize(12),
                fontWeight: 'bold',
                color: '#ffffff'
              }
            }
          }
        }],
        tooltip: {
          position: ['20%', '40%'],
          trigger: 'item',
          formatter: '{b} : {c}个 ({d}%)',
          textStyle: {
            fontSize: this.getAutoSize(12)
          }
        },
        grid: {
          bottom: '1%',
          left: 10,
          right: '10%'
        },
        series: [
          // 主要展示层的
          {
            radius: ['40%', '60%'],
            center: ['50%', '50%'],
            type: 'pie',
            label: {
              normal: {
                show: true,
                formatter: params => {
                  return (
                    '{name|' + params.name + '}:{value|' +
                    params.value + '个' + '}'
                  )
                },
                rich: {
                  name: {
                    fontSize: this.getAutoSize(12)
                    // padding: [0, 10, 10, 4],
                  },
                  value: {
                    fontSize: this.getAutoSize(12)
                    // color: '#A6A6A6',
                    // padding: [0, 10, 5, 4],
                  }
                },
                textStyle: {
                  fontSize: this.getAutoSize(12)
                },
                position: 'outside'
              },
              emphasis: {
                show: true
              }
            },
            labelLine: {
              normal: {
                show: true,
                length: this.getAutoSize(15),
                length2: this.getAutoSize(15)
              },
              emphasis: {
                show: true
              }
            },
            data: data
          },
          // 边框的设置
          {
            radius: ['30%', '34%'],
            center: ['50%', '50%'],
            type: 'pie',
            label: {
              normal: {
                show: false
              },
              emphasis: {
                show: false
              }
            },
            labelLine: {
              normal: {
                show: false
              },
              emphasis: {
                show: false
              }
            },
            animation: false,
            tooltip: {
              show: false
            }
          }, {
            name: '外边框',
            type: 'pie',
            clockWise: false, // 顺时加载
            hoverAnimation: false, // 鼠标移入变大
            center: ['50%', '50%'],
            radius: ['75%', '75%'],
            label: {
              normal: {
                show: false
              }
            },
            data: [{
              value: alarmTotalNum,
              name: '总告警',
              itemStyle: {
                normal: {
                  borderWidth: this.getAutoSize(2),
                  borderColor: '#0b5263'
                }
              }
            }]
          }
        ]
      })
    },
    initChart() {
      this.chart = echarts.init(this.$refs.myEchart, 'macarons')
      this.setOptions(this.alarmTotalNum, this.data)
    },
    loadData() {
      this.initChart()
    }
  }
}
</script>
