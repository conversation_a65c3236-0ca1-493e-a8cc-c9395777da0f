import Layout from '@/layout'
export default [

  {
    name: '流程办理',
    path: '/flow/todo/:procInstId/:taskId',
    component: Layout,
    redirect: '/flow/todo/:procInstId/:taskId',
    children: [{
      name: 'ToDoPage',
      path: '/',
      component: () => import('@/views/otherResource/soc/flow/do/todo')
    }],
    hidden: true
  },
  {
    name: '流程已办查看',
    path: '/flow/done/:procInstId',
    component: Layout,
    redirect: '/flow/done/:procInstId',
    children: [{
      name: 'DonePage',
      path: '/',
      component: () => import('@/views/otherResource/soc/flow/do/done')
    }],
    hidden: true
  },
  {
    name: '流程查看',
    path: '/flow/view/:procInstId',
    component: Layout,
    redirect: '/flow/view/:procInstId',
    children: [{
      name: 'ViewPage',
      path: '/',
      component: () => import('@/views/otherResource/soc/flow/do/view')
    }],
    hidden: true
  },
  {
    name: '流程抄送阅办',
    path: '/flow/cc/todo/:procInstId/:actExtCcId',
    component: () => import('@/views/otherResource/soc/flow/do/cc-todo'),
    hidden: true
  },
  {
    name: '流程抄送查看',
    path: '/flow/cc/done/:procInstId/:actExtCcId',
    component: () => import('@/views/otherResource/soc/flow/do/cc-done'),
    hidden: true
  },
  {
    name: '流程打印',
    path: '/flow/print/:procInstId',
    component: () => import('@/views/otherResource/soc/flow/do/print'),
    hidden: true
  },
  {
    name: '流程打印',
    path: '/flow/print/:procInstId',
    component: () => import('@/views/otherResource/soc/flow/do/print'),
    hidden: true
  },
  {
    name: '流程编辑',
    path: '/act-de-model/edit/:actDeModelId',
    component: () => import('@/views/otherResource/soc/flow/act-de-model/tab'),
    hidden: true
  },
  {
    name: '新建资源维修工单',
    path: '/instance/repair-proc/proc-create/:modelKey',
    component: () => import('@/views/otherResource/soc/instance/repair-proc/create'),
    hidden: true
  },
  {
    name: '流程配置',
    path: '/flow/create/:modelKey',
    component: () => import('@/views/otherResource/soc/flow/do/create'),
    hidden: true
  },
  {
    name: '新建流程',
    path: '/flow/create/:modelKey',
    component: () => import('@/views/otherResource/soc/flow/do/create'),
    hidden: true
  },
  {
    name: '新建子流程',
    path: '/flow/create/:modelKey/:superProcessInstanceId',
    component: () => import('@/views/otherResource/soc/flow/do/create'),
    hidden: true
  },
  {
    name: '创建子流程列表',
    path: '/flow/create-child-proc/:superProcessInstanceId',
    component: () => import('@/views/otherResource/soc/flow/do/child-proc/create-list'),
    hidden: true
  }
]
