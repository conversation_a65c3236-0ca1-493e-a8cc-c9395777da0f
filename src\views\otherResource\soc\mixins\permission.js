import { mapGetters } from 'vuex'

/**
 * 权限检查mixin
 * 提供按钮级别的权限控制功能
 */
export const permission = {
  computed: {
    ...mapGetters(['hasButtonPermission', 'hasMenuPermission', 'funcCodes'])
  },
  methods: {
    /**
     * 检查按钮权限
     * @param {string|number|Array} buttonCode 按钮权限码
     * @returns {boolean} 是否有权限
     */
    checkButtonPermission(buttonCode) {
      return this.hasButtonPermission(buttonCode)
    },

    /**
     * 检查菜单权限
     * @param {string|number} menuId 菜单ID
     * @returns {boolean} 是否有权限
     */
    checkMenuPermission(menuId) {
      return this.hasMenuPermission(menuId)
    },

    /**
     * 过滤有权限的按钮列表
     * @param {Array} buttonList 按钮列表，每个按钮对象应包含permission字段
     * @returns {Array} 过滤后的按钮列表
     */
    filterButtonsByPermission(buttonList) {
      return buttonList.filter(button => {
        if (!button.permission) {
          return true // 没有设置权限则默认显示
        }
        return this.checkButtonPermission(button.permission)
      })
    },

    /**
     * 检查操作栏是否有任何权限
     * @param {Array} permissionCodes 权限码数组
     * @returns {boolean} 是否有任何操作权限
     */
    hasAnyActionPermission(permissionCodes) {
      if (!permissionCodes || permissionCodes.length === 0) {
        return true // 没有设置权限则默认显示
      }
      return permissionCodes.some(code => this.checkButtonPermission(code))
    }
  }
}

export default permission 