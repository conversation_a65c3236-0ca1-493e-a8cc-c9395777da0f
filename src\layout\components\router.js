{
    "path" : "/dashboard",
        "componentType" : "url",
            "component" : "dashboard/admin/index",
                "name" : "14193",
                    "type" : "0",
                        "menuId" : 14193,
                            "menuUpId" : 0,
                                "meta" : {
        "title" : "运管驾驶舱",
            "icon" : "icon-guanwang<PERSON>gwen"
    },
    "children" : null,
        "isShow" : 1
}, {
    "path" : "/cmdb",
        "componentType" : "layout",
            "component" : "",
                "name" : "14231",
                    "type" : "0",
                        "menuId" : 14231,
                            "menuUpId" : 0,
                                "meta" : {
        "title" : "设备基础信息管理",
            "icon" : "zichanguanli2"
    },
    "children" : [{
        "path": "/cmdb/asset",
        "componentType": "layout",
        "component": "otherResource/indexjs",
        "name": "14233",
        "type": "0",
        "menuId": 14233,
        "menuUpId": 14231,
        "meta": {
            "title": "资产信息管理",
            "icon": "cdgl"
        },
        "children": [{
            "path": "szdl/resource-search",
            "componentType": "url",
            "component": "otherResource/cmdb/resource/search/index",
            "name": "14235",
            "type": "0",
            "menuId": 14235,
            "menuUpId": 14233,
            "meta": {
                "title": "资产列表",
                "icon": "sbbq"
            },
            "children": null,
            "isShow": 1
        }, {
            "path": "szdl/subject",
            "componentType": "url",
            "component": "otherResource/cmdb/resource/subject/index",
            "name": "14237",
            "type": "0",
            "menuId": 14237,
            "menuUpId": 14233,
            "meta": {
                "title": "专题管理",
                "icon": "wcgd"
            },
            "children": null,
            "isShow": 1
        }, {
            "path": "szdl/manufacturer",
            "componentType": "url",
            "component": "otherResource/cmdb/resource/manufacturer/index",
            "name": "14239",
            "type": "0",
            "menuId": 14239,
            "menuUpId": 14233,
            "meta": {
                "title": "厂商管理",
                "icon": "ss"
            },
            "children": null,
            "isShow": 1
        }, {
            "path": "szdl/resource-model-attr",
            "componentType": "url",
            "component": "otherResource/cmdb/resource/model-attr-info/index",
            "name": "14241",
            "type": "0",
            "menuId": 14241,
            "menuUpId": 14233,
            "meta": {
                "title": "型号管理",
                "icon": "cdgl"
            },
            "children": null,
            "isShow": 1
        }, {
            "path": "szdl/instance-import",
            "componentType": "url",
            "component": "otherResource/cmdb/resource/instance-import/index",
            "name": "14243",
            "type": "0",
            "menuId": 14243,
            "menuUpId": 14233,
            "meta": {
                "title": "导入记录",
                "icon": "sbbq"
            },
            "children": null,
            "isShow": 1
        }],
        "isShow": 1
    }, {
        "path": "/szdl/zone-base",
        "componentType": "layout",
        "component": "otherResource/indexjs",
        "name": "14245",
        "type": "0",
        "menuId": 14245,
        "menuUpId": 14231,
        "meta": {
            "title": "区域信息管理",
            "icon": "cq"
        },
        "children": [{
            "path": "szdl/zone-list",
            "componentType": "url",
            "component": "otherResource/soc/zone-base/zone-list/index",
            "name": "14247",
            "type": "0",
            "menuId": 14247,
            "menuUpId": 14245,
            "meta": {
                "title": "区域列表",
                "icon": "liebiao"
            },
            "children": null,
            "isShow": 1
        }],
        "isShow": 1
    }, {
        "path": "/szdl/model",
        "componentType": "layout",
        "component": "otherResource/indexjs",
        "name": "14249",
        "type": "0",
        "menuId": 14249,
        "menuUpId": 14231,
        "meta": {
            "title": "资产模型管理",
            "icon": "xtgl02"
        },
        "children": [{
            "path": "szdl/resource-model",
            "componentType": "url",
            "component": "otherResource/cmdb/resource/model/index",
            "name": "14251",
            "type": "0",
            "menuId": 14251,
            "menuUpId": 14249,
            "meta": {
                "title": "资产模型",
                "icon": "fxnhfx"
            },
            "children": null,
            "isShow": 1
        }],
        "isShow": 1
    }, {
        "path": "szdl/resource-report",
        "componentType": "url",
        "component": "otherResource/cmdb/resource/screenReport/index",
        "name": "14253",
        "type": "0",
        "menuId": 14253,
        "menuUpId": 14231,
        "meta": {
            "title": "资产统计管理",
            "icon": "bgyp"
        },
        "children": null,
        "isShow": 1
    }],
        "isShow" : 1
}, {
    "path" : "/equipmentManagement",
        "componentType" : "layout",
            "component" : null,
                "name" : "14227",
                    "type" : "0",
                        "menuId" : 14227,
                            "menuUpId" : 0,
                                "meta" : {
        "title" : "设备状态监测",
            "icon" : "sbgl"
    },
    "children" : [{
        "path": "equipmentQuery",
        "componentType": "url",
        "component": "PMS/equipmentManagement/equipmentQuery/index",
        "name": "14229",
        "type": "0",
        "menuId": 14229,
        "menuUpId": 14227,
        "meta": {
            "title": "设备查询",
            "icon": "sbbq"
        },
        "children": null,
        "isShow": 1
    }],
        "isShow" : 1
}, {
    "path" : "/szdl/flow",
        "componentType" : "layout",
            "component" : "szdl/flow",
                "name" : "14195",
                    "type" : "0",
                        "menuId" : 14195,
                            "menuUpId" : 0,
                                "meta" : {
        "title" : "设备维修工单管理",
            "icon" : "bmgl"
    },
    "children" : [{
        "path": "/alarmManagement",
        "componentType": "layout",
        "component": "PMS/alarmManagement/index",
        "name": "14197",
        "type": "0",
        "menuId": 14197,
        "menuUpId": 14195,
        "meta": {
            "title": "告警管理",
            "icon": "bjgl"
        },
        "children": [{
            "path": "offlineRealtimeAlarm",
            "componentType": "url",
            "component": "PMS/alarmManagement/alarm/index",
            "name": "14219",
            "type": "0",
            "menuId": 14219,
            "menuUpId": 14197,
            "meta": {
                "title": "实时告警",
                "icon": "el-icon-time"
            },
            "children": null,
            "isShow": 1
        }, {
            "path": "offlineHistoryAlarm",
            "componentType": "url",
            "component": "PMS/alarmManagement/alarm/index",
            "name": "14221",
            "type": "0",
            "menuId": 14221,
            "menuUpId": 14197,
            "meta": {
                "title": "历史告警",
                "icon": "fabu"
            },
            "children": null,
            "isShow": 1
        }, {
            "path": "alarmColor",
            "componentType": "url",
            "component": "PMS/alarmManagement/alarmColor/index",
            "name": "14225",
            "type": "0",
            "menuId": 14225,
            "menuUpId": 14197,
            "meta": {
                "title": "告警设置",
                "icon": "cdgl"
            },
            "children": null,
            "isShow": 1
        }],
        "isShow": 1
    }, {
        "path": "/soc/flow-task",
        "componentType": "layout",
        "component": "otherResource/indexjs",
        "name": "14209",
        "type": "0",
        "menuId": 14209,
        "menuUpId": 14195,
        "meta": {
            "title": "工单管理",
            "icon": "cdgl"
        },
        "children": [{
            "path": "szdl/flow-all-list",
            "componentType": "url",
            "component": "otherResource/soc/flow/search/admin-list/index",
            "name": "14211",
            "type": "0",
            "menuId": 14211,
            "menuUpId": 14209,
            "meta": {
                "title": "工单列表",
                "icon": "liebiao"
            },
            "children": null,
            "isShow": 1
        }, {
            "path": "szdl/flow-create",
            "componentType": "url",
            "component": "otherResource/soc/flow/create/index",
            "name": "14213",
            "type": "0",
            "menuId": 14213,
            "menuUpId": 14209,
            "meta": {
                "title": "工单发起",
                "icon": "faqi"
            },
            "children": null,
            "isShow": 1
        }, {
            "path": "szdl/flow-create-list",
            "componentType": "url",
            "component": "otherResource/soc/flow/search/create-list/index",
            "name": "14215",
            "type": "0",
            "menuId": 14215,
            "menuUpId": 14209,
            "meta": {
                "title": "我发起的",
                "icon": "sponsor"
            },
            "children": null,
            "isShow": 1
        }, {
            "path": "szdl/flow-done-list",
            "componentType": "url",
            "component": "otherResource/soc/flow/search/done-list/index",
            "name": "14217",
            "type": "0",
            "menuId": 14217,
            "menuUpId": 14209,
            "meta": {
                "title": "已办工单",
                "icon": "yiban-moren"
            },
            "children": null,
            "isShow": 1
        }, {
            "path": "szdl/rule-config",
            "componentType": "url",
            "component": "otherResource/soc/flow/search/rule-config/index",
            "name": "14263",
            "type": "0",
            "menuId": 14263,
            "menuUpId": 14209,
            "meta": {
                "title": "工单规则配置",
                "icon": "yiban-moren"
            },
            "children": null,
            "isShow": 1
        }],
        "isShow": 1
    }],
        "isShow" : 1
}, {
    "path" : "/system",
        "componentType" : "layout",
            "component" : "",
                "name" : "14255",
                    "type" : "0",
                        "menuId" : 14255,
                            "menuUpId" : 0,
                                "meta" : {
        "title" : "系统管理",
            "icon" : "xitong"
    },
    "children" : [{
        "path": "user",
        "componentType": "url",
        "component": "system/user/index",
        "name": "14257",
        "type": "0",
        "menuId": 14257,
        "menuUpId": 14255,
        "meta": {
            "title": "用户管理",
            "icon": "yhgl(1)"
        },
        "children": null,
        "isShow": 1
    }, {
        "path": "role",
        "componentType": "url",
        "component": "system/role/index",
        "name": "14261",
        "type": "0",
        "menuId": 14261,
        "menuUpId": 14255,
        "meta": {
            "title": "角色管理",
            "icon": "jsgl"
        },
        "children": null,
        "isShow": 1
    }, {
        "path": "menu",
        "componentType": "url",
        "component": "system/menu/index",
        "name": "14259",
        "type": "0",
        "menuId": 14259,
        "menuUpId": 14255,
        "meta": {
            "title": "菜单管理",
            "icon": "cdgl"
        },
        "children": null,
        "isShow": 1
    }],
        "isShow" : 1
}