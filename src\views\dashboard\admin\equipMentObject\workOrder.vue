<template>
    <div class="left-top">
        <div class="tabber">
            <span class="tabber-item" :class="{ 'active': activeButtonIndex === 0 }"
                @click="toggleButton(0)">工单趋势</span>
            <span class="tabber-item" :class="{ 'active': activeButtonIndex === 1 }"
                @click="toggleButton(1)">工单分析</span>
        </div>
        <div v-if="activeButtonIndex === 0" class="container" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
            <div class="right-item">
                <echarts-chart ref="trendChart" :options="getOptions()" />
            </div>
        </div>
        <div v-if="activeButtonIndex === 1" class="container chart">
            <div class="chart-item" style="flex:4.5;">
                <echarts-chart key="echartsChart1" :options="getOptionsGrade()" />
            </div>
            <div class="chart-item" style="flex:5.5;">
                <echarts-chart key="echartsChart2" :options="getOptionsType()" />
            </div>
        </div>
    </div>
</template>

<script>
import moment from 'moment'
import echartsChart from './echartsChart.vue'
import { getOrderLevelAnalysis, getOrderStatusAnalysis } from '@/api/main/admin'
import { getWorkflowChartData } from '@/api/otherResource/flow'
import _ from 'lodash'

export default {
    components: {
        echartsChart
    },

    data() {
        return {
            activeButtonIndex: 0,
            lineData: [],
            personNum: {},
            isShowPersonNum: false,
            orderLevel: [],
            orderType: [],
            hoverTimer: null,
            isManualHover: false,  // 是否处于手动悬浮状态
            currentHoverIndex: 0,
            isHovered: false,
            chart: null,
            deviceNameList: [],
            data1: [],
            data2: [],
            data3: [],
            resizeObserver: null, // 添加resizeObserver
        }
    },

    computed: {
        needHoverEffect() {
            return this.lineData && this.lineData.length > 1
        }
    },

    mounted() {
        // 添加窗口resize监听
        window.addEventListener('resize', this.handleResize)
        
        // 延迟初始化ResizeObserver，确保DOM完全渲染
        this.$nextTick(() => {
            this.initResizeObserver()
        })

        // 获取近7天数据
        // this.mockWorkflowData()
        
        // 计算7天前的时间（包含今天，所以是6天前到今天）
        const currentTime = new Date()
        const sevenDaysAgo = new Date(currentTime.getTime() - 6 * 24 * 60 * 60 * 1000)
        
        getWorkflowChartData({
            startTime: moment(sevenDaysAgo).format('YYYY-MM-DD 00:00:00'),
            endTime: moment(currentTime).format('YYYY-MM-DD 23:59:59')
        }).then(({ data }) => {
            if (data.code === 200 || data.code === 0) {
                // 处理数据，确保最多显示近7天，按日期排序
                let processedData = data.data || []
                
                // 按日期排序（从早到晚）
                processedData.sort((a, b) => new Date(a.orderDate) - new Date(b.orderDate))
                
                // 只取最近7天的数据
                if (processedData.length > 7) {
                    processedData = processedData.slice(-7)
                }
                
                this.lineData = processedData
                this.$nextTick(() => {
                    // 使用echarts组件的renderChart方法更新图表
                    if (this.$refs.trendChart) {
                        this.$refs.trendChart.renderChart()
                    }
                    this.startHoverEffect()
                })
            }
        })
        // 等级分析
        getOrderLevelAnalysis().then(({ data }) => {
            if (data.code === 200 || data.code === 0) {
                this.orderLevel = data.data.map(item => {
                    return {
                        name: item.orderLevel,
                        value: item.orderCount,
                        ratioNum: item.ratioNum
                    }
                })
            }
        })
        // 状态分析
        getOrderStatusAnalysis().then(({ data }) => {
            if (data.code === 200 || data.code === 0) {
                this.orderType = data.data.map(item => {
                    return {
                        name: item.orderType,
                        value: item.orderCount,
                        ratioNum: item.ratioNum
                    }
                })
            }
        })
    },

    activated() {
        // 如果组件被keep-alive缓存，重新激活时重新初始化
        this.$nextTick(() => {
            this.initResizeObserver()
            this.handleResize()  // 重新计算尺寸
            this.updateCharts()  // 重新渲染图表
        })
    },

    deactivated() {
        // 组件被缓存时，暂停相关操作
        this.stopHoverEffect()
        if (this.resizeObserver) {
            this.resizeObserver.disconnect()
        }
    },

    beforeDestroy() {
        // 移除resize监听
        window.removeEventListener('resize', this.handleResize)
        
        // 断开ResizeObserver连接
        if (this.resizeObserver) {
            this.resizeObserver.disconnect()
        }

        this.stopHoverEffect()
    },

    methods: {
        handleMouseEnter() {
            this.isManualHover = true
            this.showTooltip(this.currentHoverIndex)
        },

        handleMouseLeave() {
            this.isManualHover = false
            // 从当前索引继续自动循环
            this.startHoverEffect(true)
        },

        showTooltip(dataIndex) {
            if (!this.$refs.trendChart || !this.$refs.trendChart.chartInstance) return
            
            const chart = this.$refs.trendChart.chartInstance
            
            // 清除之前的hover效果
            chart.dispatchAction({
                type: 'hideTip'
            })
            
            // 触发新的hover效果
            chart.dispatchAction({
                type: 'showTip',
                seriesIndex: 0,
                dataIndex: dataIndex
            })
        },

        startHoverEffect(keepCurrentIndex = false) {
            this.stopHoverEffect()
            if (!this.needHoverEffect) return

            const triggerHover = () => {
                if (this.isManualHover) return
                
                this.showTooltip(this.currentHoverIndex)
                // 更新索引到下一个数据点
                this.currentHoverIndex = (this.currentHoverIndex + 1) % this.lineData.length
            }

            // 是否保持当前索引
            if (!keepCurrentIndex) {
                this.currentHoverIndex = 0
            }
            
            // 立即触发第一次hover
            triggerHover()
            
            // 设置定时器循环触发
            this.hoverTimer = setInterval(triggerHover, 2000)
        },

        stopHoverEffect() {
            if (this.hoverTimer) {
                clearInterval(this.hoverTimer)
                this.hoverTimer = null
            }
            if (this.$refs.trendChart && this.$refs.trendChart.chartInstance && !this.isManualHover) {
                this.$refs.trendChart.chartInstance.dispatchAction({
                    type: 'hideTip'
                })
            }
        },

        handleTitle() {
            this.$router.push({
                path: '/soc/flow-task/szdl/flow-all-list'
            })
        },
        getAutoSize(val) {
            // 当前视口宽度
            var nowClientHeight = document.documentElement.clientHeight
            var initHeight = 1080
            return val * (nowClientHeight / initHeight)
        },
        toggleButton(index) {
            this.activeButtonIndex = index
            // 切换后重新渲染图表
            this.$nextTick(() => {
                this.updateCharts()
            })
        },
        getOptions() {
            const labelArr = this.lineData.map(item => item.orderDate.replace(/\d{4}-/, ''))
            const valueArr = this.lineData.map(item => item.dailyAdditions)
            
            const options = {
                title: [
                    {
                        text: '每天新增工单数量',
                        textStyle: {
                            color: '#A1E6F5',
                            fontSize: this.getAutoSize(10),
                            fontWeight: 700
                        },
                        top: '3%',
                        left: '3%'
                    }
                ],
                xAxis: {
                    type: 'category',
                    data: labelArr,
                    // 刻度设置
                    axisTick: {
                        // true意思：图形和刻度居中中间
                        // false意思：图形在刻度之间
                        alignWithLabel: false,
                        // 不显示刻度
                        show: false
                    },
                    // x坐标轴文字标签样式设置
                    axisLabel: {
                        rotate: 45, // 设置倾斜角度
                        color: '#ffffff',
                        fontSize: this.getAutoSize(10), // 设置横坐标轴文字大小
                        interval: 0 // 确保显示所有标签
                    },
                    // x坐标轴颜色设置
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(255, 255, 255, 0.15)'
                            // width:8,  x轴线的粗细
                            // opcity: 0,   如果不想显示x轴线 则改为 0
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '单位(条)',
                    nameTextStyle: {
                        fontSize: this.getAutoSize(10),
                        fontWeight: 400,
                        color: '#FFFFFF'
                    },
                    // 刻度设置
                    axisTick: {
                        // 不显示刻度
                        show: false
                    },
                    // y坐标轴文字标签样式设置
                    axisLabel: {
                        color: '#ffffff',
                        textStyle: {
                            fontSize: this.getAutoSize(10),
                            color: '#fff'
                        }
                    },
                    // y坐标轴颜色设置
                    axisLine: {
                        lineStyle: {
                            color: '#FFFFFF'
                        }
                    },
                    // y轴 分割线的样式
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(255, 255, 255, 0.15)'
                        }
                    }
                },
                // 直角坐标系内绘图网格（区域）
                grid: {
                    top: this.getAutoSize(50),
                    right: '4%',
                    bottom: '4%',
                    left: '5%',
                    //  图表位置紧贴画布边缘是否显示刻度以及label文字 防止坐标轴标签溢出跟grid 区域有关系
                    containLabel: true,
                    // 是否显示直角坐标系网格
                    show: false,
                    // grid 四条边框的颜色
                    borderColor: 'rgba(0, 240, 255, 0.3)'
                },
                series: [{
                    type: 'bar',
                    data: valueArr,
                    barWidth: this.getAutoSize(14),
                    itemStyle: {
                        width: 3, // 设置线宽
                        color: {
                            type: 'linear', // 渐变类型为线性渐变
                            x: 0, // 渐变起始位置 x 坐标
                            y: 0, // 渐变起始位置 y 坐标
                            x2: 0, // 渐变结束位置 x 坐标
                            y2: 1, // 渐变起始位置 x 坐标
                            colorStops: [{
                                offset: 0, // 渐变起始位置，从 0 开始
                                color: '#08FAFC' // 渐变起始颜色
                            }, {
                                offset: 1, // 渐变结束位置，到 1 结束
                                color: '#0151C8' // 渐变结束颜色
                            }]
                        }
                    },
                    showBackground: true,
                    backgroundStyle: {
                        color: 'rgba(36, 78, 140, 0.5)'
                    },
                    emphasis: {
                        itemStyle: {
                            // 鼠标悬浮时的样式
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [{
                                    offset: 0,
                                    color: '#00FFFC' // 悬浮时更亮的颜色
                                }, {
                                    offset: 1,
                                    color: '#0172C8' // 悬浮时更亮的颜色
                                }]
                            }
                        }
                    }
                }],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow',
                        lineStyle: {
                            color: '#15ecf4'
                        }
                    },
                    formatter: (params) => {
                        var dataIndex = params[0].dataIndex
                        var axisValue = params[0].axisValue
                        var value = valueArr[dataIndex]
                        return `<div>时间：${axisValue}</div><div>工单数：${value}</div>`
                    },
                    backgroundColor: 'rgba(36, 112, 183, 0.8)',
                    borderColor: '#01C2FF',
                    textStyle: { 
                        color: '#fff', 
                        fontSize: this.getAutoSize(12)
                    }
                },
                animation: true,
                animationDuration: 1000,
                animationEasing: 'cubicInOut',
                animationThreshold: 2000
            }
            
            // 在初始化图表后绑定事件
            this.$nextTick(() => {
                if (this.$refs.trendChart && this.$refs.trendChart.chartInstance) {
                    const chart = this.$refs.trendChart.chartInstance
                    
                    chart.off('mouseover').on('mouseover', params => {
                        if (params.componentType === 'series') {
                            this.isManualHover = true
                            this.currentHoverIndex = params.dataIndex
                            this.showTooltip(params.dataIndex)
                        }
                    })

                    chart.off('mouseout').on('mouseout', () => {
                        this.isManualHover = false
                        // 从当前索引继续自动循环
                        this.startHoverEffect(true)
                    })
                }
            })
            
            return options
        },
        getOptionsGrade() {
            return {
                title: {
                    text: '工单等级',
                    left: 'center',
                    top: '46%',
                    textStyle: {
                        fontSize: this.getAutoSize(8),
                        color: '#fff',
                        fontweight: 'normal'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b} : {c} ({d}%)',
                    backgroundColor: 'rgba(36, 112, 183, 0.8)',
                    borderColor: '#01C2FF',
                    textStyle: { color: '#fff', fontSize: this.getAutoSize(12) },
                    confine: true,
                    position(point, params, dom, rect, size) {
                        const x = point[0]
                        const y = point[1]
                        const viewWidth = size.viewSize[0]
                        const viewHeight = size.viewSize[1]
                        const boxWidth = size.contentSize[0]
                        const boxHeight = size.contentSize[1]
                        let posX = x
                        let posY = y

                        if (posX + boxWidth > viewWidth) {
                            posX = x - boxWidth
                        }
                        if (posY + boxHeight > viewHeight) {
                            posY = y - boxHeight
                        }

                        return [posX, posY]
                    }
                },
                series: [
                    {
                        name: '工单等级',
                        type: 'pie',
                        center: ['50%', '50%'],
                        radius: ['20%', '35%'],
                        label: {
                            normal: {
                                show: true,
                                formatter: '{d}%\n{b}',
                                color: '#ffffff',
                                fontSize: this.getAutoSize(10),
                                lineHeight: this.getAutoSize(12)
                            }
                        },
                        data: this.orderLevel
                    }
                ]
            }
        },
        getOptionsType() {
            return {
                title: {
                    text: '工单状态',
                    left: 'center',
                    top: '46%',
                    textStyle: {
                        fontSize: this.getAutoSize(8),
                        color: '#fff',
                        fontweight: 'normal'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b} : {c} ({d}%)',
                    backgroundColor: 'rgba(36, 112, 183, 0.8)',
                    borderColor: '#01C2FF',
                    textStyle: { color: '#fff', fontSize: this.getAutoSize(12) },
                    confine: true,
                    position(point, params, dom, rect, size) {
                        const x = point[0]
                        const y = point[1]
                        const viewWidth = size.viewSize[0]
                        const viewHeight = size.viewSize[1]
                        const boxWidth = size.contentSize[0]
                        const boxHeight = size.contentSize[1]
                        let posX = x
                        let posY = y

                        if (posX + boxWidth > viewWidth) {
                            posX = x - boxWidth
                        }
                        if (posY + boxHeight > viewHeight) {
                            posY = y - boxHeight
                        }

                        return [posX, posY]
                    }
                },
                series: [
                    {
                        name: '工单状态',
                        type: 'pie',
                        center: ['50%', '50%'],
                        radius: ['20%', '35%'],
                        label: {
                            normal: {
                                show: true,
                                formatter: '{d}%\n{b}',
                                color: '#ffffff',
                                fontSize: this.getAutoSize(10),
                                lineHeight: this.getAutoSize(12)
                            }
                        },
                        data: this.orderType
                    }
                ]
            }
        },
        // 添加mock数据生成函数
        mockWorkflowData() {
            const today = moment()
            const mockData = []
            
            // 生成近7天的数据（包含今天）
            for (let i = 6; i >= 0; i--) {
                mockData.push({
                    orderDate: moment(today).subtract(i, 'days').format('YYYY-MM-DD'),
                    dailyAdditions: Math.floor(Math.random() * 10) + 1 // 随机生成1-10的工单数
                })
            }

            // 更新数据
            this.lineData = mockData
            
            // 确保DOM更新后再渲染图表
            this.$nextTick(() => {
                this.updateCharts()
                this.startHoverEffect()
            })
        },
        // 处理窗口resize事件
        handleResize() {
            this.debounceResize()
        },

        // 处理容器大小变化
        handleContainerResize() {
            this.debounceResize()
        },

        // 防抖处理resize
        debounceResize: _.debounce(function() {
            // 检查组件是否仍然挂载且处于激活状态
            if (this._isDestroyed || !this.$el) {
                return
            }
            
            if (this.$refs.trendChart && this.$refs.trendChart.chartInstance) {
                try {
                    this.$refs.trendChart.chartInstance.resize()
                    // 重新渲染图表
                    this.updateCharts()
                } catch (error) {
                    console.warn('图表resize失败:', error)
                    // 如果resize失败，尝试重新初始化ResizeObserver
                    this.initResizeObserver()
                }
            }
        }, 200), // 200ms的防抖时间

        // 更新所有图表
        updateCharts() {
            if (this.activeButtonIndex === 0) {
                // 更新趋势图
                if (this.$refs.trendChart) {
                    this.$refs.trendChart.renderChart()
                }
            } else {
                // 更新饼图
                this.$nextTick(() => {
                    const charts = this.$refs.trendChart
                    if (charts) {
                        charts.renderChart()
                    }
                })
            }
        },

        // 初始化ResizeObserver
        initResizeObserver() {
            try {
                // 确保旧的observer已经断开连接
                if (this.resizeObserver) {
                    this.resizeObserver.disconnect()
                }
                
                // 创建新的ResizeObserver
                this.resizeObserver = new ResizeObserver(this.handleContainerResize)
                
                // 监听图表容器的大小变化
                if (this.$refs.trendChart && this.$refs.trendChart.$el) {
                    this.resizeObserver.observe(this.$refs.trendChart.$el)
                } else {
                    // 如果图表还未渲染完成，延迟一点再试
                    setTimeout(() => {
                        if (this.$refs.trendChart && this.$refs.trendChart.$el && this.resizeObserver) {
                            this.resizeObserver.observe(this.$refs.trendChart.$el)
                        }
                    }, 100)
                }
            } catch (error) {
                console.warn('ResizeObserver初始化失败:', error)
            }
        },
    }
}

</script>

<style lang="scss">
.left-top {
    width: 100%;
    height: 100%;

    .tabber {
        position: absolute;
        top: 6vh;  // 65/1080 * 100 ≈ 6vh
        right: 0.78vh;  // 15/1920 * 100 转换为vh ≈ 0.78vh
        z-index: 10;

        .tabber-item {
            width: 4.17vh;  // 80/1920 * 100 转换为vh ≈ 4.17vh
            height: 2.22vh;  // 24/1080 * 100 ≈ 2.22vh
            font-size: 1.11vh;  // 12/1080 * 100 ≈ 1.11vh
            color: #fff;
            padding: 0.37vh 0.625vh;  // 4/1080*100 ≈ 0.37vh, 12/1920*100 转为vh ≈ 0.625vh
            background: linear-gradient(0deg, #004C76, #004C76), linear-gradient(0deg, #26A2E8, #26A2E8);
            cursor: pointer;
        }

        .tabber-item.active {
            color: #fff;
            border-image-source: linear-gradient(180deg, #FFF08A 0%, rgba(255, 240, 138, 0) 54.17%), linear-gradient(0deg, #26A2E8, #26A2E8);
            box-shadow: 0px -4px 4px 0px #20F3FF inset;
            background: linear-gradient(0deg, #0477EC, #0477EC), linear-gradient(0deg, #26A2E8, #26A2E8), linear-gradient(180deg, #FFF08A 0%, rgba(255, 240, 138, 0) 54.17%);
        }
    }

    .container {
        .left-item {
            padding: 1.11vh;  // 12/1080 * 100 ≈ 1.11vh
            display: flex;
            flex-direction: column;
        }

        .right-item {
            width: 100%;
            height: 21.3vh;  // 230/1080 * 100 ≈ 21.3vh
        }
    }

    .container.chart {
        width: 100%;
        display: flex;
    }

    .chart .chart-item {
        flex: 1;
        height: 21.3vh;  // 230/1080 * 100 ≈ 21.3vh
    }
}

.right-item {
    transition: all 0.3s ease;
    height: 100%;
}

/* 添加图表容器的过渡效果 */
.container {
    transition: all 0.3s ease;
}

.chart-item {
    transition: all 0.3s ease;
}
</style>
