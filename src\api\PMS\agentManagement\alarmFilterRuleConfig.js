import request from '@/utils/request'
// 根据条件查询规则列表(分页查询)
export function getAlarmRuleList(data) {
  return request({
    url: '/devService/api/alarmRule/list',
    method: 'post',
    data
  })
}
// 新增设备告警规则
export function addAlarmRuleList(data) {
  return request({
    url: '/devService/api/alarmRule/addRule',
    method: 'post',
    data
  })
}
// 根据规则ID(列表)删除告警规则
export function deleteAlarmRule(data) {
  return request({
    url: '/devService/api/alarmRule/delete',
    method: 'DELETE',
    data
  })
}
// 修改设备告警规则
export function updateAlarmRule(data) {
  return request({
    url: '/devService/api/alarmRule/update',
    method: 'PUT',
    data
  })
}
// 根据ruleId查询告警规则详情
export function getAlarmRule(query) {
  return request({
    url: '/devService/api/alarmRule/get/' + query,
    method: 'GET'
  })
}
