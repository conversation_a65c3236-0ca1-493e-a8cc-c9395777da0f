<template>
  <div :class="{'has-logo':showLogo}">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :default-openeds="defaultOpeneds"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        unique-opened
        :active-text-color="variables.menuActiveText"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item-for-choose :route="choosen_router" />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import Logo from './Logo'
import SidebarItemForChoose from './SidebarItemForChoose'
import variables from '@/styles/variables.scss'

export default {
  components: { Logo, SidebarItemForChoose },
  data() {
    return {
      defaultOpeneds: []
    }
  },
  computed: {
    ...mapGetters([
      'sidebar'
    ]),
    ...mapState({
      permission_routers: state => state.permission.routers
    }),
    ...mapState({
      choosen_router: state => state.permission.choosenRouters
    }),
    // routes() {
    //   return this.$router.options.routes
    // },
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      // return !this.sidebar.opened
      return false
    }
  },
  created() {
    const flag = this.$route.name === 'ToDoPage' || this.$route.name === 'NewWorkPage'
    if (flag) this.defaultOpeneds = ['/soc/flow-task']
  }
}
</script>
