<!--
 * @Descripttion: 
 * @Author: wangl
 * @version: 
 * @Date: 2025-03-04 16:53:50
 * @LastEditors: wangl
 * @LastEditTime: 2025-03-25 14:08:56
-->
<template>
  <div v-loading.fullscreen.lock="true" element-loading-text="拼命加载中" element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)" style="background:unset!important;" class="login-container"></div>
</template>

<script>
export default {
  data() {
    return {
      loading: false
    }
  },
  created() {
    //console.log('newLogin')
    //this.$store.dispatch('IsNewLogin', true).then(() => { }).catch(() => { })
    //window.location.href = process.env.NODE_ENV === 'production' ? window.config.VUE_APP_REDIRECT_OAUTH : 'https://city189.cn:1153/#/login?client_id=XASZDL&redirect_uri=http://localhost:9528/newLogin&response_type=code&state=ejDeIX'
    //window.location.href = process.env.NODE_ENV === 'production' ? window.config.VUE_APP_REDIRECT_OAUTH : 'http://localhost:9528/'
    //this.$router.replace('/dashboard');
    const cnaviToken = this.getParameterByName('CnaviToken');
    if (cnaviToken) {
      this.clientLogin(cnaviToken); // 调用验证函数
    } else {
      window.location.href = 'http://**********:9528/login?CnaviToken=eyJhbGciOiJIUzUxMiJ9.********************************************************************************************************************************.yf00YIRqMC81PZ5fgt8PMBOkxC0S2gCegyRlWh3RT-B_5z9yHYFQgNN9e8y5qDIlVfVg5A03--1h7KBc9drB7A.1288654862045794305'
      //window.location.href = 'http://************:8000/login.html'
      //window.location.href = 'http://localhost:9528/login?CnaviToken=eyJhbGciOiJIUzUxMiJ9.********************************************************************************************************************************.O64y9AnpIiFJkKgHpfE7_MFJP6h8349RDarOb9ynfAzEc8REu4bxMBy0CnliVceHNWLnrLr7L-faOdGxYU45jQ.1288654862045794305'
    }
  },
  methods: {
    getParameterByName(name) {
      // 获取当前 URL
      const url = new URL(window.location.href);
      // 使用 URLSearchParams 获取参数值
      const params = new URLSearchParams(url.search);
      // 返回参数值，如果不存在则返回 null
      return params.has(name) ? params.get(name) : null;
    },
    clientLogin(cnaviToken) {
      this.loading = true
      this.$store.dispatch('LoginByUsername', {
        //验证登录
        midToken: cnaviToken
      }).then(() => {
        console.log('第三方登录')
        this.loading = false
        // 重置菜单
        this.$store.dispatch('ResetRouters', null).then(() => {
          console.log('重置菜单')
          // 刷新界面
          this.$router.replace('/dashboard')
        }).catch(() => { })
        // 重置左侧侧边栏
        this.$store.dispatch('ChooseRouters', null).then(() => { }).catch(() => {
          console.log('重置左侧菜单失败')
        })
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.login-container {
  width: 100%;
  height: 100%;
}
</style>
