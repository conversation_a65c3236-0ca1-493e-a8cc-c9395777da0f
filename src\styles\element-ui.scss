// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0.0926vh;
  }
}

.small-padding {
  .cell {
    padding-left: 0.5556vh;
    padding-right: 0.5556vh;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 0.6482vh 1.8520vh;
    width: 5.5560vh;
  }
}

.status-col {
  .cell {
    padding: 0 0.9260vh;
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  // margin: 0 auto;
}

// modify dialog standard
.el-dialog__header{
  position: relative;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 18.5200vh;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

// current project dialog related settings
/*header title*/
.el-dialog__header {
  background: linear-gradient(180deg, rgba(0, 85, 152, 0.6) 16.33%, rgba(0, 142, 254, 0.6) 100%);
  padding-top: 1.8520vh;
  padding-bottom: 1.8520vh;
  padding-left: 1.8520vh;
  margin-right: 0 !important;
  border-bottom: 1px solid rgba(0, 142, 254, 0.6);
    .el-dialog__title {
      color: #ffffff !important;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    }
}
.el-dialog__body{
  background: linear-gradient(180deg, rgba(4, 104, 192, 1) 0%, rgba(5, 84, 159, 1) 100%);
  padding-top: 1.8520vh;
}
.el-dialog__headerbtn{
  top:1.8520vh;
}
.custom-loading{
  height: calc(100vh - 18.5200vh) !important;
}

// link-type
.link-type,
.link-type:focus {
  color: #409EFF;
  cursor: pointer;
  &:hover {
    color: #266FE8;
  }
}

// 审核时隐藏删除按钮
.audit-upload .el-upload-list__item-actions:hover .el-upload-list__item-delete{
  display: none !important;
}

.el-picker-panel__footer button:first-child {
  display: none
}
