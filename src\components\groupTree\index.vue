<template>
  <div>
    <div v-if="treeType === 'group'" class="group-tree">
      <div class="title">
        {{ title }}
        <svg-icon v-if="!deviceList" class="icon-style" icon-class="changeType" @click.native="changeTree" />
      </div>
      <!-- v-if="deviceList" -->
      <div v-if="showEdit" class="edit">
        <i class="el-icon el-icon-circle-plus-outline" @click="add" />
        <i class="el-icon el-icon-edit" @click="edit" />
        <i class="el-icon el-icon-delete" @click="del" />
      </div>
      <div class="tree">
        <el-tree
          key="1"
          highlight-current
          node-key="groupId"
          :default-expanded-keys="openId"
          :data="groupTree"
          :props="groupProps"
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
        >
          <!-- <div slot-scope="{ node, data }" style="width: 100%">
            <span>
              <el-button type="text" size="mini" class="showname" style="text-align: left;color: #fff;">{{node.label}}</el-button>
              <el-tooltip class="item" effect="dark" :content="node.label" placement="top-start">
                <el-button type="text" size="mini" class="showname" style="text-align: left;">{{node.label}}</el-button>
              </el-tooltip>
            </span>
          </div> -->
        </el-tree>

      </div>
    </div>

    <div v-if="treeType === 'area'" class="group-tree">
      <div class="title">
        区域
        <svg-icon class="icon-style" icon-class="change" @click.native="changeTree" />
      </div>
      <div class="tree">
        <el-tree
          key="2"
          highlight-current
          node-key="areaId"
          :data="areaTree"
          :props="areaProps"
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
        />
      </div>
    </div>

    <el-dialog v-dialogDrag :visible.sync="addVisible" :title="groupTitle" width="450px">
      <!-- :deviceTypeId="deviceTypeId" -->
      <add :type="type" :checked="node" :show-dialog="addVisible" @handCancle="handCancle" />
    </el-dialog>
  </div>
</template>

<script>
import add from './add'
// , fetchDelete
import { lampGroupList, deleteLampGroup } from '@/api/PMS/LightManage/LightEquipment/management'
// import { fetchAreaTree } from '@/api/otherSystem/tenantManage/areaManage'
export default {
  components: {
    add
  },
  props: {
    deviceList: { // 是否是设备列表 是(可以增删改分组) 不是(可以切换为区域)
      type: Boolean,
      default: false
    },
    // 是否可以显示添加、删除修改，原本由 deviceList决定是(可以增删改分组) 不是(可以切换为区域)
    showEdit: { // 是否可以显示添加、删除修改
      type: Boolean,
      default: false
    },
    title: { // 分组名称
      type: String,
      default: '分组'
    },
    groupTreeType: {
      type: String,
      default: ''
    }
    // deviceTypeId: { // 设备类型
    //   type: Number,
    //   required: true
    // }
  },
  data() {
    return {
      groupTitle: '',
      addVisible: false,
      data: [],
      groupTree: [],
      areaTree: [],
      nodeId: 0,
      node: {},
      openId: [],
      type: '',
      treeType: 'group',
      groupProps: {
        value: 'groupId',
        // children: 'childList',
        label: 'groupName'
      },
      areaProps: {
        value: 'groupId',
        // children: 'childAreaList',
        label: 'groupName'
      }
    }
  },
  watch: {
    groupTree(val) {
      if (val) {
        this.$nextTick(() => {
          if (this.groupTree !== null && this.groupTree.length > 0) {
            document.querySelector('.el-tree-node__content').click()
          }
        })
      }
    },
    treeType: {
      handler(val) {
        if (val === 'group') {
          if (this.groupTree !== null && this.groupTree.length > 0) {
            this.$nextTick(() => {
              document.querySelector('.el-tree-node__content').click()
            })
          }
        } else if (val === 'area') {
          if (this.areaTree !== null && this.areaTree.length > 0) {
            this.$nextTick(() => {
              document.querySelector('.el-tree-node__content').click()
            })
          }
        }
      }
    }
  },
  created() {
    this.init()
  },

  methods: {
    init() {
      // deviceTypeId: this.deviceTypeId
      lampGroupList({
        type: this.groupTreeType
      }).then(res => {
        if (res.data.code === 200) {
          if (this.deviceList) {
            const arr = []
            this.groupTree = res.data.data
            if (this.groupTree !== null && this.groupTree.length > 0) {
              arr.push(this.groupTree[0].groupId == null ? -1 : this.groupTree[0].groupId)
            }
            this.openId = arr
          } else {
            if (res.data.data.length > 0) {
              const data = res.data.data[0].childList
              const index = data.findIndex((val, index, arr) => { // 找到未分组下标
                return val.groupId === '2'
              })
              data.splice(index, 1)
              this.groupTree = data
            } else {
              this.groupTree = []
            }
          }
          this.$emit('gerTreeData', this.groupTree)
        }
      })
      // this.getAreaTree()
    },
    getAreaTree() {
      fetchAreaTree({}).then(res => {
        this.areaTree = res.data.data
      })
    },
    changeTree() {
      if (this.treeType === 'group') {
        this.treeType = 'area'
      } else {
        this.treeType = 'group'
      }
    },
    handleNodeClick(data) {
      this.node = data
      this.$emit('getNode', this.node)
    },
    handCancle(val) {
      if (val) {
        this.init()
      }
      this.addVisible = false
    },
    add() {
      this.type = 'add'
      this.groupTitle = '增加分组'
      this.addVisible = true
    },
    del() {
      if (this.node.isSysGroup === 1) {
        this.$message.error('该分组不能删除')
        return
      } else {
        this.$confirm(`此操作将删除${this.node.groupName}分组, 是否继续?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteLampGroup({ groupId: this.node.groupId, isDel: 1 }).then(res => {
            if (res.data.code === 200) {
              this.$notify({
                title: '成功',
                message: '删除成功',
                type: 'success'
              })
            }
            this.init()
          })
        })
      }
    },
    edit() {
      if (this.node.isSysGroup === 1) {
        this.$message.error('该分组不能编辑')
        return
      } else {
        this.groupTitle = '编辑分组'
        this.type = 'edit'
        this.addVisible = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  $mainColor: #579EF8;

  .group-tree {
    .title {
      width: 95%;
      text-align: center;
      height: 4.6300vh;
      line-height: 4.6300vh;
      background-color: $mainColor;
      color: #fff;
      position: relative;
    }

    .tree {
      width: 95%;
      height: 64.8200vh;
      overflow-y: scroll;
    }

    .edit {
      width: 95%;
      height: 3.7040vh;
      background-color: #93C1FB;
      /* border-bottom: 1px solid #ddd; */
      display: flex;
      align-items: center;
      justify-content: space-around;

      .el-icon {
        font-size: 2.1298vh;
        color: #fff;
        cursor: pointer;
      }
    }
  }

  .icon-style {
    position: absolute;
    right: 0.7408vh;
    top: 0.7408vh;
    cursor: pointer;
    width: 1.4816vh;
    height: 1.4816vh;
  }

  .showname {
    width:80%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: block;
  }
  .tree /deep/ .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content{
    background-color: #F5F7FA;
  }
  .tree /deep/ .el-tree-node__content{
    height: 2.7780vh;
  }
</style>
