<template>
  <div class="board-column">
    <div class="board-column-header">
      {{ headerText }}
    </div>
    <div style="width:100%;position:relative;">
      <div class="el-transfer-panel__filter el-input el-input--small el-input--prefix" style="width: calc(100% - 20px);margin:0 auto;position:absolute;top:8px;left:10px">
        <input v-model="searchWord" type="text" autocomplete="off" placeholder="请输入" class="el-input__inner">
        <span class="el-input__prefix"><i class="el-input__icon el-icon-search" /></span>
      </div>
    </div>
    <draggable
      :list="list"
      v-bind="$attrs"
      class="board-column-content"
      :set-data="setData"
    >
      <div v-for="element in list" :key="element.id" class="board-item">
        <span v-if="element.menuName">{{ element.menuName }}</span>
        <span v-else-if="element.meta.title">{{ element.meta.title }}</span>
      </div>
    </draggable>
  </div>
</template>

<script>
import draggable from 'vuedraggable'

export default {
  name: 'DragKanbanDemo',
  components: {
    draggable
  },
  props: {
    headerText: {
      type: String,
      default: 'Header'
    },
    options: {
      type: Object,
      default() {
        return {}
      }
    },
    list: {
      type: Array,
      default() {
        return []
      }
    },
    showDialog: {
      type: Boolean
    }
  },
  data() {
    return {
      searchWord: ''
    }
  },
  watch: {
    searchWord(newWord) {
      this.$emit('search-word', newWord, this.headerText)
    },
    showDialog(n, o) {
      if (n) {
        this.searchWord = ''
      }
    }
  },
  methods: {
    setData(dataTransfer) {
      // to avoid Firefox bug
      // Detail see : https://github.com/RubaXa/Sortable/issues/1012
      dataTransfer.setData('Text', '')
    }
  }
}
</script>
<style lang="scss" scoped>
.board-column {
  min-width: 18.5200vh;
  min-height: 9.2600vh;
  height: auto;
  overflow: hidden;
  background: #f0f0f0;
  border-radius: 0.2778vh;

  .board-column-header {
    height: 4.6300vh;
    line-height: 4.6300vh;
    overflow: hidden;
    padding: 0 1.8520vh;
    text-align: center;
    background: #333;
    color: #fff;
    border-radius: 0.2778vh 0.2778vh 0 0;
  }

  .board-column-content {
    height: 27.7800vh;
    overflow-x:hidden;
    overflow-y:scroll;
    border: 0.9260vh solid transparent;
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    align-items: center;
    margin-top:3.2410vh;

    .board-item {
      cursor: pointer;
      width: 100%;
      // height: 50px;
      margin: 0.4630vh 0;
      background-color: #fff;
      text-align: left;
      line-height: 3.7040vh;
      padding: 0.4630vh 0.9260vh;
      box-sizing: border-box;
      box-shadow: 0vh 0.0926vh 0.2778vh 0 rgba(0, 0, 0, 0.2);
    }
  }
  .board-column-content::-webkit-scrollbar{
      display:none
    }
}
</style>

