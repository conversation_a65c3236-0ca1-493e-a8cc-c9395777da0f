<template>
  <div v-loading="loading" class="app-container calendar-list-container">
    <div class="filter-container">
      <div class="titleDiv">
        {{ title }}
      </div>

      <div class="contentDiv">
        <el-form ref="ruleForm" style=" margin-left: 0px" label-width="9.26vh" class="demo-ruleForm">
          <el-form-item v-for="item in deviceAlarmSettingList" :key="item.priorityName" :label="item.priorityName"
            required>
            <div v-if="isAlarmColor" style="display: flex;">
              <el-color-picker v-model="item.alarmColor" class="alarmColor" @change="colorPickerChange(item)" />
              <el-input v-model="item.alarmColor" style="width:13.89vh" type="input" clearable
                @input="colorPickerChange(item)" />
              <el-button @click="resetColor(item)">恢复默认颜色</el-button>
            </div>
            <div v-else>
              <el-col>
                <el-col :span="12">
                  <el-form-item label="是否自动派单">
                    <el-switch v-model="item.isOrder" active-color="#3C6EFF" inactive-color="#C5C5C5"
                      :active-value="activeColor" :inactive-value="inactiveValue" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="是否告警显示">
                    <el-switch v-model="item.isShow" active-color="#3C6EFF" inactive-color="#C5C5C5"
                      :active-value="activeColor" :inactive-value="inactiveValue" />
                  </el-form-item>
                </el-col>
              </el-col>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer" style="margin-bottom: 1vh">
        <button-ex 
          v-if="checkButtonPermission('324131')"
          btn-text="保 存" 
          @handle="handCreate" 
        />
        <!-- <span style="font-size: 15px; margin-left: 5px; color: #ffffff">(最近一次保存时间)：{{ latestSaveTime }}</span> -->
      </div>
      <div v-if="isAlarmColor">
        <div style="color: white;font-size: 1.5vh;">效果预览：</div>
        <el-row :gutter="10">
          <el-col :span="12">
            <div style="color: white;font-size: 1.5vh;">大屏告警</div>
            <div class="bg1">
              <zyk-table :list="dataList" :list-loading="loading" v-bind="tableConfig" />
            </div>
          </el-col>
          <el-col :span="12">
            <div style="color: white;font-size: 1.5vh;">后台告警</div>
            <div class="backstageDialog">
              <zyk-table :list="dataList" :list-loading="loading" v-bind="tableConfig" />
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>
<script>
import { fetchList } from '@/api/otherSystem/alarmManagement/alarmColor'
import ZykTable from '@/components/table/index.vue'
import permission from '@/views/otherResource/soc/mixins/permission'

export default {
  name: 'AlarmColor',
  components: {
    ZykTable
  },
  mixins: [permission],
  props: {
    title: {
      type: String,
      default: ''
    },
    isAlarmColor: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      latestSaveTime: '',
      deviceAlarmSettingList: [],
      activeColor: 1,
      inactiveValue: 0,
      loading: false,
      dataList: [
        { alarmTime: '2022-01-21 09:58:59', alarmLevelName: '紧急', alarmContent: '发生测试告警，告警阈值：10，恢复阈值：9，当前光感传感器电压值值为：14', color: '#C5402D' },
        { alarmTime: '2022-01-21 10:58:59', alarmLevelName: '严重', alarmContent: '发生测试告警，告警阈值：10，恢复阈值：9，当前光感传感器电压值值为：12', color: '' },
        { alarmTime: '2022-01-21 11:58:59', alarmLevelName: '一般', alarmContent: '发生测试告警，告警阈值：10，恢复阈值：9，当前光感传感器电压值值为：10', color: '' },
        { alarmTime: '2022-01-21 12:58:59', alarmLevelName: '次要', alarmContent: '发生测试告警，告警阈值：10，恢复阈值：9，当前光感传感器电压值值为：08', color: '' }
      ],
      tableConfig: {
        propList: [
          { prop: 'alarmTime', label: '告警时间', minWidth: '120', useTable: false },
          { prop: 'alarmLevelName', label: '告警等级', minWidth: '100', useTable: false },
          { prop: 'alarmContent', label: '告警内容', minWidth: '200', useTable: false, showOverflowTooltip: true }
        ],
        showIndexColumn: false,
        showSelectColumn: false,
        showPagination: false
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      fetchList({}, 'listDeviceAlarmSetting').then((response) => {
        this.latestSaveTime = response.data.data.latestSaveTime
        this.deviceAlarmSettingList = response.data.data.deviceAlarmSettingList
        for (const index in this.deviceAlarmSettingList) {
          this.dataList[index].color = this.deviceAlarmSettingList[index].alarmColor
        }
        this.loading = false
      })
    },
    handCancle() {
      this.getList()
    },
    // 提交保存
    handCreate() {
      this.$confirm('是否保存设备告警规则配置?', '提示')
        .then(() => {
          const obj = this.getPostValue()
          if (this.isAlarmColor && obj.isChooseColor) {
            this.$message.error('请选择颜色！')
            this.getList()
          } else {
            const settingVoListObj = {
              changeDeviceAlarmSettingVoList: obj.changeDeviceAlarmSettingVoList
            }
            fetchList(settingVoListObj, 'updateDeviceAlarmSetting').then(res => {
              this.$message.success('修改成功')
            })
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
          this.getList()
        })
    },
    getPostValue() {
      const returnObj = {
        isChooseColor: false,
        changeDeviceAlarmSettingVoList: []
      }
      for (const key of this.deviceAlarmSettingList) {
        const obj = {}
        if (key.alarmColor === '' || !key.alarmColor) {
          returnObj.isChooseColor = true
        }
        obj['deviceAlarmSettingId'] = key.deviceAlarmSettingId
        obj['alarmColor'] = key.alarmColor
        obj['isOrder'] = key.isOrder
        obj['isShow'] = key.isShow
        returnObj.changeDeviceAlarmSettingVoList.push(obj)
      }
      return returnObj
    },
    // 颜色选择器的颜色发生变化
    colorPickerChange(item) {
      this.dataList[item.deviceAlarmSettingId - 1].color = item.alarmColor
    },
    resetColor(item) {
      let color = ''
      if (item.deviceAlarmSettingId === 1) {
        // 严重警告默认颜色
        color = '#C5402D'
      } else if (item.deviceAlarmSettingId === 2) {
        // 主要警告
        color = '#E49526'
      } else if (item.deviceAlarmSettingId === 3) {
        // 次要警告
        color = '#DBDD28'
      } else if (item.deviceAlarmSettingId === 4) {
        // 警告告警
        color = '#4F62FB'
      }
      this.deviceAlarmSettingList[item.deviceAlarmSettingId - 1].alarmColor = color
      this.colorPickerChange(item)
    }
  }
}
</script>

<style scoped>
.titleDiv {
  font-size: 25px;
  margin-left: 0px;
  color: #ffffff;
}

.contentDiv {
  display: flex;
  justify-content: center;
  align-items: center;
}

/deep/ .el-color-picker--medium .el-color-picker__trigger {
  width: 150px;
}

/deep/ .el-input--medium {
  /* margin-left: 1vw; */
  margin: 0 1vw;
}

/deep/ .bg1 .el-table--medium td {
  padding: 10px 0 !important;
}
</style>
