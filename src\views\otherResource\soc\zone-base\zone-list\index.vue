<template>
  <div class="app-container">
    <div class="deviceSearchDiv">
      <div class="repair-container">
        <el-input
          v-model="listQuery.searchContent"
          clearable
          style="width:21.3vh; margin-bottom:1vh;"
          placeholder="请输入区域/道路名称"
        />
        <el-button
          v-waves
          style="margin-left: 0.56vh"
          type="primary"
          icon="el-icon-search"
          @click="search"
        >
          {{ $t("button.search") }}
        </el-button>
        <el-button
          v-waves
          v-bind="addBtnConfig"
          v-if="checkButtonPermission('322211')"
          @click="creat"
        >新增</el-button>
      </div>
    </div>
    <zyk-table
      ref="table"
      :list="dataList"
      :list-loading="listLoading"
      :page-sizes="[10, 20, 30, 50]"
      v-bind="tableConfig"
      :total="total"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :table-max-height="$store.getters.tableMaxHeight - 80"
      @handleSizeChange="handleSizeChange"
      @handleCurrentChange="handleCurrentChange"
    >
      <template #num="scope">
        <div class="ranking">{{ scope.row.point ? scope.row.point.length : 0 }}</div>
      </template>
      <template #point1="scope">
        <div class="ranking">{{ scope.row.point ? scope.row.point[0] : '-' }}</div>
      </template>
      <template #point2="scope">
        <div class="ranking">{{ scope.row.point ? scope.row.point[1] : '-' }}</div>
      </template>
      <template #point="scope">
        <div class="ranking">
          <el-popover
            placement="top-start"
            title="定位点信息"
            width="400"
            trigger="hover"
          >
            <div style="height: 27.78vh; overflow: auto">
              <p v-for="(e, i) in scope.row.point" :key="i">{{ e }}</p>
            </div>
            <el-button slot="reference" type="text" :disabled="!scope.row.point || scope.row.point.length < 2">展开</el-button>
          </el-popover>
        </div>
      </template>
      <template #handler="scope">
        <div v-if="hasAnyActionPermission(['322211', '322212', '322213'])">
          <el-button
            v-if="checkButtonPermission('322211')"
            type="text"
            size="medium"
            @click="$refs.edit.open(scope.row.markerDataId)"
          >新增</el-button>
          <el-button
            v-if="checkButtonPermission('322212')"
            type="text"
            size="medium"
            @click="$refs.edit.open(scope.row)"
          >编辑</el-button>
          <el-button
            v-if="checkButtonPermission('322213')"
            type="text"
            size="medium"
            @click="del(scope.row)"
          >删除</el-button>
        </div>
      </template>
    </zyk-table>
    <edit ref="edit" @success="get" />
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import ZykTable from '@/components/table/index.vue'
import edit from './edit'
import { listPage } from '@/views/otherResource/soc/mixins'
import { geAreaList } from '@/api/otherResource/cmdb/area'
import permission from '@/views/otherResource/soc/mixins/permission'

export default {
  components: { ZykTable, edit },
  mixins: [listPage, permission],
  data() {
    return {
      total: null,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 10,
        orderBy: null,
        order: null,
        searchContent: ''
      },
      dataList: [],
      tableHeight: null,
      tableConfig: {
        propList: [
          { prop: 'markerName', label: '名称', minWidth: '100', useTable: false, showOverflowTooltip: true },
          { prop: 'markerDataId', label: '编号', minWidth: '100', useTable: false, showOverflowTooltip: true },
          { prop: 'markerCenterPoint', label: '中心点', minWidth: '100', useTable: false, showOverflowTooltip: true },
          { prop: 'num', label: '定位点数量', minWidth: '100', useTable: false, showOverflowTooltip: true, slotName: 'num' },
          { prop: 'point1', label: '定位点1', minWidth: '100', useTable: false, showOverflowTooltip: true, slotName: 'point1' },
          { prop: 'point2', label: '定位点2', minWidth: '100', useTable: false, slotName: 'point2' },
          { prop: 'point', label: '', minWidth: '100', useTable: false, slotName: 'point' },
          { prop: 'handler', label: '操作', minWidth: '100', useTable: false, slotName: 'handler' }
        ],
        showIndexColumn: false,
        showSelectColumn: false,
        treeProps: { children: 'childLayerMessage' },
        rowKey: 'markerDataId'
      }
    }
  },
  computed: {
    ...mapGetters([
      'userId'
    ]),
    tableButtonList() {
      return [
        {
          name: '查看', click: scope => {
            this.openBase('detail', scope.row.id)
            this.addCount(scope.row.id)
          },
          visible: (scope) => true
        },
        {

          name: '编辑', click: scope => this.openBase('edit', scope.row.id),
          visible: (scope) => scope.row.createUserId === this.userId && (scope.row.auditStatus === '未审批' || scope.row.auditStatus === '未通过')
        },
        {
          name: '删除', click: scope => this.del({ ...scope.row }),
          visible: (scope) => scope.row.createUserId === this.userId && (scope.row.auditStatus === '未审批' || scope.row.auditStatus === '未通过')
        }
      ]
    }
  },
  created() {
    this.get()
  },
  methods: {
    get() {
      this.listLoading = true
      geAreaList(this.listQuery).then(res => {
        this.dataList = this.parsePoint(res.data.data)
        this.total = res.data.total
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    search() {
      this.listQuery.page = 1
      this.get()
    },
    parsePoint(ary) {
      return ary.map(ele => {
        ele.point = JSON.parse(ele.point)
        if (ele.childLayerMessage) {
          ele.childLayerMessage = this.parsePoint(ele.childLayerMessage)
        }
        return ele
      })
    },
    creat() {
      this.$refs.edit.open()
    },
    handleSizeChange(val) {
      // 每页显示条目个数
      this.listQuery.limit = val
      this.get()
    },
    handleCurrentChange(val) {
      // 上一页/下一页
      this.listQuery.page = val
      this.get()
    },
    del(item) {
      this.postData({
        url: '/cmdbService/api/layerManagement/delete?markerDataId=' + item.markerDataId,
        successText: '删除成功！', isBefoureNotify: true, befoureNotifyText: '永久删除该数据',
        success: () => this.get()
      })
    },
    wrapText(row, column, cellValue) {
      return cellValue.replace(',', '\n')
    },
    openDetail(row) {
      this.openBase('detail', row.id)
    },
    addCount(id) {
      this.postData({
        url: '/cmdbService/knowledge-base/updateCount',
        params: { id },
        isSuccessNotify: false
      })
    }
  }
}
</script>
