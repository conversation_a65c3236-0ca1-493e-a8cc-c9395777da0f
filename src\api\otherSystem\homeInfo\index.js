import request from '@/utils/request'

export function findLampList(data) {
  return request({
    url: '/pmsdevice/api/homePage/findLampList',
    method: 'post',
    data
  })
}

export function fetchParkingList(data) {
  return request({
    url: '/pmsdevice/api/keyTopParkingDev/carInOutList',
    method: 'post',
    data
  })
}

export function findLampInfo(data) {
  return request({
    url: '/pmsdevice/api/homePage/findLampInfo',
    method: 'post',
    data
  })
}

export function deviceOptionLight(data) {
  return request({
    // baseURL: process.env.OPEN_API,
    url: '/pmsexternal/open/sync/lamp/deviceOptionLight',
    method: 'post',
    data
  })
}

export function openDoorSwitch(data) {
  return request({
    baseURL: process.env.OPEN_API,
    url: '/pmsexternal/open/sync/doorControl/openDoorSwitch',
    method: 'post',
    data
  })
}

export function fetchBuildingList(data) {
  return request({
    url: '/pmsService/api/building/list',
    method: 'post',
    data
  })
}

export function findAccessInfo(data) {
  return request({
    url: '/pmsdevice/api/homePage/findAccessInfo',
    method: 'post',
    data
  })
}

export function doorInOutList(data) {
  return request({
    url: '/pmsexternal/api/doorInOut/list',
    method: 'post',
    data
  })
}

export function parkingRecordList(data) {
  return request({
    url: '/pmsdevice/api/parkingRecord/list',
    method: 'post',
    data
  })
}

export function garageDetailList(data) {
  return request({
    url: '/pmsdevice/api/magentDev/getMagent',
    method: 'post',
    data
  })
}

export function controlDevice(data) {
  return request({
    baseURL: process.env.OPEN_API,
    url: '/pmsexternal/open/sync/hik/controlDevice',
    method: 'post',
    data
  })
}

export function invokeCrossPic(data) {
  return request({
    baseURL: process.env.OPEN_API,
    url: '/pmsdevice/api/sync/hik/invokeCrossPic',
    method: 'post',
    data
  })
}

export function findBuildingVedio(data) {
  return request({
    url: '/pmsdevice/api/homePage/findBuildingVedio',
    method: 'post',
    data
  })
}
export function findWellcoverInfo(data) {
  return request({
    url: '/pmsdevice/api/homePage/findWellcoverInfo',
    method: 'post',
    data
  })
}

export function findReportToWeek(data) {
  return request({
    url: '/pmsdevice/api/homePage/findReportToWeek',
    method: 'post',
    data
  })
}

export function findLampPercent(data) {
  return request({
    url: '/pmsdevice/api/homePage/findLampPercent',
    method: 'post',
    data
  })
}

export function findBaList(data) {
  return request({
    url: '/pmsdevice/api/homePage/findBaList',
    method: 'post',
    data
  })
}

export function staticsFastbox(data) {
  return request({
    url: '/pmsdevice/api/homePage/staticsFastbox',
    method: 'post',
    data
  })
}

export function findFastboxList(data) {
  return request({
    url: '/pmsdevice/api/homePage/findFastboxList',
    method: 'post',
    data
  })
}

export function ctrlSetVolume(data) {
  return request({
    url: '/pmsexternal/open/sync/ExternalApi/radio/ctrlSetVolume',
    method: 'post',
    data
  })
}

export function selectRadioStatus(data) {
  return request({
    url: '/pmsexternal/open/sync/ExternalApi/radio/selectRadioStatus',
    method: 'post',
    data
  })
}

export function dzList(data) {
  return request({
    url: '/pmsexternal/api/garageDetail/dzList',
    method: 'get',
    data
  })
}

export function getHouseTypeCount(data) {
  return request({
    url: '/pmsdevice/api/homePage/getHouseTypeCount',
    method: 'post',
    data
  })
}

export function findWaterMeterByTarget(data) {
  return request({
    url: '/pmsdevice/api/homePage/findWaterMeterByTarget',
    method: 'post',
    data
  })
}

export function findFireHydrantData(data) {
  return request({
    url: '/pmsdevice/api/fireHydrantInfo/sevenDayList',
    method: 'post',
    data
  })
}

export function findYanganData(data) {
  return request({
    url: '/pmsdevice/api/smokeSensation/getByDeviceId',
    method: 'post',
    data
  })
}

export function findMouseData(data) {
  return request({
    url: '/pmsdevice/api/rodentMonitorDev/getRodentMonitor',
    method: 'post',
    data
  })
}

export function findMouseExtendData(data) {
  return request({
    url: '/pmsdevice/api/rodentMonitorDev/getDeviceInfo',
    method: 'post',
    data
  })
}
