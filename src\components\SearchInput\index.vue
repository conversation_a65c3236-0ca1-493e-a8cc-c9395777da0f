<template>
  <div style="display: inline-block;" class="box">
    <slot name="before" />
    <template v-for="(item, index) in inputItem">
      <template v-if="item.type === 'deviceSearch'">
        <device-search
          ref="deviceSearch"
          :key="item.label"
          v-bind="item.deviceSearchConfig"
          :share-device-name="value.shareDeviceName"
          @handleData="handleData"
        />
      </template>
      <template v-else-if="item.type === 'newDeviceSearch'">
        <new-device-search
          ref="deviceSearch"
          :key="item.label"
          v-bind="item.deviceSearchConfig"
          :share-device-name="value.shareDeviceName"
          @handleData="handleData"
        />
      </template>
      <template v-else-if="item.type === 'input'">
        <el-input
          :key="item.label"
          v-model="value[`${item.field}`]"
          style="width: 16.6680vh"
          :type="item.inputType"
          :style="item.style"
          :placeholder="item.placeholder"
          clearable
        />
      </template>
      <template v-else-if="item.type === 'datePicker'">
        <el-date-picker
          :key="item.label"
          v-model="value[`${item.field}`]"
          style="width: 16.6680vh"
          :type="item.dateType"
          :placeholder="item.placeholder"
          :value-format="item.valueFormat"
          :style="item.style"
          @change="getTips"
        />
      </template>
      <template v-else-if="item.type === 'datetime'">
        <span v-if="item.label" :key="`${item.label + index}`" class="s1">{{ item.label }}</span>
        <el-date-picker
          :key="item.label"
          v-model="value[`${item.field}`]"
          style="width: 20.3720vh"
          :type="item.dateType"
          :placeholder="item.placeholder"
          :value-format="item.valueFormat"
          :style="item.style"
          @change="getTips"
        />
      </template>
      <template v-else-if="item.type === 'dateRangePicker'">
        <el-date-picker
          :key="item.label"
          v-model="dates"
          :format="item.format"
          :picker-options="item.options"
          :value-format="item.valueFormat"
          :type="item.dateType"
          :style="item.style"
          :range-separator="item.rangeSeparator"
          :start-placeholder="item.startPlaceholder"
          :end-placeholder="item.endPlaceholder"
          @change="dateRangePickerChange"
        />
      </template>
      <template v-else-if="item.type === 'datetimerange'">
        <el-date-picker
          :key="item.label"
          v-model="dates"
          :type="item.type"
          :value-format="item.valueFormat"
          :style="item.style"
          :picker-options="item.pickerOptions"
          :range-separator="dates && dates.length > 0 ? '至' : ''"
          :start-placeholder="item.label"
          :end-placeholder="item.label"
          @change="dateRangePickerChange"
        />
      </template>
      <template v-else-if="item.type === 'select'">
        <el-select
          :key="item.label"
          v-model="value[`${item.field}`]"
          :style="item.style"
          clearable
          :placeholder="item.placeholder"
          class="form-item-width"
        >
          <el-option
            v-for="optionItem in item.options"
            :key="optionItem[item.selectOption && item.selectOption.value ? item.selectOption.value : 'value']"
            :label="optionItem[item.selectOption && item.selectOption.value ? item.selectOption.label : 'label']"
            :value="optionItem[item.selectOption && item.selectOption.value ? item.selectOption.value : 'value']"
          />
        </el-select>
      </template>
      <template v-else-if="item.type === 'tagSelect'">
        <el-select
          :key="item.label"
          v-model="value[`${item.field}`]"
          :style="item.style"
          clearable
          :placeholder="item.placeholder"
          class="form-item-width"
        >
          <el-option
            v-for="optionItem in item.options"
            :key="optionItem[item.selectOption && item.selectOption.value ? item.selectOption.value : 'value']"
            :label="optionItem[item.selectOption && item.selectOption.value ? item.selectOption.label : 'label']"
            :value="optionItem[item.selectOption && item.selectOption.value ? item.selectOption.value : 'value']"
          />
        </el-select>
      </template>
    </template>
    <slot name="after" />
  </div>
</template>

<script>
import deviceSearch from '@/components/deviceSearch/index'
import newDeviceSearch from '@/components/deviceSearchNew/index'
export default {
  name: 'SearchInput',
  components: {
    deviceSearch,
    newDeviceSearch
  },
  props: {
    value: {
      type: Object,
      required: true
    },
    inputItem: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      dates: '',
      time1: ''
    }
  },
  computed: {

  },
  methods: {
    handleData(data) {
      this.$emit('handleData', data)
      // this.listQuery = Object.assign(this.listQuery, data); // 接收并合并公共组件查询条件
      // console.log(this.listQuery)
    },
    getTips() {
      this.$emit('getTips')
    },
    handleCreate() {
      this.$emit('handleCreate')
    },
    dateRangePickerChange(val) {
      this.$emit('dateRangePickerChange', val)
    },
    clear(query) {
      this.dates = ''
      for (const key in query) {
        switch (key) {
        case 'page':
          query[key] = 1
          break
        case 'limit':
          break
        case 'isSpot':
        case 'order':
        case 'sortBy':
          if (this.$route.path !== '/dashboard') {
            query[key] = ''
          }
          break
        default:
          query[key] = ''
          break
        }
      }
      // 兼容
      this.$refs.deviceSearch[0].clear(query)
    }
  }

}
</script>
<style scoped>
.s1 {
  width: 7.8710vh;
  display: inline-block;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  position: relative;
  vertical-align: middle;
  font-size: 1.2964vh;
  height: 3.3336vh;
  line-height: 3.3336vh;
  color: rgba(0,0,0,0.6);
}
.form-item-width {
  margin-right: 0.5556vh
}
</style>
