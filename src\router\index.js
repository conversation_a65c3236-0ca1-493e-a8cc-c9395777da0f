import Vue from 'vue'
import Router from 'vue-router'
import socRouterExtend from '../router/soc/router-extend'
import cmdbRouterExtend from '../router/cmdb/router-extend'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'
import FullScreenLayout from '@/layout/FullScreenLayout'
/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRouterMap
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRouterMap = [{
  path: '/newLogin',
  component: () => import('@/views/login/index'),
  hidden: true
},
//展示专用登陆跳转页
{
  path: '/showLogin',
  component: () => import('@/views/login/showLogin'),
  hidden: true
},

{
  path: '/login',
  component: () => import('@/views/login/newLogin'),
  hidden: true
},
{
  path: '/404',
  component: () => import('@/views/404'),
  hidden: true
},
/* {
  path: '/dashboard1',
  component: () => import('@/views/dashboard-B/admin/index'),
  hidden: true
}, */
//展示专用首页跳转页
{
  path: '/showDashboard',
  component: () => import('@/views/dashboard-B/admin/index'),
  hidden: true
},
// {
//     path: '/',
//     component: Layout,
//     redirect: '/dashboard',
//     children: [{
//         path: 'dashboard',
//         name: 'Dashboard',
//         component: () => import('@/views/dashboard/index'),
//         meta: { title: '首页', icon: 'dashboard' }
//     }]
// },
{
  path: '/',
  component: FullScreenLayout,
  children: []
},
{
  path: '/welcome',
  component: Layout,
  children: []
},
{
  path: '/back',
  component: Layout,
  hidden: true,
  redirect: { name: 'dashboard' },
  children: [{
    path: 'navigation',
    name: 'dashboard',
    component: () => import(/* webpackChunkName: "dashboardBack" */ '@/views/dashboard/index'),
    meta: { title: '首页', icon: 'dashboard', noCache: true },
    hidden: true
  }]
},
{
  path: '/instanceManagementHcw',
  component: Layout,
  hidden: true,
  children: [{
    path: 'equipmentQuery',
    name: '1804',
    component: () => import(/* webpackChunkName: "dashboardBack" */ '@/views/PMS/equipmentManagement/equipmentQuery/index'),
    meta: { title: '回传网设备查询', icon: 'sbbq', noCache: true },
    hidden: true
  }]
},
{
  path: '/instanceManagementByjd',
  component: Layout,
  hidden: true,
  redirect: { name: 'equipmentQuery' },
  children: [{
    path: 'equipmentQuery',
    name: '1806',
    component: () => import(/* webpackChunkName: "dashboardBack" */ '@/views/PMS/equipmentManagement/equipmentQuery/index'),
    meta: { title: '边缘节点设备查询', icon: 'sbbq', noCache: true },
    hidden: true
  }]
},
{
  path: '/instanceManagementGd',
  component: Layout,
  hidden: true,
  redirect: { name: 'equipmentQuery' },
  children: [{
    path: 'equipmentQuery',
    name: '1808',
    component: () => import(/* webpackChunkName: "dashboardBack" */ '@/views/PMS/equipmentManagement/equipmentQuery/index'),
    meta: { title: '供电设备查询', icon: 'sbbq', noCache: true },
    hidden: true
  }]
},
{
  path: '/instanceManagementJg',
  component: Layout,
  hidden: true,
  redirect: { name: 'equipmentQuery' },
  children: [{
    path: 'equipmentQuery',
    name: '1810',
    component: () => import(/* webpackChunkName: "dashboardBack" */ '@/views/PMS/equipmentManagement/equipmentQuery/index'),
    meta: { title: '交管设备查询', icon: 'sbbq', noCache: true },
    hidden: true
  }]
},
{
  path: '/instanceManagementSpjk',
  component: Layout,
  hidden: true,
  redirect: { name: 'equipmentQuery' },
  children: [{
    path: 'equipmentQuery',
    name: '1812',
    component: () => import(/* webpackChunkName: "dashboardBack" */ '@/views/PMS/equipmentManagement/equipmentQuery/index'),
    meta: { title: '视频监控设备查询', icon: 'sbbq', noCache: true },
    hidden: true
  }]
},
{
  path: '/instanceManagementDl',
  component: Layout,
  hidden: true,
  redirect: { name: 'equipmentQuery' },
  children: [{
    path: 'equipmentQuery',
    name: '1814',
    component: () => import(/* webpackChunkName: "dashboardBack" */ '@/views/PMS/equipmentManagement/equipmentQuery/index'),
    meta: { title: '道路设备查询', icon: 'sbbq', noCache: true },
    hidden: true
  }]
}
  // {
  //     path: '/alarmManagement',
  //     component: Layout,
  //     hidden: true,
  //     children: [{
  //         path: 'realtimeAlarm',
  //         name: 'realtimeAlarm',
  //         component: () =>
  //             import('@/views/PMS/alarmManagement/realtimeAlarm/index'),
  //         meta: { title: '告警实时记录查询', icon: 'dashboard' }
  //     }]
  // }

  // {
  //   path: '/',
  //   component: Layout,
  //   redirect: '/ntko',
  //   children: [{
  //     path: 'ntko',
  //     name: 'Ntko',
  //     component: () => import('@/views/PMS/schoolAffairsAndOA/documentManagement/postManagement/components/Ntko.vue'),
  //     meta: { title: '', icon: 'dashboard' }
  //   }]
  // }

  // 404 page must be placed at the end !!!
  // { path: '*', redirect: '/404', hidden: true }
]
const createRouter = () => new Router({
  mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: [...constantRouterMap, ...socRouterExtend, ...cmdbRouterExtend]
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
