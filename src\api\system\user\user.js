import request from '@/utils/request'
export function fetchList(data) {
  return request({
    url: '/userService/api/person/list',
    method: 'post',
    data
  })
}

export function findShiftUser(data) {
  return request({
    url: '/zhdxService/api/training/findShiftUser',
    method: 'post',
    data
  })
}

export function fetchDetail(data) {
  return request({
    url: '/userService/api/person/detail',
    method: 'post',
    data
  })
}

export function fetchUserDetail(data) {
  return request({
    url: '/userService/api/person/detail',
    method: 'post',
    data
  })
}

export function fetchDelete(userIds) {
  var data = { userIds }
  return request({
    url: '/userService/api/person/delete',
    method: 'post',
    data
  })
}

export function fetchCreate(data) {
  return request({
    url: '/userService/api/person/create',
    method: 'post',
    data
  })
}

export function fetchUpdate(data) {
  return request({
    url: '/userService/api/person/update',
    method: 'post',
    data
  })
}

export function updateUserPassword(userId, userPwd) {
  const data = { userId, userPwd }
  return request({
    url: '/userService/api/person/updateUserPassword',
    method: 'post',
    data
  })
}

export function updateUserState(userId, userState) {
  const data = { userId, userState }
  return request({
    url: '/userService/api/person/updateUserState',
    method: 'post',
    data
  })
}

export function checkAcc(userId, userAcc) {
  const data = { userId, userAcc }
  return request({
    url: '/userService/api/person/checkAcc',
    method: 'post',
    data
  })
}

export function selectList(data) {
  return request({
    url: '/userService/api/person/selectList',
    method: 'post',
    data
  })
}

export function selectListByDept(data) {
  return request({
    url: '/userService/api/person/selectListByDept',
    method: 'post',
    data
  })
}

// 个人信息
export function fetchPersonInfo() {
  return request({
    url: '/userService/api/user/getLoginUser',
    method: 'post'
  })
}

export function teacherAndOffStaffByDept(data) {
  return request({
    url: '/userService/api/person/teacherAndOffStaffByDept',
    method: 'post',
    data
  })
}

export function selectPartyBranchUserTree(data) {
  return request({
    url: '/userService/api/person/selectPartyBranchUserTree',
    method: 'post',
    data
  })
}

export function selectPartBranchUserList(data) {
  return request({
    url: '/userService/api/person/selectPartBranchUserList',
    method: 'post',
    data
  })
}

export function unlockAccount(data) {
  return request({
    url: '/userService/api/person/unlockAccount',
    method: 'post',
    data
  })
}

export function updateAccountPassword(data) {
  return request({
    url: '/userService/api/person/updateAccountPassword',
    method: 'post',
    data
  })
}

export function getUserLastOpTime(data) {
  return request({
    url: '/userService/api/person/getUserLastOpTime',
    method: 'post',
    data
  })
}

export function getUserRealTimeState(data) {
  return request({
    url: '/userService/api/person/getUserRealTimeState',
    method: 'post',
    data
  })
}
