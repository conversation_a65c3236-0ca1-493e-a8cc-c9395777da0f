const getters = {
  sidebar: state => state.app.sidebar,
  language: state => state.app.language,
  device: state => state.app.device,
  tableMaxHeight: state => state.settings.tableMaxHeight,
  // visitedViews: state => state.tagsView.visitedViews,
  // cachedViews: state => state.tagsView.cachedViews,
  token: state => state.user.token,
  avatar: state => state.user.avatar,
  name: state => state.user.name,
  userId: state => state.user.userId,
  introduction: state => state.user.introduction,
  status: state => state.user.status,
  setting: state => state.user.setting,
  permission_routers: state => state.permission.routers,
  firstMenu: state => {
    const manageMenuName = getManageMenu(state.permission.routers.find(item => {
      return item.path !== '/dashboard' && !item.hidden && item.name !== undefined
    }))
    const dashboard = state.permission.routers.find(ele => ele.path === '/dashboard') || {}
    console.log(dashboard, 'dashboard')
    return {
      dashboard: dashboard.name,
      manage: manageMenuName
    }
  },
  choosen_router: state => state.permission.choosenRouters,
  addRouters: state => state.permission.addRouters,
  errorLogs: state => state.errorLog.logs,
  entSerno: state => state.user.entSerno,
  menuTree: state => state.user.menuTree,
  alarmData: state => state.user.alarmData,
  isReal: state => state.user.isReal,
  realFilter: state => state.user.realFilter,
  buildId: state => state.user.buildId,
  isYulan: state => state.user.isYulan,
  set_alarm_data: state => state.user.alarmData,
  encrypt_userId: state => state.user.encryptUserId,
  isNewLogin: state => state.user.isNewLogin,

  roles: state => state.user.roles,
  permissions: state => state.user.permissions,

  // 用户权限码
  funcCodes: state => state.user.funcCodes,
  
  // 检查菜单权限的方法
  hasMenuPermission: (state) => (menuId) => {
    const funcCodes = state.user.funcCodes || []
    if (funcCodes.length === 0) {
      return true // 如果没有权限码，默认有权限（向后兼容）
    }
    return funcCodes.includes(menuId?.toString()) || funcCodes.includes(menuId)
  },

  // 检查按钮权限的方法
  hasButtonPermission: (state) => (buttonCode) => {
    const funcCodes = state.user.funcCodes || []
    if (funcCodes.length === 0) {
      return true // 如果没有权限码，默认有权限（向后兼容）
    }
    // 支持单个权限码或权限码数组
    if (Array.isArray(buttonCode)) {
      return buttonCode.some(code => funcCodes.includes(code?.toString()) || funcCodes.includes(code))
    }
    return funcCodes.includes(buttonCode?.toString()) || funcCodes.includes(buttonCode)
  }
}

function getManageMenu(menu = {}) {
  if (menu.children) {
    return getManageMenu(menu.children[0])
  } else {
    return menu.name
  }
}
export default getters
