import { loginByUsername, logout, getLoginUser } from '@/api/login'
import { getUserInfo, setIsNewLogin, getToken, setToken, setRfleshToken, getRfleshToken, removeToken, getUserId, setUserId, setUserInfo, removeUserId, getEntSerno, setEntSerno, removeEntSerno, setEncryptUserId, getEncryptUserId, removeEncryptUserId, getIsNewLogin, getIsShowSpot } from '@/utils/auth'

import { getStore, setStore, clearStore } from '@/views/otherResource/cmdb/util/store'
import { setIsShowSpot } from '../../utils/auth'
// 导入本地菜单数据
import menuData from '@/router/menu.json'

// 菜单权限过滤工具函数
const filterMenuByPermission = (menuTree, funcCodes) => {
  if (!funcCodes || !Array.isArray(funcCodes) || funcCodes.length === 0) {
    // 如果没有权限码，返回所有菜单（保持原有逻辑）
    console.log('没有权限码，返回所有菜单')
    return menuTree
  }


  const filterMenu = (menus, level = 0) => {
    if (!menus || !Array.isArray(menus)) {
      return []
    }

    const indent = '  '.repeat(level)

    return menus.map(menu => {
      // 将menuId转换为字符串进行比较，确保类型一致
      const menuIdStr = menu.menuId ? menu.menuId.toString() : ''
      const hasDirectPermission = funcCodes.includes(menuIdStr) || funcCodes.includes(menu.menuId)
      
      // 处理子菜单
      if (menu.children && Array.isArray(menu.children) && menu.children.length > 0) {
        // 递归过滤子菜单，只保留有权限的子菜单
        const filteredChildren = filterMenu(menu.children, level + 1)
        
        
        // 如果当前菜单有直接权限，或者有子菜单有权限，则显示该菜单
        if (hasDirectPermission || filteredChildren.length > 0) {
          return {
            ...menu,
            children: filteredChildren
          }
        }
        
        // 既没有直接权限，子菜单也都没有权限，不显示
        return null
      }
      
      // 叶子节点，直接根据权限判断
      if (hasDirectPermission) {
        return menu
      } else {
        return null
      }
    }).filter(item => item !== null) // 过滤掉null值
  }
  
  const result = filterMenu(menuTree)
  return result
}

const user = {
  state: {
    user: '',
    status: '',
    code: '',
    token: getToken(),
    refleshToken: getRfleshToken(),
    name: '',
    avatar: '',
    introduction: '',
    buildId: '',
    userId: getUserId(),
    userInfo: getUserInfo(),
    entSchool: getEntSerno(),
    menuTree: [],
    alarmData: [],
    isReal: '0',
    realFilter: null,
    setting: {
      articlePlatform: []
    },
    isYulan: '',
    encryptUserId: getEncryptUserId(),
    isNewLogin: getIsNewLogin(),
    // 新增：用户权限码
    funcCodes: []
  },

  mutations: {
    SET_CODE: (state, code) => {
      state.code = code
    },
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_ENTSERNO: (state, entSerno) => {
      state.entSchool = entSerno
    },
    SET_INTRODUCTION: (state, introduction) => {
      state.introduction = introduction
    },
    SET_SETTING: (state, setting) => {
      state.setting = setting
    },
    SET_STATUS: (state, status) => {
      state.status = status
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_REFLESHTOKEN: (state, refleshToken) => {
      state.refleshToken = refleshToken
    },
    SET_USER_INFO: (state, userInfo) => {
      state.userInfo = userInfo
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_USER_ID: (state, userId) => {
      state.userId = userId
    },
    SET_MENU: (state, menuTree) => {
      state.menuTree = menuTree
    },
    SET_ALARM_DATA: (state, alarmData) => {
      state.alarmData = alarmData
    },
    SET_ISREAL: (state, isReal) => {
      state.isReal = isReal
    },
    SET_REAL_FILTER: (state, realFilter) => {
      state.realFilter = realFilter
    },
    SET_BUILD_ID: (state, buildId) => {
      state.buildId = buildId
    },
    SET_IS_YULAN: (state, isYulan) => {
      state.isYulan = isYulan
    },
    SET_ENCRYPT_USER_ID: (state, encryptUserId) => {
      state.encryptUserId = encryptUserId
    },
    SET_IS_NEW_LOGIN: (state, isNewLogin) => {
      state.isNewLogin = isNewLogin
    },
    // 新增：设置用户权限码
    SET_FUNC_CODES: (state, funcCodes) => {
      state.funcCodes = funcCodes || []
    }
  },

  actions: {
    // 用户名登录
    LoginByUsername({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        // if (process.env.NODE_ENV === 'development') {
        //   userInfo.redirect_uri = 'http://localhost:9528/newLogin'
        // }
        loginByUsername(userInfo).then(response => {
          const data = response.data.data
          console.log(data, '获取登录结果')
          commit('SET_TOKEN', data.access_token) // 新框架改动点 token->access-token
          setToken(data.access_token)
          //  新框架改动点 获取用户信息不需要使用userid，直接请求就行
          commit('SET_REFLESHTOKEN', data.refresh_token) // 新框架改动点 token->access-token
          setRfleshToken(data.refresh_token)
          console.log('保存结束')
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    }, 
    // 获取用户信息
    GetUserInfo({ commit }) {
      return new Promise((resolve, reject) => {
        // 新框架改动点 获取用户信息
        getLoginUser().then(response => {
          const data = response.data.data
          commit('SET_USER_ID', data.userId)
          setUserId(data.userId)
          setEntSerno(data.userId)
          commit('SET_NAME', data.userName)
          commit('SET_ENTSERNO', data.entSerno)
          setEncryptUserId(data.encryptUserId)
          commit('SET_ENCRYPT_USER_ID', data.encryptUserId)
          // 将用户对象保存
          setUserInfo(data)
          commit('SET_USER_INFO', data)

          // 获取用户权限码 - 简化版本
          const funcCodes = data.funcCodes || [] 
         /*  const funcCodes = [
            '321000',
            '322100','322110','322111','322112','322113','322114','322115','322116',] */
          commit('SET_FUNC_CODES', funcCodes)
          console.log('用户权限码:', funcCodes)

          // 检查是否有基础权限（321000 - 运管驾驶舱）
          if (!funcCodes.includes('320001')) {
            console.warn('用户无基础权限，权限码:', funcCodes)
            
            // 清理登录信息
            commit('SET_TOKEN', '')
            commit('SET_REFLESHTOKEN', '')
            commit('SET_MENU', [])
            commit('SET_FUNC_CODES', [])
            removeToken()
            removeEntSerno()
            removeUserId()
            removeEncryptUserId()
            
            // 延迟弹窗，确保DOM渲染完成
            setTimeout(() => {
              // 使用Element UI的MessageBox进行提示
              import('element-ui').then(ElementUI => {
                ElementUI.MessageBox.alert('暂无此应用内部功能权限', '权限提示', {
                  confirmButtonText: '确定',
                  type: 'warning',
                  showClose: false,
                  closeOnClickModal: false,
                  closeOnPressEscape: false,
                  callback: () => {
                    // 跳转到指定登录页面
                    window.location.href = 'http://144.7.97.207:8000/login.html'
                  }
                })
              }).catch(() => {
                // 如果Element UI加载失败，使用原生alert
                alert('暂无此应用内部功能权限')
                window.location.href = 'http://144.7.97.207:8000/login.html'
              })
            }, 100)
            
            // 返回权限错误
            reject(new Error('用户无基础权限'))
            return
          }

          // 从本地menu.json文件获取菜单数据并进行权限过滤
          try {
            const originalMenuData = menuData.data
            
            // 根据权限码过滤菜单
            const filteredMenuData = filterMenuByPermission(originalMenuData, funcCodes)
            
            commit('SET_MENU', filteredMenuData)
            resolve(filteredMenuData)
          } catch (err) {
            console.error('读取本地菜单数据失败:', err)
            commit('SET_MENU', [{ path: '/welcome' }])
            reject(err)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 登出
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_REFLESHTOKEN', '') // 新框架改动点 token->access-token
          commit('SET_MENU', [])
          commit('SET_FUNC_CODES', []) // 清除权限码

          removeToken()
          removeEntSerno()
          removeUserId()
          removeEncryptUserId()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    IsNewLogin({ commit }, isNewLogin) {
      return new Promise(resolve => {
        // 保存到缓存
        setIsNewLogin(isNewLogin)
        commit('SET_IS_NEW_LOGIN', isNewLogin)
        resolve()
      })
    },
    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        commit('SET_REFLESHTOKEN', '') // 新框架改动点 token->access-token
        commit('SET_FUNC_CODES', []) // 清除权限码
        removeToken()
        resolve()
      })
    }
  }
}

export default user
