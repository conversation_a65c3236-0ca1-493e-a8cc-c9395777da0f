import axios from 'axios'
import { Message, MessageBox } from 'element-ui'
import store from '@/store'
import { getToken, getRfleshToken } from '@/utils/auth'

// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 80000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  /*  config => {
     // do something before request is sent
     if (store.getters.token) {
       // let each request carry token
       // ['X-Token'] is a custom headers key
       // please modify it according to the actual situation
       config.withCredentials = true
       if (store.getters.token) {
         config.headers['Authorization'] = 'Bearer ' + getToken()
       }
       return config
     }
     return config
   }, 
  config => {
    // 如果请求配置中有 skipAuthCheck 标记，则跳过 token 设置
    if (!config.skipAuthCheck && store.getters.token) {
      config.withCredentials = true
      config.headers['Authorization'] = 'Bearer ' + getToken()
    }
    return config
  }, */
  config => {
    // 如果请求配置中有 skipAuthCheck 标记，则跳过 token 设置
    if (!config.skipAuthCheck && store.getters.token) {
      config.withCredentials = true
      config.headers['Authorization'] = 'Bearer ' + getToken()
    }
    return config
  },
  error => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
     * If you want to get http information such as headers or status
     * Please return  response => response
     */

  /**
     * Determine the request status by custom code
     * Here is just an example
     * You can also judge the status by HTTP Status Code
     */
  response => {
    const res = response.data
    const status = Number(response.status)
    if (status === 200 && response.request.responseURL.indexOf('/auth/ssocode/token') > 0) return response
    else if (status === 200 && response.request.responseType === 'blob') return response
    // if the custom code is not 20000, it is judged as an error.
    if (res.code !== 200 && res.code !== 0) {
      Message({
        message: res.message,
        type: 'error',
        duration: 5 * 1000
      })
      // 508:非法的token; 512:其他客户端登录了;  514:Token 过期了;
      if (res.code === 508 || res.code === 512 || res.code === 514 || res.code === 230001) {
        this.$store.dispatch('LogOut')
        MessageBox.confirm('你已被登出，可以取消继续留在该页面，或者重新登录', '确定登出', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          store.dispatch('FedLogOut').then(() => {
            location.href = window.location.origin + '/login'
            // location.reload()
          })
        })
      }
      return Promise.reject(res)
    } else {
      if (res.data !== null && res.data.statusCodeValue === 204) {
        Message({
          message: '您已退出登录',
          type: 'success',
          duration: 5 * 1000
        })
      }
      return response
    }
  },
  error => {
    if (error.request.responseType === 'blob') {
      const reader = new FileReader()
      reader.readAsText(error.response.data)
      reader.onload = (e) => {
        const r = JSON.parse(e.target.result)
        const message = r.msg || error.message
        Message({
          message: message,
          type: 'error'
        })
      }
    } else {
      console.log(error.response)
      if (error.response.status === 401) {
        MessageBox.confirm('你已被登出，请重新登录', '确定登出', {
          confirmButtonText: '重新登录',
          type: 'warning',
          showClose: false,
          showCancelButton: false,
          closeOnClickModal: false,
          closeOnPressEscape: false,
          closeOnHashChange: false,
          beforeClose: (action, instance, done) => {
            instance.confirmButtonLoading = true
            store.dispatch('FedLogOut').then(() => {
              location.href = window.location.origin + '/login'
              // location.reload()
            })
          }
        })
      } else {
        Message({
          message: error.response ? error.response.data.msg : error.message.indexOf('timeout') > -1 ? '请求超时，请检查网络环境' : error.message,
          type: 'error',
          duration: 5 * 1000
        })
      }
    }
    return Promise.reject(error)
  }
)

export default service
