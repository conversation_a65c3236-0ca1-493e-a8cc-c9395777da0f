import request from '@/utils/request'
// 协议类型
export function getProtocolTypeList() {
  return request({
    url: '/szdlService/api/deviceManage/getProtocolTypeList',
    method: 'get'

  })
}
// 厂商代码
export function getVenderCodeList() {
  return request({
    url: '/szdlService/api/deviceManage/getVenderCodeList',
    method: 'get'

  })
}
export function configurationLockNet(data) {
  return request({
    url: '/szdlService/api/deviceManage/configurationLockNet',
    method: 'post',
    data
  })
}
