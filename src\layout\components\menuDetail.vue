<template>
  <div>
    <div class="menuDetail">
      <template v-for="item in routes" v-if="!item.hidden&&item.children">
        <div :key="item.name" class="menuDetail_item subLink" :index="route.path+'/'+item.path" :class="{'submenu-title-noDropdown':true}">
          <svg-icon v-if="item.meta&&item.meta.icon" :icon-class="item.meta.icon" />
          <span v-if="item.meta&&item.meta.title" style="line-height: 3.7040vh;">{{ item.meta.title }}</span>
          <template v-for="subItem in item.children" v-if="!subItem.hidden&&subItem.children === null">
            <router-link :key="subItem.name" class="menuDetail_item" :to="item.path+'/'+subItem.path" @click.native="chooseRouters(route)">
              <div :index="item.path+'/'+subItem.path" class="threeSubLink" :class="{'submenu-title-noDropdown':true}">
                <svg-icon v-if="subItem.meta&&subItem.meta.icon" :icon-class="subItem.meta.icon" />
                <span v-if="subItem.meta&&subItem.meta.title">{{ subItem.meta.title }}</span>
              </div>
            </router-link>
          </template>
          <!--四级菜单-->
          <template v-for="subItem in item.children" v-if="!subItem.hidden&&subItem.children">
            <div :key="subItem.name" :index="item.path+'/'+subItem.path" class="subMenu" :class="{'submenu-title-noDropdown':true}">
              <svg-icon v-if="subItem.meta&&subItem.meta.icon" :icon-class="subItem.meta.icon" />
              <span v-if="subItem.meta&&subItem.meta.title">{{ subItem.meta.title }}</span>
              <template v-for="thirdItem in subItem.children" v-if="!thirdItem.hidden&&thirdItem.children === null">
                <router-link :key="thirdItem.name" class="menuDetail_item" :to="subItem.path+'/'+thirdItem.path" @click.native="chooseRouters(route)">
                  <div :index="subItem.path+'/'+thirdItem.path" class="fourSubLink" :class="{'submenu-title-noDropdown':true}">
                    <svg-icon v-if="thirdItem.meta&&thirdItem.meta.icon" :icon-class="thirdItem.meta.icon" />
                    <span v-if="thirdItem.meta&&thirdItem.meta.title">{{ thirdItem.meta.title }}</span>
                  </div>
                </router-link>
              </template>
            </div>
          </template>
        </div>
      </template>
      <template v-for="item in routes" v-if="!item.hidden&&item.children === null">
        <router-link :key="item.name" class="menuDetail_item threeSubLink" :to="route.path+'/'+item.path" @click.native="chooseRouters(route)">
          <div :index="route.path+'/'+item.path" :class="{'submenu-title-noDropdown':true}">
            <svg-icon v-if="item.meta&&item.meta.icon" :icon-class="item.meta.icon" />
            <span v-if="item.meta&&item.meta.title" style="line-height: 3.7040vh;">{{ item.meta.title }}</span>
          </div>
        </router-link>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MenuDetail',
  props: ['route'],
  computed: {
    routes: function() {
      return this.route.children
    }
  },
  methods: {
    hasOneShowingChildren(children) { // 是否直接显示只有一个子菜单的对象
      const showingChildren = children.filter(item => {
        return !item.hidden
      })
      if (showingChildren.length === 1) {
        return true
      }
      return false
    },
    chooseRouters(router) { // 修改选中的左侧菜单
      this.$emit('hiddenProver', router)
      this.$store.dispatch('ChooseRouters', router).then(() => {
      }).catch(() => {
        console.log('修改左侧菜单失败！')
      })
    }
  }
}
</script>
