<template>
  <div class="app-container">
    <!-- 头部查询 -->
    <el-scrollbar class="scrollbar">
      <div class="header-search border" :style="{ overflow: 'hidden' }">
        <el-form ref="normalForm" :inline="true" :model="form" label-width="7.87vh" size="medium">
          <!-- 专题名称查询 -->
          <el-form-item label="" prop="ip">
            <el-input v-model.trim="form.typeName" placeholder="请输入专题名称" style="width: 18.52vh" />
          </el-form-item>
          <el-button type="primary" size="medium" icon="el-icon-search" @click="queryData">查询</el-button>
          <el-button v-if="checkButtonPermission(322121)" type="primary" size="medium" icon="el-icon-plus" @click="handleEdit()">新增</el-button>
        </el-form>
      </div>
    </el-scrollbar>
    <el-table ref="table" v-loading="instanceTableLoading" :data="tableData" :height="tableHeight" border stripe
      min-width="100%" :cell-class-name="cellClassName" element-loading-text="数据加载中">
      <el-table-column type="index" label="序号" width="80" align="center" />
      <el-table-column label="专题名称" prop="typeName" align="center" />
      <el-table-column label="大屏展示" prop="isSpot" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.isSpot === 1 ? '是' : '否' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" width="180" prop="orderNum" align="center" />
      <el-table-column v-if="hasAnyActionPermission([322122, 322123, 322124])" label="操作" width="280" align="center">
        <template slot-scope="scope">
          <el-button v-if="checkButtonPermission(322122)" type="text" icon="el-icon-edit" @click="handleEdit(scope.row.typeId)">编辑</el-button>
          <el-button v-if="checkButtonPermission(322123)" type="text" @click="linkType(scope.row)">关联类型</el-button>
          <el-button v-if="checkButtonPermission(322124)" type="text" @click="handleDel(scope.row.typeId)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page.sync="pager.page" :page-size.sync="pager.size" :page-sizes="[10, 20, 50]"
      layout="total, sizes, prev, pager, next, jumper" :total="totalPage" @size-change="handleSizeChange"
      @current-change="handleCurrentChange">
    </el-pagination>
    <el-dialog v-dialogDrag :title="`专题类型关系配置-${currentSubject.typeName || ''}`" :visible.sync="relativeVisible" width="74.07vh"
      :before-close="handleClose">
      <div style="margin-left:7.41vh" v-if="relativeVisible">
        <el-transfer v-model="relativeValue" filterable :filter-method="filterMethod" filter-placeholder="请输入类型名称"
          :left-default-checked="[]" :props="{
            key: 'id',
            label: 'cname'
          }" :titles="['未配置', '已配置']" :data="relativeData" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose"
          style="background-color: rgba(150, 170, 222, 0.4); border-color: rgba(150, 170, 222, 0.4); color: rgb(255, 255, 255);">取
          消</el-button>
        <el-button type="primary" @click="handleSave">确 定</el-button>
      </span>
    </el-dialog>
    <edit-modal v-if="v === 'edit'" v-bind="{ id: editId }" :is-dialog="true" @saved="queryData" />
  </div>
</template>

<script>
import { dictSubjectPage, getDictSubjectRel, updateBySusbjectId } from '@/api/otherResource/cmdb/DictSubject'
import { queryAllCiTypeList } from '@/api/otherResource/cmdb/confCiType'
import editModal from './edit'
import { listPage } from '@/views/otherResource/soc/mixins'
import permission from '@/views/otherResource/soc/mixins/permission'

export default {
  name: 'Index',
  mixins: [listPage, permission],
  components: {
    editModal
  },
  data() {
    return {
      form: {},
      pager: { page: 1, size: 10 }, // 分页查询的当前页数
      totalPage: 0,
      tableData: [],
      relativeData: [],
      relativeValue: [],
      checkSubjectId: '',
      tableHeight: '680px',
      instanceTableLoading: false,
      relativeVisible: false,
      editId: null,
      currentSubject: {} // 当前选中的专题对象
    }
  },
  created() {
    this.queryData()
  },
  methods: {
    queryData() {
      let params = Object.assign({}, this.form, this.pager)
      this.instanceTableLoading = true
      dictSubjectPage(params).then(res => {
        this.instanceTableLoading = false
        if (res.data.code == 0) {
          this.tableData = res.data.data.records
          this.pager.page = res.data.data.current
          this.totalPage = res.data.data.total
        }
      }).catch(err => {
        this.instanceTableLoading = false
      })
      queryAllCiTypeList().then(res => {
        this.relativeData = res.data.data
      })
    },
    linkType(row) {
      const _this = this
      this.currentSubject = row // 保存当前专题信息
      getDictSubjectRel(row.typeValue).then(res => {
        res.data.data.forEach(ele => {
          _this.relativeValue.push(ele.id)
        })
        _this.checkSubjectId = row.typeValue
        _this.relativeVisible = true
      })
    },
    handleSave() {
      const dictSubjectRelList = []
      // if (this.relativeValue.length > 0) {
      this.relativeValue.forEach(ele => {
        dictSubjectRelList.push({ subjectId: this.checkSubjectId, typeId: ele })
      })
      updateBySusbjectId({ subjectId: this.checkSubjectId, dictSubjectRelList: dictSubjectRelList }).then(res => {
        console.log(res)
        if (res.data.code == 0) {
          this.$message({
            showClose: true,
            message: `变更成功!`,
            type: 'success'
          })
          this.clearCheckType()
          this.relativeVisible = false
        } else {
          this.$message({
            showClose: true,
            message: `变更失败`,
            type: 'warning'
          })
        }
      })
      // } else {
      //   this.$message({
      //     showClose: true,
      //     message: `清空关联类型？`,
      //     type: "warning"
      //   });
      // }
    },
    filterMethod(query, item) {
      return item.cname.indexOf(query) > -1
    },
    clearCheckType() {
      this.relativeValue = []
      this.checkSubjectId = ''
    },
    reset() {
      this.handleReset()
      this.queryData()
    },
    handleReset() {
      this.form = {
        label: ''
      }
    },
    handleClose(done) {
      this.clearCheckType()
      this.currentSubject = {} // 清除当前专题信息
      try {
        done()
      } catch {
        this.relativeVisible = false
      }
    },
    handleSizeChange(val) {
      this.pager.size = val
      this.queryData()
    },
    handleCurrentChange(val) {
      this.pager.page = val
      this.queryData()
    },
    handleEdit(id) {
      this.editId = id
      this.v = 'edit'
    },
    handleDel(id) {
      this.delData({
        url: '/cmdbService/api/sysType/delete/' + id,
        success: () => this.queryData()
      })
    },
    cellClassName({ columnIndex }) {
      if (columnIndex < 2) {
        return 'center'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.header-search {
  position: relative;
  min-width: 990px;
  background-color: rgba(9, 72, 148, 1); //改颜色

  .moreImg {
    position: absolute;
    bottom: 20px;
    left: 50%;
    z-index: 1000;
    width: 12px;
    height: 12px;
    cursor: pointer;
    transform: translateX(-50%);
  }

  .btn-more {
    padding: 0;
    padding-top: 10px;
    padding-left: 5px;
    font-size: 12px;
    border: none;

    & :hover {
      background-color: #fff;
      border: none;
    }
  }
}

.col-width {
  width: 320px;
}

.query {
  float: right;
  padding-right: 28px;
  margin-bottom: 10px;
  text-align: right;
}
</style>
