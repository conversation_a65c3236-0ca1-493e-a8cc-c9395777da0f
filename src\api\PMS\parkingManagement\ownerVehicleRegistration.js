import request from '@/utils/request'

// 获取报修列表
export function fetchList(data) {
  return request({
    url: '/pmsService/api/garageCarUser/list',
    method: 'post',
    data
  })
}
// 添加
export function fetchCreate(data) {
  return request({
    url: '/pmsService/api/garageCarUser/insert',
    method: 'post',
    data
  })
}
// 详情
export function fetchDetail(data) {
  return request({
    url: '/pmsService/api/garageCarUser/get',
    method: 'post',
    data
  })
}

// 删除
export function fetchDelete(ids) {
  var data = { ids }
  return request({
    url: '/pmsService/api/garageCarUser/delete',
    method: 'post',
    data
  })
}
// 编辑
export function fetchUpdate(data) {
  return request({
    url: '/pmsService/api/garageCarUser/update',
    method: 'post',
    data
  })
}

export function importExcel(data) {
  return request({
    url: '/pmsService/api/garageCarUser/exportGarageCarUser',
    method: 'post',
    data
  })
}
export function importExcelNoUser(data) {
  return request({
    url: '/pmsService/api/garageCarUser/exportGarageCarUserNoUser',
    method: 'post',
    data
  })
}
// 通过业主获取停车场和车位层级关系
export function fetchGarageAndParkingLot(data) {
  return request({
    url: '/pmsService/api/garageCarUser/findGarageAndParkingLot',
    method: 'post',
    data
  })
}
