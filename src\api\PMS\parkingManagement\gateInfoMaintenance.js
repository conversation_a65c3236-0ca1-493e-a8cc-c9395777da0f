import request from '@/utils/request'

export function fetchList(data) {
  return request({
    url: '/pmsexternal/api/declareParkDec/list',
    method: 'post',
    data
  })
}

// 添加
export function fetchCreate(data) {
  return request({
    url: '/pmsexternal/api/declareParkDec/insert',
    method: 'post',
    data
  })
}
// 详情
export function fetchDetail(data) {
  return request({
    url: '/pmsexternal/api/declareParkDec/get',
    method: 'post',
    data
  })
}
// 删除
export function fetchDelete(ids) {
  var data = { ids }
  return request({
    url: '/pmsexternal/api/declareParkDec/delete',
    method: 'post',
    data
  })
}
// 编辑
export function fetchUpdate(data) {
  return request({
    url: '/pmsexternal/api/declareParkDec/update',
    method: 'post',
    data
  })
}

// 申报
export function fetchApplication(ids) {
  const data = { ids }
  return request({
    url: '/basiDeclare/garageDeclare',
    method: 'post',
    data
  })
}
