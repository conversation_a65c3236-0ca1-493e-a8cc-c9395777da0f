<template>
  <div>
    <div class="header">
      <slot name="headerHandler" />
    </div>
    <!--
    :header-cell-style="{ borderColor:'#ffffff' }"-->
    <div class="tableDiv">
      <el-table
        ref="table"
        v-loading="listLoading"
        :data="list"
        border
        :stripe="isStripe"
        :height="tableHeight"
        :max-height="height"
        :element-loading-text="loadingText"
        fit
        :style="tableStyle"
        :tree-props="treeProps"
        :row-key="rowKey"
        class="striped"
        @sort-change="sortChange"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column v-if="showSelectColumn" type="selection" width="55" />
        <el-table-column v-if="showIndexColumn" type="index" :label="indexName" width="100" />
        <el-table-column
          v-for="propItem in tableColumn"
          :key="propItem.prop"
          :label="propItem.useTable ? $t(propItem.label):propItem.label"
          v-bind="propItem"
          align="center"
          :sortable="propItem.sort|| false"
          show-overflow-tooltip
        >
          <template slot="header" slot-scope="scope">
            <span v-if="propItem.isShowCustomTooltip">
              <span>{{ scope.column.label }}</span>
              <el-tooltip placement="top">
                <div slot="content">{{ propItem.customTooltip }}</div>
                <i class="el-icon-warning-outline" style="color:orange;margin-left:0.4630vh" />
              </el-tooltip>
            </span>
            <span v-else>{{ scope.column.label }}</span>
            <!-- <el-popover v-if="scope.$index === tableColumn.length" class="el-table-config" placement="bottom" width="340" trigger="click"> -->
            <el-popover v-if="false" class="el-table-config" placement="bottom" width="31.4840vh" trigger="click">
              <el-table :data="propList">
                <el-table-column width="9.2600vh" prop="label" label="列" />
                <el-table-column width="18.5200vh" label="控制">
                  <template slot-scope="scope1">
                    <el-radio-group v-model="scope1.row.isShow" size="mini">
                      <el-radio-button :label="true">显示</el-radio-button>
                      <el-radio-button :label="false">隐藏</el-radio-button>
                    </el-radio-group>
                  </template>
                </el-table-column>
              </el-table>
              <i
                slot="reference"
                class="el-icon-setting"
                style="cursor: pointer;position:absolute;display: inline-block;height: 100%; right: 5%; top: calc((100% - 1.2964vh)/ 2)"
                @click.stop
              />
            </el-popover>
          </template>
          <template slot-scope="scope">
            <slot :name="propItem.slotName" :row="scope.row">
              <span
                :style="'color:'+(scope.row.color == null? 'unset':scope.row.color) +';border-color:'+(scope.row.color == null? 'unset':scope.row.color) +';'"
              >{{
                scope.row[propItem.prop] || '-' }}</span>
            </slot>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <slot name="footer">
        <el-pagination
          v-if="showPagination"
          background
          :total="total"
          :current-page="page"
          :page-sizes="pageSizes"
          :page-size="limit"
          :layout="layout"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ZykTable',
  props: {
    listLoading: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => ([])
      // required: true
    },
    indexName: {
      type: String,
      default: '序号'
    },
    loadingText: {
      type: String,
      default: '数据正在加载中...'
    },
    showSelectColumn: {
      type: Boolean,
      default: false
    },
    showIndexColumn: {
      type: Boolean,
      default: true
    },
    showPagination: {
      type: Boolean,
      default: true
    },
    tableHeight: {
      type: [Number, String],
      default: null
    },
    tableMaxHeight: {
      type: [String, Number],
      default: null
    },
    propList: {
      type: Array,
      required: true
    },
    total: {
      type: Number,
      default: 0
    },
    page: {
      type: Number,
      default: 1
    },
    limit: {
      type: Number,
      default: 10
    },
    pageSizes: {
      type: Array,
      default: () => ([10, 20, 30, 50, 100, 200])
    },
    layout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },
    tableStyle: {
      type: String,
      default: 'width: 100%; position: none;'
    },
    isStripe: {
      type: Boolean,
      default: true
    },
    treeProps: {
      type: Object,
      default: () => {
        return {
          children: 'childLayerMessage',
          hasChildren: 'childLayerMessage'
        }
      }
    },
    rowKey: {
      type: String,
      default: ''
    },
    clearSelection: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    height() {
      return this.tableMaxHeight || this.$store.getters.tableMaxHeight
    },
    tableColumn() {
      const ary = this.propList.map(ele => {
        this.$set(ele, 'isShow', ele.isShow !== false)
        return ele
      })
      return ary.filter(ele => ele.isShow !== false)
    }
  },
  watch: {
    clearSelection: function(n, o) {
      this.$refs.table.clearSelection()
    }
  },
  methods: {
    renderHeader(h, { column, $index }) {
      let index = 0
      if (this.showSelectColumn) {
        index++
      }
      if (this.showIndexColumn) {
        index++
      }
      if (this.propList[$index - index].isShowCustomTooltip) {
        const content = [
          h(
            'div',
            {
              slot: 'content'
            },
            this.propList[$index - index].customTooltip
          )
        ]

        return h('span', [
          h('span', column.label),
          h(
            'el-tooltip',
            {
              props: {
                placement: 'top'
              }
            },
            [
              content,
              h('i', {
                class: 'el-icon-warning-outline',
                style: 'color:orange;margin-left:0.4630vh;'
              })
            ]
          )
        ])
      } else {
        return h('span', [
          h('span', column.label)
        ])
      }
    },
    handleSelectionChange(val) {
      this.$emit('handleSelectionChange', val)
    },
    handleSizeChange(val) {
      this.$emit('handleSizeChange', val)
    },
    handleCurrentChange(val) {
      this.$emit('handleCurrentChange', val)
    },
    handleRowClick(row, event, column) {
      this.$emit('handleRowClick', row, event, column)
    },
    sortChange(row) {
      this.$emit('sortChange', row)
    }
  }
}
</script>

<style lang="scss" scoped>
.tableDiv {
  padding-top: 1vh;
}
/deep/.pointer span {
  cursor: pointer;
  &:hover {
    text-decoration: underline
  }
}
</style>

