import request from '@/utils/request'
export function fetchList(data) {
  return request({
    url: '/pmslinkage/api/linkage/methodDict/list',
    method: 'post',
    data
  })
}

export function fetchDetail(queryData) {
  return request({
    url: '/pmslinkage/api/linkage/methodDict/detail',
    method: 'get',
    params: queryData
  })
}

export function fetchDelete(ids) {
  var data = { ids }
  return request({
    url: '/pmslinkage/api/linkage/methodDict/delete',
    method: 'post',
    data
  })
}

export function fetchCreate(data) {
  return request({
    url: '/pmslinkage/api/linkage/methodDict/create',
    method: 'post',
    data
  })
}

export function fetchUpdate(data) {
  return request({
    url: '/pmslinkage/api/linkage/methodDict/update',
    method: 'post',
    data
  })
}

export function fetchMethodTreeList(methodType) {
  var data = { methodType }
  return request({
    url: '/pmslinkage/api/linkage/method/methodTree',
    method: 'post',
    data
  })
}
