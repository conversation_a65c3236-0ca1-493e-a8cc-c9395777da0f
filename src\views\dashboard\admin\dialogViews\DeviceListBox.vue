<template>
  <div v-loading="loading" element-loading-text="拼命加载中" element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0)">
    <div class="boxListContent">
      <div class="deviceDetail" :style="{ 'left': !isShowZy ? '1.78vh' : '42vh', 'top': !isShowZy ? '1vh' : '4vh' }">
        <el-input v-model="input" style="font-size:1.06vh;width:100%;" placeholder="搜索设备名称、唯一编码、道路、区域"
          @input="searchChange" @blur="hideDevicePanelDetail" @focus="showDevicePanelDetail" />
        <el-tooltip class="item" effect="dark" content="搜索" placement="bottom">
          <div class="deviceCs" style="position: absolute;right: 2.7vh;top:0.7vh;" @click="showSearch" />
        </el-tooltip>
        <div class="closeBtn" style="position: absolute;right: 0.89vh;top: 0.7vh;" @click="showOff" />
      </div>
      <div v-show="isShowType" class="devicePanelDetail"
        :style="{ 'left': !isShowZy ? '1.78vh' : '42vh', 'top': !isShowZy ? '5.5vh' : '8.5vh' }">
        <div class="around-box">
          <div class="recommend-c">
            <div :class="searchType == 0 ? 'typeActive' : 'typeNoActive'" class="item sbmc-item" data-type="search"
              keyword="设备名称" @click="changeType(0)">
              <div class="img-c">
                <img v-if="searchType == 0" src="/static/img/qx_active.png" width="100">
                <img v-else src="/static/img/qx.png" width="100">
              </div>
              <div v-if="searchType == 0" class="name_active ellipsis">全选</div>
              <div v-else class="name ellipsis">全部</div>
            </div>
            <div :class="searchType == 1 ? 'typeActive' : 'typeNoActive'" class="item sbmc-item" data-type="search"
              keyword="设备" @click="changeType(1)">
              <div class="img-c">
                <img v-if="searchType == 1" src="/static/img/sb_active.png" width="100">
                <img v-else src="/static/img/sb.png" width="100">
              </div>
              <div v-if="searchType == 1" class="name_active ellipsis">设备</div>
              <div v-else class="name ellipsis">设备</div>
            </div>
            <div :class="searchType == 2 ? 'typeActive' : 'typeNoActive'" class="item sblx-item" data-type="search"
              keyword="道路" @click="changeType(2)">
              <div class="img-c">
                <img v-if="searchType == 2" src="/static/img/dl_active.png" width="100">
                <img v-else src="/static/img/dl.png" width="100">
              </div>
              <div v-if="searchType == 2" class="name_active ellipsis">道路</div>
              <div v-else class="name ellipsis">道路</div>
            </div>
            <div :class="searchType == 3 ? 'typeActive' : 'typeNoActive'" class="item qy-item" data-type="search"
              keyword="区域" @click="changeType(3)">
              <div class="img-c">
                <img v-if="searchType == 3" src="/static/img/qy_active.png" width="100">
                <img v-else src="/static/img/qy.png" width="100">
              </div>
              <div v-if="searchType == 3" class="name_active ellipsis">区域</div>
              <div v-else class="name ellipsis">区域</div>
            </div>
          </div>
        </div>
        <!-- 设备列表搜索结果 -->
        <div v-show="isShowPanel" v-loading.sync="loading" element-loading-background="rgba(0, 0, 0, 0.8)"
          class="deviceSearchItem"
          style="justify-content: start!important;cursor: pointer;max-height: 59vh;overflow: auto;">
          <div class="docletItem" style="height: 100%">
            <div v-for="item in deviceData" :key="item.deviceId">
              <div
                :class="selectDeviceItem.deviceId == item.deviceId ? 'checkBoxGroup_item_active' : 'checkBoxGroup_item'"
                @click="selectDevice(item)">
                <div style="display: flex;line-height: 3vh;font-size: 1.067vh; cursor: pointer;">
                  <!-- 设备图标 -->
                  <img v-if="item.searchType == 1 && selectDeviceItem.deviceId == item.deviceId"
                    style="width: 2.13vh;height: 2.2vh;margin-top: 0.3vh;"
                    :src="'/static/img/deviceIcon/' + item.deviceType + '_0.png'" width="100">
                  <img v-else-if="item.searchType == 1 && selectDeviceItem.deviceId != item.deviceId"
                    style="width: 2.13vh;height: 2.2vh;margin-top: 0.3vh;"
                    :src="'/static/img/deviceIcon/' + item.deviceType + '_1.png'" width="100">
                  <!-- 区域图标 -->
                  <img v-if="item.searchType == 3 && selectDeviceItem.deviceId == item.deviceId"
                    style="width: 2.13vh;height: 2.2vh;margin-top: 0.3vh;" src="/static/img/deviceIconActive_qy.png"
                    width="100">
                  <img v-else-if="item.searchType == 3 && selectDeviceItem.deviceId != item.deviceId"
                    style="width: 2.13vh;height: 2.2vh;margin-top: 0.3vh;" src="/static/img/deviceIcon_qy.png"
                    width="100">
                  <!-- 道路图标 -->
                  <img v-if="item.searchType == 2 && selectDeviceItem.deviceId == item.deviceId"
                    style="width: 2.13vh;height: 2.2vh;margin-top: 0.3vh;" src="/static/img/deviceIconActive_dl.png"
                    width="100">
                  <img v-else-if="item.searchType == 2 && selectDeviceItem.deviceId != item.deviceId"
                    style="width: 2.13vh;height: 2.2vh;margin-top: 0.3vh;" src="/static/img/deviceIcon_dl.png"
                    width="100">
                  <div style="margin-left: 0.356vh;">{{ item.deviceName }}<span
                      v-if="item.searchType != 2 && item.deviceSerno != null">({{ item.deviceSerno }})</span></div>
                </div>
                <div :class="selectDeviceItem.deviceId == item.deviceId ? 'checkBoxAdd_active' : 'checkBoxAdd'">{{
                  item.deviceAddress }}</div>
              </div>
            </div>
            <div v-show="!deviceData"
              style="color:#C5D4FF;font-size:1.067vh;text-align: center; height: 4.6vh; line-height:4.6vh">{{ loading ?
                '正在搜索' : '暂无数据' }}</div>
          </div>
          <div v-if="isShowPage" class="pagination-container">
            <el-pagination small :current-page="listQuery.page" :page-size="listQuery.limit" layout="slot, prev, next"
              :total="total" @current-change="handleCurrentChange">
              <span style="color:#C5D4FF;font-size: 1.067vh;">{{ listQuery.page }}/{{ totalPage }}页</span>
              <span style="color:#C5D4FF;font-size: 1.067vh;"
                :style="{ visibility: listQuery.page > 1 ? 'visible' : 'hidden' }" @click="fnPage">首页</span>
              <span style="color:#C5D4FF;font-size: 1.067vh;"
                :style="{ visibility: listQuery.page > 1 ? 'visible' : 'hidden' }" @click="myPage">末页</span>
            </el-pagination>
          </div>
        </div>
        <!-- 设备列表点击详情 -->
        <!-- <div v-if="isShowDetailPanel" style="justify-content: start!important;height:9vh;overflow:auto;cursor: pointer;">
                <div>
                    <div style="border-bottom: 1px dashed #dbdee2;">
                        <div class="deviceFh" @click="showFh">
                            <img style="width: 1vw;height: 1.6vh;" src="/static/img/fh.png" width="100" />
                            返回
                        </div>
                    </div>
                    <div class="checkBoxGroup_item" @click="selectDevice(item)">
                        <div style="display: inline-block;line-height: 19px;font-size: 0.8vw; cursor: pointer;">
                            <img style="width: 1vw;height: 1.6vh;" src="/static/img/sb.png" width="100" />
                            {{selectDeviceItem.deviceName}}
                        </div>
                        <div style="color:#999999;font-size: 12px; ">{{selectDeviceItem.deviceAddress}}</div>
                    </div>
                </div>
            </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import { findList } from '@/api/main/admin'

export default {
  props: ['visible', 'sysType', 'isShowZy'],
  data() {
    return {
      loading: false, // 是否正在加载
      searchLoading: false,
      searchFlag: false,
      searchTime: 0,
      deviceData: null,
      input: '',
      isShowPanel: false,
      isShowType: false,
      isShowDetailPanel: false,
      isShowPage: false,
      searchType: 0,
      selectDeviceItem: {
        deviceId: null
      },
      listQuery: {
        limit: 10,
        page: 1,
        isSearch: 0, // 是否点击查询（0否 ，1是）
        searchContent: '', // 搜索内容
        searchType: 0, // 搜索类型：0 全选 ，1 设备 ，2 道路 ，3 区域
        sysType: -1
      },
      total: 0,
      totalPage: 0
    }
  },
  methods: {
    fnPage() {
      this.handleCurrentChange(1)
    },
    myPage() {
      this.handleCurrentChange(this.totalPage)
    },
    getList(isSearch, time) {
      if (this.input != '') {
        this.loading = true
        this.listQuery.isSearch = isSearch === false ? 0 : 1
        this.listQuery.searchContent = this.input
        this.listQuery.searchType = this.searchType
        this.listQuery.sysType = this.sysType
        findList(this.listQuery).then(response => {
          if (time && time < this.searchTime) {
            return
          }
          this.deviceData = response.data.data
          this.total = response.data.total
          this.totalPage = Math.ceil(response.data.total / 10)

          // if (this.deviceData !== null && this.deviceData.length > 0) {
          //   this.isShowPanel = true
          // } else {
          //   this.isShowPanel = false
          //   this.isShowType = false
          // }
          this.loading = false
        })
      }
    },
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.getList(true)
    },
    changeType(type) {
      this.searchType = type
      this.listQuery.page = 1
      this.listQuery.limit = 10
      this.selectDeviceItem = {
        deviceId: null
      }
      console.log(this.searchType)
      this.getList(this.isShowPanel)
    },
    showDevicePanelDetail() { // 在 Input 获得焦点时触发
      this.isShowType = true
    },
    hideDevicePanelDetail() { // 在 Input 失去焦点时触发
      // this.isShowType = false
    },
    searchChange() { // 在 Input 值改变时触发
      if (this.searchFlag) return
      this.isShowPanel = true
      this.loading = true
      this.searchFlag = true
      this.timer = setTimeout(() => {
        this.searchFlag = false
        this.changeDevicePanelDetail()
      }, 500)
    },
    changeDevicePanelDetail() {
      if (this.input == '' || this.input == null) {
        this.deviceData = null
        this.isShowPanel = false
        this.isSearch = false
        this.isShowPage = false
        this.listQuery.page = 1
      } else {
        this.isShowType = true
        this.searchTime = Date.now()
        this.getList(false, this.searchTime)
      }
    },
    showSearch() { // 查询
      if (this.input == '' || this.input == null) {
        this.$message.error('请输入设备名称/设备编号/道路/区域！')
      }
      this.isShowPage = true
      this.getList(true)
      // this.$EventBus.$emit('visible', !this.visible)
    },
    showOff() {
      this.isShowType = false
      this.input = ''
      this.deviceData = null
      this.isShowPanel = false
      this.isShowDetailPanel = false
      this.isShowPage = false
      this.searchType = 0
      this.isSearch = false
      this.listQuery = {
        limit: 10,
        page: 1,
        isSearch: 0, // 是否点击查询（0否 ，1是）
        searchContent: this.input, // 搜索内容
        searchType: this.searchType, // 搜索类型：0 全选 ，1 设备 ，2 道路 ，3 区域
        sysType: this.sysType
      }
      this.total = 0
    },
    showFh() {
      this.isShowDetailPanel = false
      this.isShowType = true
      this.isShowPanel = true
      this.selectDeviceItem = {
        deviceId: null
      }
    },
    selectDevice(deviceData) {
      // window.frames['map1'].postMessage({ type: 'backHome' }, '*')
      // 清空地图高亮的区域颜色
      window.frames['map1'].postMessage({ type: 'clearMap' }, '*')
      console.log(111, deviceData)
      this.selectDeviceItem = deviceData
      this.isShowDetailPanel = true
      // this.isShowType = false
      // this.isShowPanel = false
      if (deviceData.searchType == 1) { // 数值类型 1 设备 ，2 道路 ，3 区域
        if (deviceData.latitude == null || deviceData.longitude == null) {
          this.$message.error('该设备未获取到经纬度坐标信息，无法定位跳转到对应点位，请检查！')
          // 取消选中设备
          this.$EventBus.$emit('resetChoose', deviceData)
          return
        }
        // 显示设备
        this.$EventBus.$emit('showDevice', deviceData)
        // 选择某一个设备
        this.$EventBus.$emit('deviceData', deviceData)
      } else if (deviceData.searchType == 3) {
        // 选择的对应区域，需高亮显示为不同的颜色
        window.frames['map1'].postMessage({ type: 'setAreaColor', area: deviceData }, '*')
        // 由于渲染区域颜色会清空地图上的所有设备标志，所以这里再重新渲染
        this.$EventBus.$emit('showDevice', deviceData)
      } else if (deviceData.searchType == 2) {
        // 道路选中
        window.frames['map1'].postMessage({ type: 'setPolyline', data: deviceData }, '*')
      }
    }
  }

}
</script>

<style scoped>
.checkGroup {
  flex-direction: column;
  display: flex;
  width: 100%;
}

.checkBoxGroup_item {
  color: #C5D4FF;
  padding: 0.53vh 0.89vh 0.64vh 0.89vh;
  border-bottom: 0.11px solid #315192;
}

.checkBoxGroup_item_active {
  color: #F97A16;
  padding: 0.53vh 0.89vh 0.64vh 0.89vh;
  border-bottom: 0.11px solid #315192;
}

/deep/ .boxListContent .el-checkbox__input.is-checked+.el-checkbox__label {
  color: #eeeeee;
}

/deep/.boxListContent .el-input__inner {
  height: 4vh;
  line-height: 4vh;
}

/deep/ .boxListContent .el-input--medium .el-input__inner {
  border: unset !important;
  color: #C5D4FF !important;
  height: 4vh;
  background-color: unset !important;
}

/deep/ .boxListContent .el-checkbox__inner::after {
  border-color: #eeeeee;
}

.boxListContent ::-webkit-input-placeholder {
  color: lightgray !important;
}

/deep/ .boxListContent .el-input--medium .el-input__icon {
  line-height: 4vh;
}

.boxListContent :-moz-placeholder {
  color: lightgray !important;
}

.boxListContent ::-moz-placeholder {
  color: lightgray !important;
}

.boxListContent :-ms-input-placeholder {
  color: lightgray !important;
}

.boxListContent .el-checkbox {
  margin-right: 0px;
}

.boxListContent {
  width: 100%;
  display: flex;
  justify-content: center;
}

.deviceDetail {
  font-size: 1.24445vh;
  width: 28.44448vh;
  /* z-index: 1; */
  padding: 0;
  border-radius: 4px;
  position: absolute;
  top: 4vh;
  left: 35.5556vh;
  display: flex;
  background: rgba(9, 72, 148, 1) !important;
  border: 0.09vh solid rgba(0, 142, 254, 1)  !important;
  color: #C5D4FF !important;
}

.devicePanelDetail {
  font-size: 1.24445vh;
  width: 28.44448vh;
  /* z-index: 1; */
  padding: 0;
  border-radius: 0.21vh;
  position: absolute;
  top: 8.2vh;
  left: 35.5556vh;
  border: 0.08889vh solid rgba(0, 142, 254, 1)  !important;
}

.devicePanelDetail .el-pagination .btn-next,
.devicePanelDetail .el-pagination .btn-prev {
  background: #282F42 !important;
  border: 0.08889vh solid rgba(0, 142, 254, 1)  !important;
  color: #C5D4FF !important;
}

.searchBtn {
  color: white;
  cursor: pointer;
  width: 2.66667vh;
}

.borrow {
  position: absolute;
  bottom: -0.53vh;
  left: 50%;
  margin-right: 3px;
  border-top-color: #ebeef5;
  border-bottom-width: 0;
  border-width: 0.53vh;
  filter: drop-shadow(0 0.18vh 1.067vh rgba(0, 0, 0, .03));
}

.borrow:after {
  bottom: -0.53vh;
  margin-left: -0.53vh;
  border-top-color: #2F3243 !important;
  border-bottom-width: 0;
  content: " ";
  border-width: 0.53vh;
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}

.around-box .recommend-c {
  height: 6vh;
  display: flex;
  align-items: center;
}

.around-box .recommend-c .item {
  width: 33.3%;
  height: 100%;
  float: left;
  text-align: center;
  cursor: pointer;
}

.around-box .recommend-c .item.active {
  background-color: #4E6BBE;
}

.around-box .recommend-c .item .img-c {
  width: 2.66667vh;
  height: 2.5vh;
  overflow: hidden;
  margin: 0 auto;
  margin-top: 1vh;
}

.around-box .recommend-c .item .img-c img {
  width: 100%;
  height: 100%;
  border: 0;
}

.around-box .recommend-c .item .name {
  font-size: 1.067vh;
  color: #C5D4FF;
}

.around-box .recommend-c .item .name_active {
  font-size: 1.067vh;
  color: #ffffff;
}

.ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

.typeActive {
  background: rgba(69, 93, 220, 0.6);
  border: 0.08889vh solid rgba(0, 142, 254, 1)  !important;
}

.typeNoActive {
  background: rgba(9, 72, 148, 1) !important;
  /* border: #01ffff !important; */
  color: #C5D4FF !important;
}

.deviceFh {
  line-height: 3vh;
  padding-left: 0.88889vh;
  font-size: 1.422vh;
  width: 7.11vh;
}

.deviceCs:hover {
  cursor: pointer;
  background: url(/static/img/search_active.png)no-repeat center;
  width: 1.78vh;
  height: 2.5vh;
}

.deviceCs {
  cursor: pointer;
  background: url(/static/img/search.png)no-repeat center;
  width: 1.78vh;
  height: 2.5vh;
}

.closeBtn {
  cursor: pointer;
  background: url(/static/img/close.png)no-repeat center;
  width: 1.78vh;
  height: 2.5vh;
}

.closeBtn:hover {
  cursor: pointer;
  background: url(/static/img/close_active.png)no-repeat center;
  width: 1.78vh;
  height: 2.5vh;
}

.checkBoxAdd {
  font-size: 1.067vh;
  margin-top: 0.2vh;
}

.checkBoxAdd_active {
  color: #F97A16;
  font-size: 1.067vh;
  margin-top: 0.2vh;
}

.deviceSearchItem {
  padding-bottom: 10px;
  height: 100%;
  background: rgba(9, 72, 148, 1) !important;
}
</style>
