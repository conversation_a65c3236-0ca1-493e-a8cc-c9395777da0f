<template>
  <div>
    <el-form ref="dataForm" :model="childData" label-width="8.3333vh">
      <el-form-item label="路灯名称">
        <el-input v-model="deviceName" disabled class="form-item-width" />
      </el-form-item>
      <el-form-item label="通道" class="btx">
        <el-select v-model="chnNoValue" style="width: 29.6296vh;" placeholder="请选择通道" @change="searchBir()">
          <el-option v-for="item in options" :key="item.chanNo" :label="item.chanNoName" :value="item.chanNo" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="开关状态">
      <el-radio-group @change="changeStatus" v-model="childData.status">
        <el-radio :label="1">开启</el-radio>
        <el-radio :label="0">关闭</el-radio>
      </el-radio-group>
    </el-form-item> -->
      <el-form-item label="亮度" class="btx">
        <div>
          <!-- <el-input-number style="width:240px;" :min="0" :max="100" :disabled="isDisabled" class="form-item-width" v-model="childData.lightValue"></el-input-number>
                  <div>
                      （0-100）
                  </div> -->
          <el-slider v-model="childData.lightValue" :format-tooltip="formatTooltip" :disabled="isDisabled" show-input />
        </div>

      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handCancle">{{ $t('button.cancel') }}</el-button>
      <el-button type="primary" @click="handCreate">{{
        $t('button.confirm')
      }}</el-button>
    </div>
  </div>
</template>

<script>
import { getLampConfigInfo, issuedInstruction, getLampBri } from '@/api/PMS/LightManage/LightEquipment/management'
export default {
  props: {
    defaultChnno: {
      default: null
    },
    keyValue: {},
    showDialog: {}
  },
  data() {
    return {
      chnNoValue: '',
      deviceName: '',
      childData: {
        lightValue: 0,
        chnNo: [],
        commandTypeVo: {
          cmdCallbackUri: '',
          cmdCode: 1025,
          cmdName: '',
          cmdType: 0,
          cmdUri: '',
          commandTypeId: 0,
          isShow: 0,
          modelId: 0
        }
        // cmdOption: 16//1：重启设备，2：恢复出厂设备，4：参数配置，8：查询状态 16：开关灯
      },
      options: [],
      isDisabled: false
      //   rules: {
      //   deviceParentName: [{ required: true, message: '请选择所属杆柱', trigger: 'change' }],
      //   brandName: [{ required: true, message: '请选择设备厂商', trigger: 'change' }],
      //   deviceIp: [{ required: true, message: '请输入设备Ip', trigger: 'blur' }],
      //   devicePort: [{ required: true, message: '请输入设备端口', trigger: 'blur' }],
      //   netWorkAddress: [{ required: true, message: '请输入网络地址', trigger: 'blur' }]
      // },
    }
  },
  watch: {
    showDialog(n, o) {
      if (n) {
        this.$refs.dataForm.resetFields()
        this.init()
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    formatTooltip(val) {
      return val + '%'
    },
    init() {
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      this.childData.lightValue = 0
      this.childData.chnNo = []
      this.chnNoValue = this.defaultChnno ? ~~this.defaultChnno : ''

      const query = { deviceId: this.keyValue }
      getLampConfigInfo(query).then(res => {
        // this.childData = res.data.data
        this.deviceName = res.data.data.deviceName
        this.options = res.data.data.childDeviceVoList
        this.childData.deviceIds = []
        this.childData.deviceIds.push(res.data.data.deviceId)
      })
      if (this.chnNoValue) this.searchBir()
    },
    // changeStatus(val) {
    //   if (val === 0) {
    //     this.childData.lightValue = 0
    //     this.isDisabled = true
    //   } else {
    //     this.childData.lightValue = 50
    //     this.isDisabled = false
    //   }
    // },
    handCreate() {
      this.childData.chnNo = [this.chnNoValue]
      console.log(this.childData.chnNo)
      if (this.childData.chnNo.length === 0) {
        this.$message.error('请选择通道')
        return
      }
      if (this.childData.lightValue === '' || this.childData.lightValue === undefined || this.childData.lightValue === null) {
        this.$message.error('请输入亮度')
        return
      }
      issuedInstruction(this.childData).then(res => {
        if (res.data.code === 200) {
          this.$emit('handCancle', true)
          this.Alert({ alertType: 'update' })
        }
      })
    },
    handCancle() {
      this.$emit('handCancle', false)
    },
    searchBir() {
      getLampBri({
        deviceId: this.keyValue,
        chnno: this.chnNoValue
      }).then(res => {
        this.childData.lightValue = res.data.data.bri
      })
    }
  }
}
</script>

<style>
.btx .el-form-item__label:before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}
</style>
