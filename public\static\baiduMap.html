<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="initial-scale=1.0, user-scalable=no, width=device-width"
    />
    <title>图片图层</title>
    <!-- 百度地图API -->
    <script
      type="text/javascript"
      src="https://api.map.baidu.com/api?v=3.0&type=webgl&ak=q8tOYX9ShNbUBuDzWurOjhr8UyDcnYvF"
    ></script>
    <script src="https://unpkg.com/@bmapgl-plugin/cluster"></script>
    <!-- 百度地图聚合插件 -->
    <!-- <script type="text/javascript"
        src="https://api.map.baidu.com/library/MarkerClusterer/1.2/src/MarkerClusterer_min.js"></script> -->
    <!--     <script type="text/javascript" src="https://api.map.baidu.com/library/TextIconOverlay/1.2/src/TextIconOverlay_min.js"></script> -->
    <script src="/static/js/config.js"></script>
    <script src="/static/js/mapStyle.js"></script>
    <style>
      html,
      body {
        width: 100%;
        height: 100%;
        margin: 0;
        font-family: "微软雅黑";
        overflow: hidden;
      }

      .amap-logo,
      .amap-copyright {
        visibility: hidden;
      }

      #allmap {
        width: 100%;
        height: 100%;
      }

      #r-result {
        width: 100%;
        margin-top: 5px;
      }

      #mapAdress {
        position: absolute;
        background: white;
        display: none;
      }

      #deviceList {
        position: absolute;
        display: none;
      }

      .anchorBL {
        display: none;
      }

      p {
        margin: 5px;
        font-size: 14px;
      }

      .donghua1 {
        height: 32px;
        width: 32px;
        position: relative;
      }

      .donghua1:hover {
        animation: rotate 1s linear infinite;
      }

      .donghua1::before {
        content: "";
        height: 8px;
        width: 32px;
        background: #000;
        opacity: 0.8;
        border-radius: 50%;
        position: absolute;
        top: 35px;
        left: 0;
        animation: shadow 1s linear infinite;
      }

      .donghua2 {
        width: 32px;
        height: 32px;
        margin: 15px 0;
        animation: breath 0.8s infinite;
        animation-direction: alternate;
        -webkit-animation-direction: alternate;
        /* -webkit-transition: all 1s ease;
			-moz-transition: all 1s ease;
			-o-transition: all 1s ease; */
      }

      .donghua3 {
        height: 32px;
        width: 32px;
        position: relative;
        animation: breath 1.2s infinite;
      }

      .boxDetail {
        background-color: rgba(15, 61, 103, 0.85); /* 聚合弹窗保持中等透明度 */
        color: white;
        font-size: 0.7vw;
        line-height: 25px;
        white-space: nowrap;
        position: absolute;
        bottom: 45px;
        left: -6.5vw;
        border: 1px solid #377aff; /* 保持原有边框颜色 */
        border-radius: 0.3vw;
        padding: 0.3vw 0.7vw;
        z-index: 200;
        box-shadow: 0 2px 8px rgba(55, 122, 255, 0.2); /* 添加较淡的阴影效果 */
      }
      .deviceDetail {
        background: #0f3d67;
        background-color: rgba(15, 61, 103, 0.95); /* 提高透明度从0.8到0.95，使其更不透明 */
        color: white;
        font-size: 0.7vw;
        line-height: 25px;
        white-space: nowrap;
        position: absolute;
        bottom: 45px;
        /* left: -6.5vw; */
        border: 1px solid #00D4FF; /* 调整边框颜色为科技蓝，与区域边框保持一致但更突出 */
        border-radius: 0.3vw;
        padding: 0.3vw;
        z-index: 101;
        box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3); /* 添加蓝色阴影效果，增强科技感 */
      }

      .closeBtn {
        color: white;
        cursor: pointer;
      }

      .borrow {
        position: absolute;
        bottom: -6px;
        left: 50%;
        margin-right: 3px;
        border-top-color: #ebeef5;
        border-bottom-width: 0;
        border-width: 6px;
        filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
      }

      .borrow:after {
        bottom: -6px;
        margin-left: -11px;
        border-top-color: #0f3d67 !important;
        border-bottom-width: 0;
        content: " ";
        border-width: 6px;
        position: absolute;
        display: block;
        width: 0;
        height: 0;
        border-color: transparent;
        border-style: solid;
      }

      .borrow3 {
        position: absolute;
        bottom: -6px;
        left: 55%;
        margin-right: 3px;
        border-top-color: #ebeef5;
        border-bottom-width: 0;
        border-width: 6px;
        filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
      }

      .borrow3:after {
        bottom: -6px;
        margin-left: -11px;
        border-top-color: #00ff97 !important;
        border-bottom-width: 0;
        content: " ";
        border-width: 6px;
        position: absolute;
        display: block;
        width: 0;
        height: 0;
        border-color: transparent;
        border-style: solid;
      }

      .borrow2 {
        position: absolute;
        top: -6px;
        left: 50%;
        margin-right: 3px;
        border-bottom-color: #ebeef5;
        border-top-width: 0;
        border-width: 6px;
        filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
      }

      .borrow2:after {
        bottom: -6px;
        margin-left: -11px;
        border-bottom-color: #0f3d67 !important;
        border-top-width: 0;
        content: " ";
        border-width: 6px;
        position: absolute;
        display: block;
        width: 0;
        height: 0;
        border-color: transparent;
        border-style: solid;
      }

      .lightGy {
        /* animation: myfirst 1.5s infinite;
            box-shadow: 0px -15px 20px #fc780f;
            border-radius: 50%; */
      }

      .BMap_mask {
        z-index: -1 !important;
      }

      .animate {
        -webkit-animation: ripple 4s linear infinite alternate;
        animation: ripple 4s linear infinite alternate;
      }

      @keyframes ripple {
        0% {
          -webkit-transform: scale(0.8);
          transform: scale(0.8);
        }

        100% {
          -webkit-transform: scale(1.2);
          transform: scale(1.2);
          background-color: transparent;
        }
      }

      @keyframes shadow {
        0%,
        100% {
          transform: scaleX(1);
        }

        50% {
          transform: scaleX(1.2);
        }
      }

      @keyframes rotate {
        0% {
          transform: translateY(0);
        }

        25% {
          transform: translateY(5px);
        }

        50% {
          transform: translateY(10px) scale(1.1, 0.9);
        }

        75% {
          transform: translateY(5px);
        }

        100% {
          transform: translateY(0);
        }
      }

      @keyframes breath {
        0% {
          width: 32px;
          height: 32px;
          margin-left: 0px;
        }

        25% {
          width: 36px;
          height: 36px;
          margin-left: -2.5px;
        }

        50% {
          width: 40px;
          height: 40px;
          margin-left: -5px;
        }

        75% {
          width: 36px;
          height: 36px;
          margin-left: -2.5px;
        }

        100% {
          width: 32px;
          height: 32px;
          margin-left: 0px;
        }
      }

      @keyframes myfirst {
        10% {
          transform: scale(1);
        }

        100% {
          transform: scale(8);
        }
      }

      @keyframes fadein {
        0% {
          opacity: 0;
        }

        100% {
          opacity: 1;
        }
      }

      @keyframes zoomin {
        0% {
          transform: scale(0);
        }

        100% {
          transform: scale(1);
        }
      }
    </style>
  </head>

  <body>
    <div id="allmap"></div>
    <div id="mapAdress"></div>
    <div id="deviceList"></div>
    <script>
      // 确保DOM和百度地图API完全加载后再初始化
      window.onload = function() {
        //获取地址栏参数
        function getUrlParam(name) {
          var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
          var r = window.location.search.substr(1).match(reg);
          if (r != null) return unescape(r[2]);
          return null;
        }

        // 初始化变量
        var markerList = [];
        var maskLayer = undefined;
        var fromView = getUrlParam("from"); //是否来自大屏
        var center = window.config
          ? window.config.MAP_CENTER
          : [116.404, 39.915];
        var zoom = window.config ? window.config.MAP_ZOOM : 13;
        var markerClusterer = null;
        var boxList = [];
        var deviceTypeList = [];
        var powerMapLayer = null; //环网地图  电力地图
        var boxList = []; //聚合点的悬浮展示弹窗

        // 坐标转换函数：WGS84 -> BD09（直接二级转换）
        // 注意：此处原本的gcj02ToBd09已被移动到下方统一的坐标转换区域

        // 初始化地图
        var map = new BMapGL.Map("allmap");
        // 根据配置设置中心点和缩放级别
        var centerPoints = new BMapGL.Point(center[0], center[1]);
        map.centerAndZoom(centerPoints, zoom);
        map.setMapStyleV2({ styleJson: mapStyle });
        map.enableScrollWheelZoom(); // 允许滚轮缩放
        map.enableDragging(); //允许地图拖拽

        // 添加地图控件
        //map.addControl(new BMapGL.NavigationControl());
        // 3D导航控件（包含指南针和缩放控件）
        //map.addControl(new BMapGL.NavigationControl3D());
        // 添加标记函数 当前版本可用  非marker 使用overlay
        function addMarker(lng, lat, data, type, chooseId) {
          // 创建坐标点 - 直接WGS84转BD09（二级转换）
          let bdPoint = wgs84ToBd09(parseFloat(lng), parseFloat(lat));
          let point = new BMapGL.Point(bdPoint[0], bdPoint[1]);
          let marker = null;
          // 如果有自定义内容，使用CustomOverlay
          if (data) {
            let customContent = createCustomMarker(data, type, chooseId);
            // 创建自定义覆盖物类
            class CustomMarker extends BMapGL.Overlay {
              constructor(point, content, deviceData) {
                super();
                this._point = point;
                this._content = content;
                this._deviceData = deviceData; // 添加设备数据保存
              }

              initialize(map) {
                this._map = map;
                this._div = this._content;
                map.getPanes().markerPane.appendChild(this._div);
                return this._div;
              }

              draw() {
                const position = this._map.pointToOverlayPixel(this._point);
                this._div.style.left = position.x - 20 + "px";
                this._div.style.top = position.y - 20 + "px";
                this._div.style.position = "absolute";
              }
            }

            marker = new CustomMarker(point, customContent, data);
          } else {
            // 如果没有自定义内容，使用普通标记
            marker = new BMapGL.Marker(point);
          }

          //map.addOverlay(marker);
          markerList.push(marker);
          return marker;
        }

        // 创建自定义标记内容
        function createCustomMarker(deviceData, type, chooseId) {
          var div = document.createElement("div");
          div.className = "device_detail";
          div.id = "deviceBox_" + deviceData.deviceId;
          div.style.position = "absolute";
          div.style.cursor = "pointer";

          if (chooseId === deviceData.deviceId) {
            div.style.background = "url(./img/border.png) round";
          }

          div.style.display = "flex";
          div.style.flexDirection = "column";
          div.style.alignItems = "center";

          var iconUrl = "CAM_1.png";

          // 添加详情框
          if (fromView == 1 && type === 1) {
            iconUrl = getIconName(deviceData);

            var detailDiv = document.createElement("div");
            var htmlStr =
              '<div id="deviceDetail_' +
              deviceData.deviceId +
              '" style="display:none; transform: translateX(-50%)" class="deviceDetail">';
            htmlStr +=
              '<div style="border-bottom: 1px solid #f3e47124;"><span style="font-weight:bolder;font-size: 0.8vw;color:#03EDFB;">' +
              deviceData.deviceName +
              "</span></div>";

            if (deviceData.onlineState === 1 && deviceData.alarmState !== 1) {
              htmlStr +=
                '<div>状态：<span style="color:#09E42B;">在线</span></div>';
            } else if (deviceData.onlineState == 0) {
              htmlStr +=
                '<div>状态：<span style="color:#B1B1B1;">离线</span></div>';
            } else if (
              deviceData.onlineState == 1 &&
              deviceData.alarmState === 1
            ) {
              htmlStr +=
                '<div>状态：<span style="color:#FF0000;">告警</span></div>';
            }

            if (deviceData.offlineReason) {
              htmlStr += `<div>原因：<span style="color:#B1B1B1;">${
                deviceData.offlineReason
              }</span></div>`;
            }

            htmlStr += '<div x-arrow="" class="borrow"></div>';
            htmlStr +=
              '<div x-arrow="" class="borrow2" style="display: none"></div></div>';
            detailDiv.innerHTML = htmlStr;
            div.appendChild(detailDiv);
          }

          // 设置图标
          const url = `/cmdbService/images/${iconUrl}`;
          div.innerHTML += `<div style='height: 40px;width: 40px;border-radius: 50%;background: url("${url}");background-size: ${
            iconUrl.indexOf("_") !== -1
              ? "40px 40px;"
              : "60px 60px;background-position: -10px -10px;"
          }background-repeat: no-repeat' class="lightGy"></div>`;

          // 点击事件
          div.onmousedown = function(e) {
            if (e.button === 0) {
              if (fromView == 1 && type === 1) {
                window.parent.postMessage(
                  { type: "getDevicePoint", data: deviceData },
                  "*"
                );
              }
            } else if (e.button === 2) {
              if (
                deviceData.deviceType === "587" ||
                deviceData.deviceType === "586"
              ) {
                window.parent.postMessage(
                  { type: "getPowerMap", data: deviceData },
                  "*"
                );
              }
            }
          };

          // 悬浮事件
          div.onmouseover = function(e) {
            var dom = document.getElementById(
              "deviceDetail_" + deviceData.deviceId
            );
            if (showDeviceName == 1) {
              return;
            } else {
              if (dom && (fromView == 1 && type === 1)) {
                dom.style.display = "block";

                if (
                  dom.parentNode.parentNode.parentNode.parentNode.offsetTop <=
                  dom.offsetHeight + 20
                ) {
                  dom.style.bottom = "unset";
                  dom.style.top = "45px";
                  dom.getElementsByClassName("borrow2")[0].style.display =
                    "block";
                  dom.getElementsByClassName("borrow")[0].style.display =
                    "none";
                } else {
                  dom.style.bottom = "45px";
                  dom.style.top = "unset";
                  dom.getElementsByClassName("borrow")[0].style.display =
                    "block";
                  dom.getElementsByClassName("borrow2")[0].style.display =
                    "none";
                }

                dom.parentNode.parentNode.parentNode.parentNode.style.zIndex = 100;
              }
            }
          };

          // 悬浮移除事件
          div.onmouseout = function() {
            var dom = document.getElementById(
              "deviceDetail_" + deviceData.deviceId
            );
            if (showDeviceName == 1) {
              return;
            } else {
              if (dom && (fromView == 1 && type === 1)) {
                dom.style.display = "none";
                dom.parentNode.parentNode.parentNode.parentNode.style.zIndex = 0;
              }
            }
          };

          return div;
        }

        // 图标名称获取函数
        function getIconName(deviceData) {
          // 灯亮度图标
          if (
            deviceData.deviceType === "6" &&
            deviceData.onoff === 1 &&
            deviceData.alarmState === 0 &&
            deviceData.onlineState === 1
          ) {
            if (deviceData.bir >= 0 && deviceData.bir < 20) {
              return "img1.png";
            } else if (deviceData.bir >= 20 && deviceData.bir < 40) {
              return "img2.png";
            } else if (deviceData.bir >= 40 && deviceData.bir < 60) {
              return "img3.png";
            } else if (deviceData.bir >= 60 && deviceData.bir < 80) {
              return "img4.png";
            } else if (deviceData.bir >= 80 && deviceData.bir <= 100) {
              return "img5.png";
            }
          }

          const deviceState =
            deviceData.onlineState === 0
              ? 1
              : deviceData.alarmState === 1
              ? 2
              : 0;
          return deviceTypeList[deviceData.deviceType]
            ? deviceTypeList[deviceData.deviceType][
                ["onlineFileName", "offlineFileName", "alertFileName"][
                  deviceState
                ]
              ]
            : "dingwei";
        }
        // 实现点聚合
        function createMarkerClusterer(markers) {
          // 销毁已有点聚合
          if (markerClusterer) {
            markerClusterer.destroy();
            markerClusterer = null;
          }
          // 记录原始标记，以便在集群变化时控制显示/隐藏
          var originalMarkers = markers.slice();
          // 将markers转换为GeoJSON格式的点数据
          const points = markers.map(marker => {
            let lng, lat;

            // 检查marker类型并获取位置
            if (marker instanceof BMapGL.Marker) {
              // 原生标记，使用getPosition方法
              const position = marker.getPosition();
              lng = position.lng;
              lat = position.lat;
            } else if (marker instanceof BMapGL.Overlay || marker._point) {
              // 自定义覆盖物，使用_point属性
              lng = marker._point.lng;
              lat = marker._point.lat;
            } else {
              console.error("未知类型的标记:", marker);
              // 提供默认值避免报错
              lng = 0;
              lat = 0;
            }
            return {
              type: "Feature",
              properties: {
                // 保存marker的原始信息，用于自定义渲染
                originMarker: marker
              },
              geometry: {
                type: "Point",
                coordinates: [lng, lat]
              }
            };
          });
          // 创建新的点聚合实例
          markerClusterer = new Cluster.View(map, {
            clusterMinPoints: 2, // 最小聚合数量
            clusterMaxZoom: 18, // 最大聚合缩放级别
            updateRealTime: true, // 实时更新
            fitViewOnClick: false, // 点击聚合点时是否自动调整视图
            //isAnimation:true,
            clusterType: [
              [3, null, Cluster.ClusterType.DIS_PIXEL, 35] // 使用像素距离聚合
            ],
            //点输入属性
            clusterMap: props => {
              // 初始化设备数据对象，可以按照设备类型分类
              const deviceData = {
                devices: [] // 存储设备数据的数组
              };
              // 从特征中提取设备数据
              let extractedData = null;
              if (
                props &&
                props.properties &&
                props.properties.originMarker &&
                props.properties.originMarker._deviceData
              ) {
                extractedData = props.properties.originMarker._deviceData;
              } else if (
                props &&
                props.originMarker &&
                props.originMarker._deviceData
              ) {
                extractedData = props.originMarker._deviceData;
              } else if (props && props._deviceData) {
                extractedData = props._deviceData;
              } else {
                console.warn("无法提取设备数据，props结构:", props);
              }

              // 如果找到设备数据，添加到数组中
              if (extractedData) {
                deviceData.devices.push(extractedData);
              } else {
                console.error("未找到设备数据");
              }

              return deviceData;
            },
            //点聚合属性
            clusterReduce: (accumulated, props) => {
              // 如果 props 中有设备数据，添加到 accumulated 中
              // 确保 accumulated 中有 devices 数组
              if (!accumulated.devices) {
                accumulated.devices = [];
                accumulated.rawCount = 0;
              }

              // 累加rawCount，保持与pointCount一致
              accumulated.rawCount += props.rawCount || 1;

              if (props && props.devices && Array.isArray(props.devices)) {
                // 直接合并设备数组，不执行去重
                accumulated.devices = accumulated.devices.concat(props.devices);
              }

              return accumulated;
            },
            // 自定义聚合点样式
            renderClusterStyle: {
              type: Cluster.ClusterRender.DOM,
              style: {
                anchors: [0.5, 0.5], // 锚点位置
                offsetX: 0,
                offsetY: 0
              },
              inject: function(context) {
                const count = context.pointCount || 1;
                //判断是否存在离线告警 进行判断
                let hasOfflineDevice = context.reduces.devices.some(
                  device => device.onlineState === 0
                );
                let hasAlarmDevice = context.reduces.devices.some(
                  device => device.alarmState === 1
                );

                // 创建自定义DOM元素
                const div = document.createElement("div");
                //div.style.background =hasOfflineDevice||hasAlarmDevice?`radial-gradient(50% 50% at 50% 50%, rgba(169,169,169, 0) 0%, rgba(169,169,169, 0.8) 100%)`:`radial-gradient(50% 50% at 50% 50%, rgba(1, 255, 255, 0) 0%, rgba(1, 255, 255, 0.8) 100%)`;
                div.style.background = `radial-gradient(50% 50% at 50% 50%, rgba(1, 255, 255, 0) 0%, rgba(1, 255, 255, 0.8) 100%)`;
                div.style.color = "#ffffff";
                div.style.fontSize = "12px";
                div.style.textAlign = "center";
                div.style.borderRadius = "50%";
                div.style.padding = "10px";
                div.style.display = "flex";
                div.style.justifyContent = "center";
                div.style.alignItems = "center";

                // 根据数字位数调整样式，但保持圆形
                const digitCount = String(count).length;
                const size = Math.max(24, 24 + (digitCount - 3) * 4);
                div.style.width = `${size}px`;
                div.style.height = `${size}px`;
                // 如果数字很大，适当减小字体大小
                if (digitCount > 3) {
                  div.style.fontSize = `${Math.max(
                    8,
                    12 - (digitCount - 3)
                  )}px`;
                }

                div.innerHTML = count;

                return div;
              }
            },
            // 单个点使用原始标记
            renderSingleStyle: {
              type: Cluster.ClusterRender.DOM,
              inject: function(feature) {
                // 返回原始marker
                return feature.originMarker._content;
              }
            }
          });

          // 设置聚合数据
          markerClusterer.setData(points);
          // 添加事件监听
          markerClusterer.on(Cluster.ClusterEvent.CLICK, e => {
            // 只处理聚合点的点击事件，单个点的点击事件由原marker处理
            if (e.isCluster && e.pointCount > 1) {
              // 获取当前缩放级别和最大缩放级别
              const currentZoom = map.getZoom();
              const maxZoom = map.getMaxZoom ? map.getMaxZoom() : 19; // 百度地图默认最大缩放级别是19

              // 检查是否是最大缩放级别
              if (currentZoom >= maxZoom) {
                // 确保聚合中心位置正确
                const center = e.isCluster
                  ? new BMapGL.Point(e.latLng[0], e.latLng[1])
                  : e.marker && e.marker.getPosition();

                showMiniCluster({
                  points: e.reduces.devices,
                  getCenter: () => center
                });
              } else {
                window.parent.postMessage(
                  {
                    type: "showClusterData",
                    data: e.reduces.devices
                  },
                  "*"
                );
              }
              // 阻止事件冒泡，防止触发地图的点击事件
              e.domEvent && e.domEvent.stopPropagation();
            }
          });

          // 将聚合实例赋值给全局变量，以便其他函数可以访问
          window.clusterLayer = markerClusterer;

          markerClusterer.on(Cluster.ClusterEvent.MOUSE_OVER, e => {
            if (e.isCluster && e.pointCount > 1) {
              initBox(e.latLng, e.reduces.devices);
            }
          });

          markerClusterer.on(Cluster.ClusterEvent.MOUSE_OUT, e => {
            // 处理鼠标移出事件
            // 只处理聚合点的移出事件
            if (e.isCluster && e.pointCount > 1) {
              //console.log('聚合点鼠标移出事件', e);
              if (boxList.length > 0) {
                boxList.forEach(marker => {
                  map.removeOverlay(marker);
                });
                boxList = [];
              }
            }
          });

          markerClusterer.on(Cluster.ClusterEvent.CHANGE, e => {
            //console.log('聚合变化事件', e);
            // 处理聚合状态变化事件
            if (boxList.length > 0) {
              boxList.forEach(marker => {
                map.removeOverlay(marker);
              });
              boxList = [];
            }
            // 清理选中框
            /* let lastDiv = document.getElementById("deviceBox_aim");
            if (lastDiv !== null && lastDiv !== undefined) {
              lastDiv.style.background = "";
              map.getPanes().labelPane.removeChild(lastDiv);
            } */
          });

          return markerClusterer;
        }
        // 添加弹框
        function AddBox(data) {
          var div = (this._div = document.createElement("div"));
          
          if (!data || !Array.isArray(data) || data.length === 0) {
            console.error("AddBox: 无效的数据", data);
            div.innerHTML = '<div class="boxDetail">无设备数据</div>';
            return div;
          }
          
          div.id = "BoxList_" + data[0].deviceId;
          div.style.width = "0px";
          div.style.height = "0px";
          div.style.left = "17px";
          div.style.position = "relative";
          // div.style.display = "flex";
          // div.style.flexDirection = "column";
          // div.style.alignItems = "center";
          normal = 0;
          alarm = 0;
          exit = 0;
          if (data.length > 0) {
            data.forEach(item => {
              if (item.onlineState === 1) {
                normal++;
                if (item.alarmState === 1) {
                  alarm++;
                }
              } else if (item.onlineState === 0) {
                exit++;
              }
            });
          }
          //添加详情框
          var htmlStr = "";
          htmlStr += `<div id="pointNum_${
            data[0].deviceId
          }" class="boxDetail" style="position: absolute; left: 0; transform: translateX(-50%) translateY(40px)">`;
          htmlStr +=
            '<div style="color: #80FFFF;">在线：' +
            normal +
            '个</div><div style="color: #FFFFFF;">离线：' +
            exit +
            '个</div><div style="color: #FF451E;">告警：' +
            alarm +
            "个</div>";
          htmlStr += '<div x-arrow="" class="borrow3" ></div>';
          // htmlStr += '<div x-arrow="" class="borrow2" ></div></div>';
          div.innerHTML = htmlStr;
          return div;
        }
        // 初始化设备框
        function initBox(point, clusterList) {
          
          if (!clusterList || !Array.isArray(clusterList) || clusterList.length === 0) {
            console.error("initBox: 聚合列表数据无效", clusterList);
            return;
          }
          
          // 清理之前的标记
          if (boxList) {
            boxList.forEach(marker => {
              map.removeOverlay(marker); // 使用百度地图的 removeOverlay 方法
            });
          }
          const listContent = AddBox(clusterList);
          class deviceBoxList extends BMapGL.Overlay {
            constructor(point, content) {
              super();
              this._point = point;
              this._content = content;
            }

            initialize(map) {
              this._map = map;
              this._div = this._content;
              map.getPanes().markerPane.appendChild(this._div);
              return this._div;
            }

            draw() {
              const position = this._map.pointToOverlayPixel(this._point);
              this._div.style.left = position.x + "px";
              this._div.style.top = position.y - 20 + "px";
              this._div.style.position = "absolute";
            }
          }

          let marker = new deviceBoxList(
            new BMapGL.Point(point[0], point[1]),
            listContent
          );
          // 将标记添加到地图
          map.addOverlay(marker);

          // 将标记存储到 boxList 中
          boxList = [marker];

          // 清理 deviceBox
          deviceBox = null;
        }
        // 更新地图标记
        // 百度地图API 可用
        function updateMap() {
          for (let i = 0; i < markerList.length; i++) {
            map.removeOverlay(markerList[i]);
          }
          markerList = [];
        }
        //不同区域显示不同底图颜色
        var curArea = { text: null, content: null };
        var curRoad = null;

        //添加面
        function addArea(area) {
          // 移除已有的区域和文本
          if (curArea.text) {
            map.removeOverlay(curArea.text);
          }
          if (curArea.content) {
            map.removeOverlay(curArea.content);
          }

          // 如果有中心点，添加区域名称
          if (area.markerCenterPoint) {
            AddAreaName(area);
          }

          // 创建多边形区域
          /*  var points = JSON.parse(area.point).map(ele => {
                     return new BMapGL.Point(ele[0], ele[1]);
                 }); */
          // 在创建点时进行坐标转换 - 直接WGS84转BD09（二级转换）
          var points = JSON.parse(area.point).map(ele => {
            var bdCoord = wgs84ToBd09(ele[0], ele[1]);
            return new BMapGL.Point(bdCoord[0], bdCoord[1]);
          });

          curArea.content = new BMapGL.Polygon(points, {
            strokeColor: "#00D4FF", // 边框颜色 - 科技蓝色
            strokeWeight: 2, // 边框宽度
            strokeOpacity: 0.9, // 边框透明度 - 提高可见度
            strokeStyle: "dashed", // 边框样式 - 虚线
            fillColor: "#0066CC", // 填充颜色 - 深蓝色
            fillOpacity: 0.25 // 填充透明度 - 降低透明度营造科技感
          });

          // 添加多边形到地图
          map.addOverlay(curArea.content);

          // 调整视图以适应多边形
          var viewport = map.getViewport(points);
          map.flyTo(viewport.center, viewport.zoom);
        }

        // 添加区域名称
        function AddAreaName(data) {
          var centerPoint = JSON.parse(data.markerCenterPoint);
          
          // 添加坐标转换：直接WGS84转BD09（二级转换）
          var bdCoord = wgs84ToBd09(centerPoint[0], centerPoint[1]);
          var point = new BMapGL.Point(bdCoord[0], bdCoord[1]);

          // 创建文本标注
          var label = new BMapGL.Label(data.markerName || data.deviceName, {
            position: point,
            offset: new BMapGL.Size(-40, 0)
          });

          // 设置样式
          label.setStyle({
            color: "#00FFFF", // 青蓝色文字 - 科技感
            fontSize: "0.8vw",
            border: "1px solid #00D4FF", // 添加边框
            backgroundColor: "rgba(0, 102, 204, 0.3)", // 半透明蓝色背景
            textAlign: "center",
            width: "100px",
            height: "40px",
            lineHeight: "40px",
            borderRadius: "4px", // 圆角
            display: "flex",
            flexDirection: "column",
            alignItems: "center"
          });

          curArea.text = label;
          map.addOverlay(label);
        }
        // 点击地图事件
        map.addEventListener("click", function(e) {
          if (fromView === 1) {
            // 发送点击位置消息到父页面
            window.parent.postMessage({ type: "hiddenSearchDevice" }, "*");
          }

          // 添加标记
          if (window.setPoint == 1 || fromView == "test") {
            updateMap();
            var marker = addMarker(e.latlng.lng, e.latlng.lat, null, 0, null);

            // 调用父页面方法(传值回去)
            var data = {
              type: "getPoint",
              longitude: e.latlng.lng,
              latitude: e.latlng.lat,
              alt: 0
            };
            window.parent.postMessage(data, "*");
          }
        });

        // 地图加载完成事件
        var firstLoad = true;
        map.addEventListener("tilesloaded", function() {
          // 只在首次加载完成时通知父页面
          if (firstLoad) {
            window.parent.postMessage({ type: "loadOver" }, "*");
            firstLoad = false;
          }
        });

        // 监听事件
        var showDeviceName = null;
        window.addEventListener("message", function(e) {
          var type = e.data.type;
          // console.log(type)
          // 修改数据还原记录坐标点事件
          // 已替换百度地图API  暂未发现应用文件
          if (type === "editPoint") {
            //使用百度地图API 进行改写
            // 清除现有标记
            updateMap();

            // 如果需要坐标转换（从GCJ02到BD09） 创建新的坐标点
            let convertedPoint = gcj02ToBd09(e.data.longitude, e.data.latitude);
            let point = new BMapGL.Point(convertedPoint[0], convertedPoint[1]);
            // 创建新标记并添加到地图
            var marker = new BMapGL.Marker(point, {
              offset: new BMapGL.Size(-20, -20),
              enableDragging: false
            });

            // 设置标记的自定义内容  当前无样式的marker就是默认marker
            //marker.setContent(AddDevice(point, null, -1, 0));

            // 将标记添加到地图和标记列表
            markerList.push(marker);
            map.addOverlay(marker);

            // 平移地图中心点
            if (e.data.longitude === 0 && e.data.latitude === 0) {
              map.flyTo(new BMapGL.Point(center[0], center[1]));
            } else {
              map.flyTo(point);
            }
          }
          // 百度地图API 可用
          if (type === "reload") {
            // 初始化地图配置，清理所有图标，将地图重新放置于中心点
            updateMap();
            map.centerAndZoom(centerPoints, zoom);
            setPoint = 0;
          }
          // 暂未发现应用文件
          if (type === "setPoint") {
            setPoint = 1;
          }
          // 设置显示设备名称
          // 百度地图API 可用
          if (type === "setShowDeviceName") {
            showDeviceName = e.data.data;
            // 获取所有 device_detail 元素
            var deviceDetails = document.getElementsByClassName("deviceDetail");
            if (deviceDetails) {
              for (var i = 0; i < deviceDetails.length; i++) {
                if (showDeviceName === 1) {
                  // 如果 showDeviceName 为 1，显示所有详情框
                  deviceDetails[i].style.display = "block";
                  // 调整箭头显示
                  var borrow = deviceDetails[i].getElementsByClassName(
                    "borrow"
                  )[0];
                  var borrow2 = deviceDetails[i].getElementsByClassName(
                    "borrow2"
                  )[0];
                  if (borrow) borrow.style.display = "block";
                  if (borrow2) borrow2.style.display = "none";
                } else {
                  // 否则恢复默认的悬浮显隐策略
                  deviceDetails[i].style.display = "none";
                }
              }
            }
          }
          // 从大屏进入 初始化地图 显示设备
          // 百度地图API 可用
          if (type === "fromView") {
            // 处理来自大屏的数据  改写后的代码
            fromView = 1;
            updateMap();

            // 处理设备数据显示
            var deviceData = e.data.data;
            var dataList = e.data.dataList;
            var markers = [];

            if (e.data.showType === 0) {
              for (var i = 0; i < deviceData.length; i++) {
                var sysType = deviceData[i].sysType;
                var devices = dataList[sysType];

                if (devices && devices.length > 0) {
                  for (var j = 0; j < devices.length; j++) {
                    var device = devices[j];
                    if (device.longitude && device.latitude) {
                      var marker = addMarker(
                        device.longitude,
                        device.latitude,
                        device,
                        1,
                        e.data.chooseId
                      );
                      markers.push(marker);
                    }
                  }
                }
              }

              // 创建点聚合
              createMarkerClusterer(markers);
              //解决设备详情弹窗忽闪的问题
              let deviceDetails = document.getElementsByClassName(
                "deviceDetail"
              );
              if (deviceDetails) {
                for (var i = 0; i < deviceDetails.length; i++) {
                  if (showDeviceName === 1) {
                    // 如果 showDeviceName 为 1，显示所有详情框
                    deviceDetails[i].style.display = "block";
                    // 调整箭头显示
                    var borrow = deviceDetails[i].getElementsByClassName(
                      "borrow"
                    )[0];
                    var borrow2 = deviceDetails[i].getElementsByClassName(
                      "borrow2"
                    )[0];
                    if (borrow) borrow.style.display = "block";
                    if (borrow2) borrow2.style.display = "none";
                  } else {
                    // 否则恢复默认的悬浮显隐策略
                    deviceDetails[i].style.display = "none";
                  }
                }
              }
              // 设置视图以包含所有标记
              if (e.data.isZoom) {
                map.setViewport(
                  markers.map(function(marker) {
                    return marker.getPosition();
                  })
                );
              }
            }
          }
          // 百度地图API 可用
          if (type === "backHome") {
            /* map.panTo(center);
                    map.setZoom(zoom) */
            //替换为 百度地图API
            map.centerAndZoom(centerPoints, zoom);
          }
          // 暂时未进行更改
          let clearTimeoutId = null;
          if (type === "selectDevice") {
            // 移除聚合点遮罩层
            let maskLayer = document.querySelector(".cluster-mask-layer");
            if (maskLayer) {
              maskLayer.remove();
            }
            //尝试使用百度地图API
            // 直接将WGS84坐标转换为BD09坐标（二级转换）
            let bdPoint = wgs84ToBd09(
              parseFloat(e.data.data.longitude),
              parseFloat(e.data.data.latitude)
            );
            let aimPoint = new BMapGL.Point(bdPoint[0], bdPoint[1]);
            //let aimPoint = new BMapGL.Point(parseFloat(e.data.data.longitude),parseFloat(e.data.data.latitude));
            // 2. 平移地图到设备位置
            setTimeout(() => {
              map.flyTo(aimPoint, 18.5);
              let aimPosition = map.pointToOverlayPixel(aimPoint);

              // 创建自定义高亮图层
              class HighlightOverlay extends BMapGL.Overlay {
                constructor(point) {
                  super();
                  this._point = point;
                }

                initialize(map) {
                  this._map = map;
                  this._div = document.createElement("div");
                  this._div.id = "deviceBox_aim";
                  this._div.style.position = "absolute";
                  this._div.style.width = "44px";
                  this._div.style.height = "44px";
                  this._div.style.background = "url(./img/border.png) round";
                  this._div.style.zIndex = "1";
                  map.getPanes().labelPane.appendChild(this._div);
                  return this._div;
                }

                draw() {
                  const position = this._map.pointToOverlayPixel(this._point);
                  this._div.style.left = position.x - 22 + "px";
                  this._div.style.top = position.y - 22 + "px";
                }
              }

              // 移除已存在的高亮图层
              const existingHighlight = document.getElementById(
                "deviceBox_aim"
              );
              if (existingHighlight) {
                existingHighlight.remove();
              }

              // 创建并添加新的高亮图层
              const highlightOverlay = new HighlightOverlay(aimPoint);
              map.addOverlay(highlightOverlay);
            }, 1000);
            // 清理选中框
            if (clearTimeoutId) {
              clearTimeout(clearTimeoutId);
              clearTimeoutId = null;
            }
            clearTimeoutId = setTimeout(() => {
              let lastDiv = document.getElementById("deviceBox_aim");
              if (lastDiv !== null && lastDiv !== undefined) {
                lastDiv.style.background = "";
                map.getPanes().labelPane.removeChild(lastDiv);
              }
            }, 5000);
          }

          // 重置选择 初始时会加载 暂时不需要更改 百度地图API 可用
          if (type === "resetChoose") {
            let lastDiv = document.getElementById("deviceBox_aim");
            if (lastDiv !== null && lastDiv !== undefined) {
              lastDiv.style.background = "";
              map.getPanes().labelPane.removeChild(lastDiv);
            }
          }
          // 百度地图API 可用
          if (type === "closeMap") {
            map.destroy();
          }
          // 百度地图API 可用
          if (type === "setAreaColor") {
            addArea(e.data.area);
          }
          //百度地图API 可用
          if (type === "clearMap") {
            //map.clearMap()
            map.clearOverlays(); //替换为百度地图
            curArea = { text: null, content: null };
            curRoad = null;
          }
          //道路选中
          /* if (type === 'setPolyline') {
                    if (e.data.data.point != null) {
                        curRoad && map.remove(curRoad)
                        var pathInfo = JSON.parse(e.data.data.point)
                        // var pathInfo = [[113.222194,23.083962],[113.222205,23.08379],[113.221878,23.08227]]
                        curRoad = new AMap.Polyline({
                            map: map, //指定目标地图
                            path: pathInfo, //折线的节点坐标数组
                            showDir: true, //是否延路径显示白色方向箭头,默认false(Canvas绘制时有效,建议折线宽度大于6时使用)
                            strokeColor: "#F97A16", //线颜色
                            strokeOpacity: 1, //线透明度
                            strokeWeight: 6, //线宽
                            // strokeStyle: "solid"  //线样式
                        });
                        map.setFitView(curRoad)
                    }
                } */
          //使用百度地图API
          if (type === "setPolyline") {
            if (e.data.data.point != null) {
              // 如果已存在路线，先移除
              if (curRoad) {
                map.removeOverlay(curRoad);
              }

              // 解析路径点数据
              var pathInfo = JSON.parse(e.data.data.point);

                        // 将坐标点转换为百度地图点对象 - 直接WGS84转BD09（二级转换）
          var points = pathInfo.map(point => {
            var bdPoint = wgs84ToBd09(point[0], point[1]);
            return new BMapGL.Point(bdPoint[0], bdPoint[1]);
          });

              // 创建折线对象
              curRoad = new BMapGL.Polyline(points, {
                strokeColor: "#F97A16", // 线颜色
                strokeOpacity: 1, // 线透明度
                strokeWeight: 6, // 线宽
                strokeStyle: "solid", // 线样式
                enableMassClear: true, // 是否在清除覆盖物时，一起清除
                enableEditing: false, // 是否启用线编辑
                enableClicking: true, // 是否响应点击事件
                geodesic: false // 是否绘制大地线
              });

              // 添加折线到地图
              map.addOverlay(curRoad);

              // 调整视图以适应折线
              var viewport = map.getViewport(points);
              map.flyTo(viewport.center, viewport.zoom);
            }
          }
          // 供电地图
          /* if (type === 'showPowerMap') {
                    powerMapLayer && map.remove(powerMapLayer)
                    if (e.data.data == null || e.data.data.length === 0) return
                    let lines = e.data.data.map(i => {
                        let path = JSON.parse(i.pathPoint)
                        let line = new AMap.Polyline({
                            path, //折线的节点坐标数组
                            strokeColor: i.color || "#ebca26", //线颜色
                            strokeOpacity: 1, //线透明度
                            strokeWeight: 2, //线宽
                            cursor: 'pointer',
                        });
                        if (e.data.deviceType === '587') {
                            line.on('click', e => {
                                let text = new AMap.Text({
                                    map,
                                    text: i.lineName,
                                    position: e.lnglat
                                })
                                setTimeout(() => { text.remove() }, 3000)
                            })
                        }
                        return line
                    })
                    powerMapLayer = new AMap.OverlayGroup(lines)
                    map.add(powerMapLayer)
                    map.setFitView(lines)
                } */
          // 环网地图
          /* if (type === 'showTransformerMap') {
                    powerMapLayer && map.remove(powerMapLayer)
                    transformerMapLayer && map.remove(transformerMapLayer)
                    if (!e.data.data) return
                    let lines = e.data.data.lineList.map(i => {
                        let path = JSON.parse(i.pathPoint)
                        let line = new AMap.Polyline({
                            path, //折线的节点坐标数组
                            strokeColor: i.color || "#ebca26", //线颜色
                            strokeOpacity: 1, //线透明度
                            strokeWeight: 2, //线宽
                            strokeStyle: i.style,
                            strokeDasharray: [3, 3]
                        });
                        return line
                    })
                    let points = e.data.data.powerDistributionRoomList.map(i => {
                        let center = JSON.parse(i.pathPoint)[0]
                        let point = new AMap.Circle({
                            center, //折线的节点坐标数组
                            radius: 10,
                            fillOpacity: 1,
                            fillColor: i.color || "#ebca26", //线颜色
                            strokeWeight: 0, //线宽
                        });
                        return point
                    })
                    let markers = e.data.data.powerDistributionRoomList.map(i => {
                        let position = JSON.parse(i.pathPoint)[0]
                        let marker = new AMap.Marker({
                            position,
                            anchor: 'bottom-center',
                            topWhenClick: true,
                            offset: [0, -10],
                            content: `<div  onclick="window.parent.postMessage({ type: 'getTransformerMap', data: ${e.data.showType === 'detail' ? undefined : i.networkId} }, '*')"
                                        style="height:30px;line-height:30px;white-space:nowrap;font-size:16px;
                                        padding:0 7px;background-color:rgba(15,61,103,0.8);
                                        border-radius:6px;border: 2px solid ${i.color};color:${i.color}">
                                        ${i.name.slice(0, 2)}
                                    </div>`
                        });
                        return marker
                    })
                    transformerMapLayer = new AMap.OverlayGroup([...lines, ...points, ...markers])
                    map.add(transformerMapLayer)
                    // map.panTo(center);
                    // map.setZoom(zoom)
                    map.setFitView([...lines], false, [70, 10, 350, 350])
                } */
          // 设备类型列表 在初次加载时会调用  不涉及地图API  当前可以复用
          if (type === "deviceTypeList") {
            deviceTypeList = e.data.data;
            const ary = [];
            for (let index = 0; index < deviceTypeList.length; index++) {
              const element = deviceTypeList[index];
              ary[parseInt(element.deviceType)] = element;
            }
            deviceTypeList = ary;
          }
        });


        // 坐标转换
        const PI = 3.1415926535897932384626;
        const a = 6378245.0;
        const ee = 0.00669342162296594323;

        /**
         * WGS84直接转换为BD09坐标（二级转换，避免累积误差）
         * @param lng WGS84经度
         * @param lat WGS84纬度
         * @returns {number[]} [BD09经度, BD09纬度]
         */
        function wgs84ToBd09(lng, lat) {
          var wgs_lat = +lat;
          var wgs_lng = +lng;
          
          // 如果在国外，直接返回原坐标
          if (out_of_china(wgs_lng, wgs_lat)) {
            return [wgs_lng, wgs_lat];
          }
          
          // Step 1: WGS84 → GCJ02
          var dlat = transformlat(wgs_lng - 105.0, wgs_lat - 35.0);
          var dlng = transformlng(wgs_lng - 105.0, wgs_lat - 35.0);
          var radlat = (wgs_lat / 180.0) * PI;
          var magic = Math.sin(radlat);
          magic = 1 - ee * magic * magic;
          var sqrtmagic = Math.sqrt(magic);
          dlat = (dlat * 180.0) / (((a * (1 - ee)) / (magic * sqrtmagic)) * PI);
          dlng = (dlng * 180.0) / ((a / sqrtmagic) * Math.cos(radlat) * PI);
          var gcj_lng = wgs_lng + dlng;
          var gcj_lat = wgs_lat + dlat;
          
          // Step 2: GCJ02 → BD09
          const x_PI = (3.14159265358979324 * 3000.0) / 180.0;
          const z = Math.sqrt(gcj_lng * gcj_lng + gcj_lat * gcj_lat) + 0.00002 * Math.sin(gcj_lat * x_PI);
          const theta = Math.atan2(gcj_lat, gcj_lng) + 0.000003 * Math.cos(gcj_lng * x_PI);
          const bd_lng = parseFloat((z * Math.cos(theta) + 0.0065).toFixed(6));
          const bd_lat = parseFloat((z * Math.sin(theta) + 0.006).toFixed(6));
          
          return [bd_lng, bd_lat];
        }

        // GCJ02坐标转百度坐标（保留此函数用于其他可能的用途）
        function gcj02ToBd09(lng, lat) {
          const x_PI = (3.14159265358979324 * 3000.0) / 180.0;
          const z =
            Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * x_PI);
          const theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * x_PI);
          const bd_lng = (z * Math.cos(theta) + 0.0065).toFixed(6);
          const bd_lat = (z * Math.sin(theta) + 0.006).toFixed(6);
          return [bd_lng, bd_lat];
        }

        // 百度坐标转GCJ02坐标
        function bd09ToGcj02(lng, lat) {
          const x_PI = (3.14159265358979324 * 3000.0) / 180.0;
          const x = lng - 0.0065;
          const y = lat - 0.006;
          const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_PI);
          const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_PI);
          const gcj_lng = (z * Math.cos(theta)).toFixed(6);
          const gcj_lat = (z * Math.sin(theta)).toFixed(6);
          return [gcj_lng, gcj_lat];
        }

        /**
         * WGS84转GCj02（保留此函数用于其他可能的用途）
         * @param lng
         * @param lat
         * @returns {*[]}
         */
        function wgs84togcj02(lng, lat) {
          var wgs_lat = +lat;
          var wgs_lng = +lng;
          if (out_of_china(lng, wgs_lat)) {
            return [wgs_lng, wgs_lat];
          } else {
            var dlat = transformlat(wgs_lng - 105.0, wgs_lat - 35.0);
            var dlng = transformlng(wgs_lng - 105.0, wgs_lat - 35.0);
            var radlat = (wgs_lat / 180.0) * PI;
            var magic = Math.sin(radlat);
            magic = 1 - ee * magic * magic;
            var sqrtmagic = Math.sqrt(magic);
            dlat =
              (dlat * 180.0) / (((a * (1 - ee)) / (magic * sqrtmagic)) * PI);
            dlng = (dlng * 180.0) / ((a / sqrtmagic) * Math.cos(radlat) * PI);
            var mglat = wgs_lat + dlat;
            var mglng = wgs_lng + dlng;
            return [mglng, mglat];
          }
        }

        /**
         * GCJ02 转换为 WGS84
         * @param lng
         * @param lat
         * @returns {*[]}
         */
        function gcj02towgs84(lng, lat) {
          var gcj_lat = +lat;
          var gcj_lng = +lng;
          if (out_of_china(gcj_lng, gcj_lat)) {
            return [gcj_lng, gcj_lat];
          } else {
            var dlat = transformlat(gcj_lng - 105.0, gcj_lat - 35.0);
            var dlng = transformlng(gcj_lng - 105.0, gcj_lat - 35.0);
            var radlat = (gcj_lat / 180.0) * PI;
            var magic = Math.sin(radlat);
            magic = 1 - ee * magic * magic;
            var sqrtmagic = Math.sqrt(magic);
            dlat =
              (dlat * 180.0) / (((a * (1 - ee)) / (magic * sqrtmagic)) * PI);
            dlng = (dlng * 180.0) / ((a / sqrtmagic) * Math.cos(radlat) * PI);
            var mglat = gcj_lat + dlat;
            var mglng = gcj_lng + dlng;
            return [gcj_lng * 2 - mglng, gcj_lat * 2 - mglat];
          }
        }

        function transformlat(lng, lat) {
          lat = +lat;
          lng = +lng;
          var ret =
            -100.0 +
            2.0 * lng +
            3.0 * lat +
            0.2 * lat * lat +
            0.1 * lng * lat +
            0.2 * Math.sqrt(Math.abs(lng));
          ret +=
            ((20.0 * Math.sin(6.0 * lng * PI) +
              20.0 * Math.sin(2.0 * lng * PI)) *
              2.0) /
            3.0;
          ret +=
            ((20.0 * Math.sin(lat * PI) + 40.0 * Math.sin((lat / 3.0) * PI)) *
              2.0) /
            3.0;
          ret +=
            ((160.0 * Math.sin((lat / 12.0) * PI) +
              320 * Math.sin((lat * PI) / 30.0)) *
              2.0) /
            3.0;
          return ret;
        }

        function transformlng(lng, lat) {
          lat = +lat;
          lng = +lng;
          var ret =
            300.0 +
            lng +
            2.0 * lat +
            0.1 * lng * lng +
            0.1 * lng * lat +
            0.1 * Math.sqrt(Math.abs(lng));
          ret +=
            ((20.0 * Math.sin(6.0 * lng * PI) +
              20.0 * Math.sin(2.0 * lng * PI)) *
              2.0) /
            3.0;
          ret +=
            ((20.0 * Math.sin(lng * PI) + 40.0 * Math.sin((lng / 3.0) * PI)) *
              2.0) /
            3.0;
          ret +=
            ((150.0 * Math.sin((lng / 12.0) * PI) +
              300.0 * Math.sin((lng / 30.0) * PI)) *
              2.0) /
            3.0;
          return ret;
        }

        /**
         * 判断是否在国内，不在国内则不做偏移
         * @param lng
         * @param lat
         * @returns {boolean}
         */
        function out_of_china(lng1, lat1) {
          var lat = +lat1;
          var lng = +lng1;
          // 纬度3.86~53.55,经度73.66~135.05
          return !(lng > 73.66 && lng < 135.05 && lat > 3.86 && lat < 53.55);
        }

        // 手动展开聚合球，用来解决设备位置重合问题
        function showMiniCluster(cluster) {

          function translateXY(index) {
            let total = cluster.points ? cluster.points.length : 0;
            if (total === 0) return { x: 0, y: 0 };

            let a = (2 * Math.PI) / total;
            let x = Math.sin((index - 1) * a) * (total + 2) * 4;
            let y = Math.cos((index - 1) * a) * (total + 2) * 4;
            return { x, y };
          }

          // 移除之前可能存在的遮罩层
          let existingMask = document.querySelector(".cluster-mask-layer");
          if (existingMask) {
            existingMask.remove();
          }

          let maskLayer = document.createElement("div");
          maskLayer.className = "cluster-mask-layer";
          maskLayer.style = `position:fixed;top:0;left:0;width:100%;height:100%;background-color:#00000088;animation:fadein .3s;z-index:9999;`;

          // 监听整个遮罩的点击事件，但不包括设备点
          maskLayer.addEventListener("click", function(e) {
            // 只有当点击的是遮罩本身，而不是其中的设备点时，才关闭遮罩
            if (e.target === maskLayer) {
              maskLayer.remove();
            }
          });

          // 获取聚合点在页面中的位置
          let center = cluster.getCenter();

          // 重要：获取经纬度对应的像素坐标
          let pixel = map.pointToPixel(center);

          // 修正：使用pixel坐标确保中心位置准确
          maskLayer.innerHTML = `<div id="cluster-container" style="position:absolute;top:${
            pixel.y
          }px;left:${
            pixel.x
          }px;animation:zoomin .3s;transform:translate(-50%, -50%);z-index:10000;"></div>`;

          // 遍历聚合内的所有点
          if (cluster.points && cluster.points.length > 0) {
            cluster.points.forEach((point, index) => {
              // 直接使用点位数据
              let deviceData = point;


              if (deviceData) {
                // 创建设备DOM元素
                let div = document.createElement("div");
                div.id = "deviceBox_" + deviceData.deviceId;
                div.className = "cluster-device-point";
                div.style = `position:absolute;cursor:pointer;transform:translate(${translateXY(
                  index
                ).x - 20}px,${translateXY(index).y - 20}px);z-index:10001;`;
                div.setAttribute("inMin", true);
                div.setAttribute("data-device-id", deviceData.deviceId);

                // 添加设备详情
                var detailDiv = document.createElement("div");
                var htmlStr =
                  '<div id="deviceDetail_' +
                  deviceData.deviceId +
                  '" style="display:none; transform: translateX(-50%)" class="deviceDetail">';
                htmlStr +=
                  '<div style="border-bottom: 1px solid #f3e47124;"><span style="font-weight:bolder;font-size: 0.8vw;color:#03EDFB;">' +
                  deviceData.deviceName +
                  "</span></div>";

                if (
                  deviceData.onlineState === 1 &&
                  deviceData.alarmState !== 1
                ) {
                  htmlStr +=
                    '<div>状态：<span style="color:#09E42B;">在线</span></div>';
                } else if (deviceData.onlineState == 0) {
                  htmlStr +=
                    '<div>状态：<span style="color:#B1B1B1;">离线</span></div>';
                } else if (
                  deviceData.onlineState == 1 &&
                  deviceData.alarmState === 1
                ) {
                  htmlStr +=
                    '<div>状态：<span style="color:#FF0000;">告警</span></div>';
                }

                if (deviceData.offlineReason) {
                  htmlStr += `<div>原因：<span style="color:#B1B1B1;">${
                    deviceData.offlineReason
                  }</span></div>`;
                }

                htmlStr += '<div x-arrow="" class="borrow"></div>';
                htmlStr +=
                  '<div x-arrow="" class="borrow2" style="display: none"></div></div>';
                detailDiv.innerHTML = htmlStr;

                // 设置图标
                const iconUrl = getIconName
                  ? getIconName(deviceData)
                  : "dingwei";
                const url = `/cmdbService/images/${iconUrl}`;

                // 添加图标
                const iconDiv = document.createElement("div");
                iconDiv.className = "lightGy";
                iconDiv.style = `height: 40px;width: 40px;border-radius: 50%;background: url("${url}");background-size: ${
                  iconUrl.indexOf("_") !== -1
                    ? "40px 40px;"
                    : "60px 60px;background-position: -10px -10px;"
                }background-repeat: no-repeat;position:relative;z-index:10002;`;

                div.appendChild(iconDiv);
                div.appendChild(detailDiv);

                // 使用事件代理方式绑定事件，避免事件冒泡问题
                div.onclick = function(e) {
                  window.parent.postMessage(
                    { type: "getDevicePoint", data: deviceData },
                    "*"
                  );
                  // 阻止事件冒泡
                  e.stopPropagation();
                };

                // 右键点击事件
                div.oncontextmenu = function(e) {
                  if (
                    deviceData.deviceType === "587" ||
                    deviceData.deviceType === "586"
                  ) {
                    window.parent.postMessage(
                      { type: "getPowerMap", data: deviceData },
                      "*"
                    );
                  }
                  // 阻止默认右键菜单
                  e.preventDefault();
                  // 阻止事件冒泡
                  e.stopPropagation();
                  return false;
                };

                // 添加鼠标悬浮事件
                div.onmouseover = function(e) {
                  var dom = document.getElementById(
                    "deviceDetail_" + deviceData.deviceId
                  );
                  if (dom) {
                    dom.style.display = "block";
                    dom.style.zIndex = "10003";

                    // 根据位置调整气泡方向
                    var deviceContainer = document.getElementById(
                      "cluster-container"
                    );
                    var devicePos = div.getBoundingClientRect();
                    var containerPos = deviceContainer.getBoundingClientRect();

                    // 如果设备在容器上半部分，气泡朝下显示
                    if (
                      devicePos.top <
                      containerPos.top + containerPos.height / 2
                    ) {
                      dom.style.bottom = "unset";
                      dom.style.top = "45px";
                      dom.getElementsByClassName("borrow2")[0].style.display =
                        "block";
                      dom.getElementsByClassName("borrow")[0].style.display =
                        "none";
                    } else {
                      dom.style.bottom = "45px";
                      dom.style.top = "unset";
                      dom.getElementsByClassName("borrow")[0].style.display =
                        "block";
                      dom.getElementsByClassName("borrow2")[0].style.display =
                        "none";
                    }
                  }

                  // 阻止事件冒泡
                  e.stopPropagation();
                };

                // 添加鼠标移出事件
                div.onmouseout = function(e) {
                  var dom = document.getElementById(
                    "deviceDetail_" + deviceData.deviceId
                  );
                  if (dom) {
                    dom.style.display = "none";
                  }

                  // 阻止事件冒泡
                  e.stopPropagation();
                };

                // 添加到遮罩层
                maskLayer.firstElementChild.appendChild(div);
              }
            });
          } else {
            console.error("聚合点内没有设备点位数据");
          }

          document.body.append(maskLayer);

          // 添加CSS样式确保z-index生效
          const style = document.createElement("style");
          style.textContent = `
                .cluster-mask-layer {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 9999;
                }
                .cluster-device-point {
                    position: absolute;
                    z-index: 10001;
                }
                .deviceDetail {
                    z-index: 10003 !important;
                }
                @keyframes fadein {
                    0% { opacity: 0 }
                    100% { opacity: 1 }
                }
                @keyframes zoomin {
                    0% { transform: scale(0) }
                    100% { transform: scale(1) }
                }
            `;
          document.head.appendChild(style);
        }

        // 移动地图时移除遮罩层
        map.addEventListener("moving", function() {
          let maskLayer = document.querySelector(".cluster-mask-layer");
          if (maskLayer) {
            maskLayer.remove();
          }
        });
      };
    </script>
  </body>
</html>
