import request from '@/utils/request'

const url = '/cmdbService'

export function getModelInfo(params) {
  return request({
    url: url + '/model-info/page',
    method: 'get',
    params: params
  })
}

export function saveBatchList(data) {
  return request({
    url: url + '/model-info-attr/newSaveBatchList',
    method: 'post',
    data: data
  })
}

export function getModelInfoAttr(params) {
  return request({
    url: url + '/model-info-attr/list-new',
    method: 'get',
    params: params
  })
}

export function findByTypeId(typeId) {
  return request({
    url: url + '/model-info/findByTypeId/' + typeId,
    method: 'get'
  })
}
