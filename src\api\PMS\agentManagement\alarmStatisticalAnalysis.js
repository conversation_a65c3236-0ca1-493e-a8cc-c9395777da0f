import request from '@/utils/request'

// 概览统计
export function getOverview(params) {
  return request({
    url: '/cmdbService/instance-statistics/getOverview',
    method: 'get',
    params
  })
}
// 专题统计
export function getSubject(params) {
  return request({
    url: '/cmdbService/instance-statistics/getSubject',
    method: 'get',
    params
  })
}
// 区域统计
export function getAreaDataList(params) {
  return request({
    url: '/cmdbService/instance-statistics/getArea',
    method: 'get',
    params
  })
}

// 资源类型统计
export function getResourceDataList(params) {
  return request({
    url: '/cmdbService/instance-statistics/getInstanceType',
    method: 'get',
    params
  })
}

// 厂商统计
export function getManufacturerDataList(params) {
  return request({
    url: '/cmdbService/instance-statistics/getManufacturerOrTypeCode',
    method: 'get',
    params: { ...params, type: 0 }
  })
}

// 资源厂商统计
export function getResourceManufacturerDataList(params) {
  return request({
    url: '/cmdbService/instance-statistics/getManufacturerOrTypeCode',
    method: 'get',
    params: { ...params, type: 1 }
  })
}

////// 20230215 告警统计改版

// 总体告警数据（图一）
export function totalAlarmData(data) {
  return request({
    url: '/szdlService/api/alarmManage/totalAlarmData',
    method: 'post',
    data
  })
}

// 设备告警率排行（图二）
export function deviceAlarmRateList(data) {
  return request({
    url: '/szdlService/api/alarmManage/deviceAlarmRateList',
    method: 'post',
    data
  })
}

// 告警级别占比统计分析（图三、图四）
export function alarmLevelSatistList(data) {
  return request({
    url: '/szdlService/api/alarmManage/alarmLevelSatistList',
    method: 'post',
    data
  })
}

// 厂商告警率排行（图五）
export function manufactureAlarmRateList(data) {
  return request({
    url: '/szdlService/api/alarmManage/manufactureAlarmRateList',
    method: 'post',
    data
  })
}

// 设备类型告警数量对比统计分析（图六）
export function alarmDeviceTypeList(data) {
  return request({
    url: '/szdlService/api/alarmManage/alarmDeviceTypeList',
    method: 'post',
    data
  })
}

// 告警数据变化统计分析（图七）
export function alarmDataChangeList(data) {
  return request({
    url: '/szdlService/api/alarmManage/alarmDataChangeList',
    method: 'post',
    data
  })
}

// 告警数据详情列表（列表八）
export function alarmDataDetailList(data) {
  return request({
    url: '/szdlService/api/alarmManage/alarmDataDetailList',
    method: 'post',
    data
  })
}


