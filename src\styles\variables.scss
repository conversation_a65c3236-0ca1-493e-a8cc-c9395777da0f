// sidebar - Updated to match Figma design
$menuText:#EFF0F2;
$menuActiveText:#8191C0;
$subMenuActiveText:#EFF0F2; //https://github.com/ElemeFE/element/issues/12951

$menuBg: rgba(12, 52, 117, 1); // Updated from rgba(12, 52, 117, 1)
$menuHover: rgba(9, 72, 148, 1); // Updated from rgba(9, 72, 148, 1)

$subMenuBg: rgba(12, 52, 117, 1); // Updated from rgba(12, 52, 117, 1)
$subMenuHover: rgba(9, 72, 148, 1); // Updated from rgba(9, 72, 148, 1)

$sideBarWidth: 24.0760vh;

// New color variables for Figma design
$primaryBlue: rgba(0, 145, 250, 1);
$primaryBlueBorder: rgba(0, 142, 254, 1);
$lightBlue: rgba(0, 142, 254, 0.2);
$mediumBlue: rgba(0, 142, 254, 0.5);
$lightBlueGray: rgba(150, 191, 222, 0.4);
$darkBlue: rgba(9, 72, 148, 1);
$veryDarkBlue: rgba(12, 52, 117, 1);
$borderBlue: rgba(0, 142, 254, 0.4);

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
  primaryBlue: $primaryBlue;
  primaryBlueBorder: $primaryBlueBorder;
  lightBlue: $lightBlue;
  mediumBlue: $mediumBlue;
  lightBlueGray: $lightBlueGray;
  darkBlue: $darkBlue;
  veryDarkBlue: $veryDarkBlue;
  borderBlue: $borderBlue;
}
