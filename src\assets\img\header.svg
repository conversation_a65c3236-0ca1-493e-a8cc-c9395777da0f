<svg xmlns="http://www.w3.org/2000/svg" width="1920" height="96" viewBox="0 0 1920 96" fill="none">
<g id="Frame 2" clip-path="url(#clip0_1_435)" filter="url(#filter0_b_1_435)">
<g id="Group 427321387">
<g id="Vector 962" filter="url(#filter1_b_1_435)">
<path d="M1820 79H1920V0H0V87.5H515L519.5 95H638L643 87H668H1815L1820 79Z" fill="#212A45" fill-opacity="0.6"/>
</g>
<path id="Vector 965" d="M1606 17L1582 76H1920V17H1606Z" fill="url(#paint0_linear_1_435)" fill-opacity="0.1"/>
<path id="Vector 958" d="M515 87.5H0V-1.5H792L780.5 20H698L668 87.5H643L638.5 95H519.5L515 87.5Z" fill="url(#paint1_linear_1_435)" fill-opacity="0.6"/>
<path id="Vector 957" d="M0 87.5H515L519.5 95H638.5L643 87.5H668L697 21.5H780L792 -1.5" stroke="url(#paint2_linear_1_435)" stroke-opacity="0.6" stroke-width="2" stroke-linecap="round"/>
<path id="Vector 956" d="M161 -1L170 13H637L643 7H1152L1156.5 -0.5" stroke="url(#paint3_linear_1_435)" stroke-opacity="0.6" stroke-width="2" stroke-linecap="round"/>
<path id="Vector 959" d="M668 87L698 20" stroke="url(#paint4_linear_1_435)" stroke-opacity="0.6" stroke-width="2" stroke-linecap="round"/>
<path id="Vector 960" d="M1857 0.5L1849 17H1606L1582 76" stroke="url(#paint5_linear_1_435)" stroke-opacity="0.6" stroke-width="2" stroke-linecap="round"/>
<path id="Vector 963" d="M1920 78H1821L1815 87H711" stroke="url(#paint6_linear_1_435)" stroke-opacity="0.3" stroke-width="2" stroke-linecap="round"/>
<path id="Vector 964" d="M1821 78H1920" stroke="url(#paint7_linear_1_435)" stroke-width="4" stroke-linecap="round"/>
</g>

<g id="Frame 427321371">
<g id="Group 427320644">
<rect id="Rectangle 3150" x="24" y="50" width="20" height="20" rx="2" transform="rotate(-45 24 50)" fill="#76AFF9"/>
<rect id="Rectangle 3149" x="41" y="49" width="12" height="12" rx="2" transform="rotate(-45 41 49)" fill="white"/>
</g>
<path id="è¶ç»´åå¸æ°åºå»ºç®¡çå¹³å°" d="M90.5706 61.8V47.68H106.971V61.8H90.5706ZM106.611 66.96H82.3706L77.1706 63.72L76.9706 66.96H72.3306L73.5306 50.44H78.0106L77.4906 59.44L79.1306 60.6V47.92H72.7306L72.6506 44.04H77.6906V39.92H73.7306V36.2H77.6906V31.72H84.7306V36.2H88.7706V39.92H84.7306V44.04H89.6106V45.92L92.4506 35.88H90.3706V32.2H107.451V46.32H99.6106L97.9706 42.52H100.851V35.88H99.1706L96.3706 46.32H89.6106V47.92H85.6106V52.8H89.2906V56.56H85.6106V63.2L108.211 63.28L106.611 66.96ZM99.9306 51.44H97.5706V58.08H99.9306V51.44ZM134.051 66.88V45.96H132.251L127.291 54.12H133.251L131.891 57.96H119.091V54.04L123.971 46.12H119.011V41.64L125.091 31.64H133.211L126.891 42.2H132.171V45.12L136.131 31.64H142.731L141.171 37.08H143.771L143.051 32.12H149.651L150.531 37.08H153.971V41H150.291V45.8H153.451V49.56H150.291V54.32H153.451V58.2H150.291V63.04H154.571V66.88H134.051ZM118.851 67.28V63.12L132.611 59.84V64.04L118.851 67.28ZM143.771 41H140.851V45.8H143.771V41ZM143.771 49.56H140.851V54.32H143.771V49.56ZM143.771 58.2H140.851V63.04H143.771V58.2ZM196.891 52.28L198.251 63.16H201.251L199.851 66.84H192.131L191.651 63.36L190.251 66.04H185.811L187.571 62.36H184.731L183.531 58.88V61.96L182.011 66.84H174.931L176.771 61.04V36.08H188.291L187.811 31.8H194.411L194.931 36.08H195.971L195.611 32.48H199.131L199.571 36.08H200.491V39.8H195.371L196.051 44.72L196.771 43.04H201.291L196.891 52.28ZM165.251 66.92V62.92L167.371 62.12V43.12H165.451V39.4H167.371V31.8H174.091V39.4H175.731V43.12H174.091V59.48L175.811 58.72V62.76L165.251 66.92ZM188.731 39.8H183.531V44.84H188.851V59.72L190.691 55.8L188.731 39.8ZM184.811 48.68H183.531V58.56H184.811V48.68ZM233.211 39.48V43.6H245.771V64.2H236.491L234.211 60.32H238.611V47.56H233.211V66.96H225.371V47.56H220.091V64.2H212.771V43.6H225.371V39.48H211.371V35.6H225.211L224.091 31.8H232.291L233.411 35.6H247.211V39.48H233.211ZM292.171 37.36H284.011V44H293.571V47.84H291.891V66.88H285.531V47.84H284.011V62.2L282.291 66.88H275.731L276.291 65.04H272.891L271.811 55.36H275.851L276.771 63.56L277.571 60.96V33.6H286.011L286.651 31.68H293.811L292.171 37.36ZM258.651 37.92V34.24H263.811L263.091 31.64H270.371L271.171 34.24H276.531V37.92H258.651ZM271.011 47.92V50.36H276.491V54.08H271.011V66.88H262.371L261.651 65.32H257.731L259.171 55.36H263.531L262.531 63.04H264.371V54.08H258.651V50.36H264.371V47.92H258.171V44.2H260.771L259.651 39H266.051L267.171 44.2H268.211L269.731 39H276.171L274.571 44.2H276.851V47.92H271.011ZM335.171 59.56V61H325.851V63.36H339.651V66.92H304.931V63.36H318.491V61H309.291V59.56H304.331L308.451 53.92H304.851V50.36H308.771V38.28H305.691V34.68H308.771V31.56H315.971V34.68H328.251V31.56H335.531V34.68H338.611V38.28H335.531V50.36H339.611V53.92H335.931L340.051 59.56H335.171ZM328.251 38.28H315.971V40.04H328.251V38.28ZM328.251 43.4H315.971V45.2H328.251V43.4ZM328.251 48.56H315.971V50.32H328.251V48.56ZM327.651 53.88H316.691L314.091 57.52H318.531V54.88H325.851V57.52H330.331L327.651 53.88ZM384.971 42.68V48.04H377.251V50H385.291V53.44H377.251V55.6H386.331V59.12H377.251V62.28H370.251V59.12H363.371V55.6H370.251V53.44H363.771V50H370.251V48.04H364.211V44.48H370.251V42.68H362.531V39.2H370.251V37.44H364.211V33.76H370.251V31.76H377.251V33.76H384.971V39.2H386.371V42.68H384.971ZM384.971 67H361.851L360.331 65.2L358.931 67H350.451L355.691 60.28V59.6L350.091 52.72H354.731L355.691 53.88V49.6H351.131V46.36L355.091 36.72H351.011V33.04H362.611V37L358.891 45.88H362.531V62.04L363.571 63.28H386.691L384.971 67ZM379.331 37.44H377.251V39.2H379.331V37.44ZM379.331 42.68H377.251V44.48H379.331V42.68ZM429.771 48.84V56H406.811V57.52H431.131V66.92H399.531V48.84H397.611V41.92H410.971L410.251 40.52H407.051L406.051 37.28H405.451L404.331 40.52H396.891L400.091 31.72H407.331L406.731 33.48H414.811V37.28H412.491L413.411 39.68H414.451L417.251 31.72H424.251L423.651 33.44H432.251V37.2H430.011L431.051 40.52H424.291L423.331 37.2H422.331L421.211 40.52H418.651L419.211 41.92H432.251V48.84H429.771ZM424.811 45.64H405.011V47.16H424.811V45.64ZM422.451 50.8H406.811V52.48H422.451V50.8ZM423.771 61.16H406.811V63.36H423.771V61.16ZM456.211 67.16V64.88L443.411 66.84V62.92L446.411 62.44V50.04H443.891V46.32H446.411V35.76H443.491V32.08H456.531V35.76H453.811V46.32H455.931V50.04H453.811V61.28L456.451 60.84V63.36H464.211V59.56H457.611V55.76H464.211V52.48H457.291V32.08H478.371V52.48H471.451V55.76H477.771V59.56H471.451V63.36H479.251V67.16H456.211ZM473.411 35.92H471.451V40.32H473.411V35.92ZM473.411 44.08H471.451V48.6H473.411V44.08ZM464.211 35.92H462.291V40.32H464.211V35.92ZM464.211 44.08H462.291V48.6H464.211V44.08ZM520.891 49.68H513.171L516.451 37.92H524.251L520.891 49.68ZM511.811 55.64V67H503.891V55.64H489.851V51.6H503.891V36.16H490.571V32.12H524.971V36.16H511.811V51.6H525.571V55.64H511.811ZM494.651 49.68L491.211 37.92H499.131L502.531 49.68H494.651ZM562.851 48.04L561.411 45.4L560.011 48.04H536.211V45.32L546.211 31.76H555.891L546.851 43.96H560.691L557.011 37.2H566.131L571.971 48.04H562.851ZM538.131 67V51.48H570.051V67H538.131ZM562.291 55.64H545.971V62.92H562.291V55.64Z" fill="white"/>
</g>






</g>
<defs>
<filter id="filter0_b_1_435" x="-8" y="-8" width="1936" height="112" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImage" stdDeviation="4"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1_435"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1_435" result="shape"/>
</filter>
<filter id="filter1_b_1_435" x="-8" y="-8" width="1936" height="111" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImage" stdDeviation="4"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1_435"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1_435" result="shape"/>
</filter>
<filter id="filter2_i_1_435" x="721.455" y="31.5" width="129.093" height="37" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.462745 0 0 0 0 0.686275 0 0 0 0 0.976471 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1_435"/>
</filter>
<linearGradient id="paint0_linear_1_435" x1="1920" y1="75.9999" x2="1582" y2="75.9999" gradientUnits="userSpaceOnUse">
<stop stop-color="#76AFF9" stop-opacity="0"/>
<stop offset="1" stop-color="#76AFF9"/>
</linearGradient>
<linearGradient id="paint1_linear_1_435" x1="396" y1="-1.5" x2="396" y2="95" gradientUnits="userSpaceOnUse">
<stop stop-color="#76AFF9" stop-opacity="0"/>
<stop offset="1" stop-color="#76AFF9"/>
</linearGradient>
<linearGradient id="paint2_linear_1_435" x1="0" y1="94.994" x2="697.301" y2="94.994" gradientUnits="userSpaceOnUse">
<stop stop-color="#76AFF9"/>
<stop offset="1" stop-color="#76AFF9" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_1_435" x1="161" y1="13.001" x2="1157" y2="13.001" gradientUnits="userSpaceOnUse">
<stop stop-color="#76AFF9"/>
<stop offset="1" stop-color="#76AFF9" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint4_linear_1_435" x1="697" y1="22" x2="668" y2="88" gradientUnits="userSpaceOnUse">
<stop stop-color="#76AFF9" stop-opacity="0"/>
<stop offset="1" stop-color="#76AFF9"/>
</linearGradient>
<linearGradient id="paint5_linear_1_435" x1="1888.5" y1="-10.005" x2="1582" y2="75.9995" gradientUnits="userSpaceOnUse">
<stop stop-color="#76AFF9" stop-opacity="0"/>
<stop offset="1" stop-color="#76AFF9"/>
</linearGradient>
<linearGradient id="paint6_linear_1_435" x1="1920" y1="87.0044" x2="668" y2="87.0044" gradientUnits="userSpaceOnUse">
<stop stop-color="#76AFF9" stop-opacity="0"/>
<stop offset="1" stop-color="#76AFF9"/>
</linearGradient>
<linearGradient id="paint7_linear_1_435" x1="1920" y1="79" x2="1820" y2="79" gradientUnits="userSpaceOnUse">
<stop stop-color="#76AFF9" stop-opacity="0"/>
<stop offset="1" stop-color="#76AFF9"/>
</linearGradient>
<clipPath id="clip0_1_435">
<rect width="1920" height="96" fill="white"/>
</clipPath>
</defs>
</svg>