<template>
  <div>
    <div class="header-search" style="overflow: auto;height: unset;">
      <div class="top-input">
        <search-input ref="searchInput" v-model="listQuery" v-bind="curPageConfig.searchItems" @getTips="getTips"
          @handleData="handleData" @dateRangePickerChange="dateRangePickerChange">
          <template v-slot:after>
            <el-button v-waves style="margin-left: 0.56vh" type="primary" icon="el-icon-search" @click="searchListInfo">{{
              $t("button.search") }}</el-button>
            <el-button v-waves style="margin-left: 0.56vh; background: rgba(150, 191, 222, 0.4); border-color: rgba(150, 191, 222, 0.4); color: #fff;" @click="$refs.searchInput.clear(listQuery)">{{
              $t("button.clear") }}</el-button>
            <!-- 可选择几秒刷新 -->
            <div v-if="curPage.infoType === 'realTime'" class="setting-btn">
              <el-popover placement="bottom" width="180" trigger="hover">
                <template>
                  <el-radio-group v-model="setTime" @change="changeSetTime">
                    <el-radio style="margin-bottom: 0.926vh" :label="10">10s</el-radio>
                    <el-radio style="margin-bottom: 0.926vh" :label="20">20s</el-radio>
                    <el-radio style="margin-bottom: 0.926vh" :label="30">30s</el-radio>
                    <el-radio style="margin-bottom: 0.926vh" :label="40">40s</el-radio>
                    <el-radio :label="50">50s</el-radio>
                    <el-radio :label="60">60s</el-radio>
                  </el-radio-group>
                </template>
                <div slot="reference" class="set-time">
                  <i class="el-icon-setting" />
                </div>
              </el-popover>
              <div class="time" @click="refresh">{{ timeRemaining }}s</div>
            </div>
          </template>
        </search-input>
      </div>
    </div>
    <div style="overflow: auto;height: unset;" class="content-table">
      <div class="table-div">
        <el-col :span="24" class="table-button">
          <el-button 
            v-if="curPage.alarmType === 'performance' && curPage.infoType === 'realTime'" 
            v-waves
            style="height: 3.24vh" 
            @click.stop="selectAll"
          >全选</el-button>
          <el-button 
            v-if="curPage.alarmType === 'performance' && curPage.infoType === 'realTime'" 
            v-waves
            :loading="closeLoading" 
            style="height: 3.24vh" 
            @click.stop="closeAlarm"
          >关闭</el-button>
          <el-button 
            v-if="checkButtonPermission('324111')||checkButtonPermission('324122')"
            v-waves 
            icon="el-icon-download" 
            style="height: 3.24vh" 
            :loading="exportLoading"
            @click.stop="handleExportExcel"
          >导出</el-button>
        </el-col>
        <zyk-table ref="table" :list="dataList" :list-loading="listLoading" :page-sizes="[10, 20, 30, 50, 100, 200]"
          :table-height="tableHeight" v-bind="curPageConfig.tableConfig" :total="total" :page="listQuery.page"
          :limit="listQuery.limit" :table-max-height="$store.getters.tableMaxHeight - 80" @sortChange="sortChange"
          @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange"
          @handleSelectionChange="handleSelectionChange" @handleRowClick="handleRowClick">
          <template #alarmCount="scope">
            <a v-if="scope.row.alarmCount" href="#" @click.stop="alarmCountDialog(scope.row)">{{ scope.row.alarmCount
            }}</a>
            <span v-else>-</span>
          </template>
          <template #todayAlarmCount="scope">
            <a v-if="scope.row.todayAlarmCount || scope.row.todayAlarmCount === 0" href="#"
              @click.stop="alarmCountDialog(scope.row)">{{ scope.row.todayAlarmCount }}</a>
            <span v-else>-</span>
          </template>
          <template #reachableState="scope">
            <span v-if="scope.row.reachableState === -1">-</span>
            <el-tag v-else :type="scope.row.reachableState === 1 ? 'success' : 'info'">
              {{ scope.row.reachableState === 1 ? '是' : '否' }}
            </el-tag>
          </template>
          <template #handler="scope">
            <el-button 
              type="text" 
              size="medium" 
              :disabled="scope.row.state === '已处理'"
              @click="$refs.dispatchDialog.open(scope.row)"
            >派单</el-button>
          </template>
        </zyk-table>
        <dispatch-dialog ref="dispatchDialog" @success="getList" />
        <el-dialog ref="dialog" v-dialogDrag :close-on-click-modal="false" :visible.sync="dialogVisible" title="告警记录"
          width="124.44vh">
          <alarm-count-detail :select-type="3" :record-id="recordId" :show-dialog="dialogVisible"
            @handCancle="countDetailHandCancle" />
        </el-dialog>
        <el-dialog v-dialogDrag class="layerManager" :width="dialogWidth" :title="deviceDetailModel.title"
          destroy-on-close :visible.sync="sbxqdialogFormVisible" width="92.59vh" height="37.04vh">
          <deviceDetailView topo-id="deviceTopo2" destroy-on-close :key-value="deviceDetailModel"
            :show-dialog="sbxqdialogFormVisible" :device-id="deviceId" @handCancle="sbxqhandCancle"
            @getValueAgain="sbxqShow" @tabChange="tabChange" />
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import ZykTable from '@/components/table/index.vue'
import SearchInput from '@/components/SearchInput/index.vue'
import { logOfflineList, getRealTimeList, getHistoryList, logOfflineRTList } from '@/api/otherSystem/alarmManagement/realtimeAlarm'
import { getAlertRecordHistoryType, getOfflineAlertHistoryType, asyncExportAlert, asyncExportOffline } from '@/api/PMS/agentManagement/alarmExportFileList'
import { updateOrder, getDeviceAttribute, } from '@/api/main/admin'
import { pageConfig } from './pageConfig'
import download from '@/views/otherResource/soc/mixins/download'
import { setLampUpdateTime, getLampUpdateTime } from '@/utils/auth.js'
import dispatchDialog from './dispatchDialog.vue'
import alarmCountDetail from '@/views/system/alarm/alarmCountDetail.vue'
import deviceDetailView from '@/views/dashboard/admin/dialogViews/deviceDetailView'
import permission from '@/views/otherResource/soc/mixins/permission'

export default {
  name: 'LogSystemPageContent',
  components: {
    ZykTable,
    SearchInput,
    dispatchDialog,
    alarmCountDetail,
    deviceDetailView
  },
  mixins: [download, permission],
  props: {
    contentTableConfig: {
      type: Object,
      default: () => ({}),
      require: true
    },
    logSearchPageConfig: {
      type: Object,
      default: () => ({})
    },
    showTipsText: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogWidth: '1000px',
      list: null,
      total: null,
      listLoading: false,
      exportLoading: false,
      closeLoading: false,
      operationLogInfo: {},
      listQuery: {
        page: 1,
        limit: 10,
        orderBy: null,
        order: null,
        tagId: undefined,
        shareDeviceName: null,
        shareAreaCode: null, // 区域
        streetCode: null,
        intersectionCode: null,
        shareDeviceTypes: null, // 设备类型
        shareManufacturerCode: null, // 设备厂商
        startTime: null,
        endTime: null,
        priority: null,
        shareEquipType: null,
        states: null
      },
      dataList: [],
      tableHeight: null,
      // 定时刷新
      setTime: +getLampUpdateTime() || 60,
      timeRemaining: +getLampUpdateTime() || 60,
      timer: null,
      curPage: {
        alarmType: null,
        infoType: null
      },
      curPageConfig: null,
      dialogVisible: false,
      recordId: null,
      selected: [],
      deviceDetailModel: {
        title: '照明设备'
      },
      sbxqdialogFormVisible: false,
      deviceId: null

    }
  },
  watch: {
    sbxqdialogFormVisible(val) {
      if (!val) {
        this.dialogWidth = '1000px'
      }
    }
  },
  created() {
    const path = this.$route.path.split('/').pop()
    const alarmTypes = ['offline', 'breakdown', 'performance']
    for (let index = 0; index < alarmTypes.length; index++) {
      const element = alarmTypes[index]
      if (path.includes(element)) {
        this.curPage.alarmType = element
        break
      }
    }
    if (path.includes('Realtime')) {
      this.curPage.infoType = 'realTime'
    } else {
      this.curPage.infoType = 'history'
    }
    this.curPageConfig = pageConfig[this.curPage.alarmType][this.curPage.infoType]
  },
  mounted() {
    this.getList()
    if (this.curPage.infoType === 'realTime') {
      this.changeTimeRemaining()
    }
  },
  beforeDestroy() {
    if (this.curPage.infoType === 'realTime') {
      window.clearInterval(this.timer)
      this.timer = null
      window.clearInterval(this.t)
      this.t = null
    }
  },
  methods: {
    // 列表参数
    getParams() {
      const data = { ...this.listQuery }
      if (this.curPage.alarmType === 'offline') {
        if (this.curPage.infoType === 'realTime' && this.curPage.alarmType === 'offline') {
          data.selectType = 0
        } else {
          data.selectType = 1
        }
      }
      return data
    },
    // 获取报表数据
    getList() {
      this.listLoading = true
      const data = this.getParams()
      let api = getHistoryList
      if (this.curPage.alarmType === 'offline') {
        api = this.curPage.infoType === 'realTime' ? logOfflineRTList : logOfflineList
      } else if (this.curPage.infoType === 'realTime') {
        api = getRealTimeList
      }
      console.log(api)
      api(data).then(res => {
        if (this.curPage.alarmType === 'offline') {
          // 离线告警
          this.dataList = this.curPage.infoType === 'realTime' ? res.data.data.records : res.data.data.map(ele => {
            if (ele.recoverTime) {
              ele.color = '#67C23A'
            }
            return ele
          })
          this.total = this.curPage.infoType === 'realTime' ? res.data.data.total : res.data.total
        } else {
          // 性能告警
          this.dataList = this.curPage.infoType === 'realTime' ? res.data.data : res.data.data.map(ele => {
            if (ele.state === '已关闭') {
              ele.color = '#67C23A'
            }
            return ele
          })
          this.total = res.data.total
        }

        this.listLoading = false
      }).catch(err => {
        console.log(err)
        this.dataList = []
        this.listLoading = false
      })
    },
    handleSizeChange(val) {
      // 每页显示条目个数
      this.listQuery.limit = val
      this.getList()
    },
    handleCurrentChange(val) {
      // 上一页/下一页
      this.listQuery.page = val
      this.getList()
    },
    handleData(data) {
      this.listQuery = Object.assign(this.listQuery, data)
    },
    // 根据条件查询报表信息
    searchListInfo() {
      this.listQuery.page = 1
      this.getList()
    },
    // 开始时间与结束时间
    getTips() {
      if ((this.listQuery.startTime !== '' && this.listQuery.startTime !== null) && (this.listQuery.endTime !== '' && this.listQuery.endTime !== null)) {
        if (this.listQuery.startTime > this.listQuery.endTime) {
          this.$notify({
            title: '时间不合理',
            message: '开始时间大于结束时间,请重新选择!',
            type: 'info',
            duration: 3500
          })
          this.listQuery.endTime = ''
        }
      }
    },
    // 时间选择器
    dateRangePickerChange(val) {
      if (!val) {
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      } else {
        this.listQuery.startTime = val[0]
        this.listQuery.endTime = val[1]
      }
    },
    // 列表导出
    async handleExportExcel() {
      // 处理loading
      this.exportLoading = true
      const data = this.getParams()
      delete data.page
      delete data.limit

      // 判断导出方式
      if (this.curPage.infoType !== 'realTime') {
        const typeApi = this.curPage.alarmType === 'offline' ? getOfflineAlertHistoryType : getAlertRecordHistoryType
        let typeResult = await typeApi(data).catch(err => {
          this.exportLoading = false
        })
        if (typeResult.data.code === 200) {
          if (typeResult.data.data === 1) {
            const asyncExportApi = this.curPage.alarmType === 'offline' ? asyncExportOffline : asyncExportAlert
            asyncExportApi(data, this).catch(err => {
              this.exportLoading = false
            })
            setTimeout(() => {
              this.cancel()
            }, 1000);
            this.exportLoading = false
            this.$confirm('由于数据量过大，本次采用离线导出，请在告警导出中进行文件下载！是否跳转下载页面？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.$router.push({ path: '/alarmManagement/fileList' })
            }).catch(() => {

            });
            return
          }

        } else {
          this.Alert({ alertType: 'error', message: '导出失败，请稍后再试！' })
          this.exportLoading = false
          return
        }
      }
      const url = this.curPage.alarmType === 'offline' ? '/devService/api/deviceOfflineAlert/exportExcel' : this.curPage.infoType === 'realTime' ? '/devService/api/alertRecord/exportExcel/realTime' : '/devService/api/alertRecord/exportExcel/history'
      const title = `设备${this.curPage.alarmType === 'offline' ? '离线' : this.curPage.alarmType === 'breakdown' ? '故障' : '性能'}${this.curPage.infoType === 'realTime' ? '实时' : '历史'}告警信息导出报表`

      this.download({
        url,
        data,
        method: 'post',
        fileName: `${title}.xlsx`
      }, 'application/vnd.ms-excel').then(res => {
        this.exportLoading = false
      }).catch(() => {
        this.exportLoading = false
      })
    },
    sortChange(row) {
      console.log(row.order)
      this.listQuery.orderBy = row.order ? row.prop : ''
      this.listQuery.order = row.order === 'ascending' ? 'ASC' : row.order === 'descending' ? 'DESC' : null
      this.handleCurrentChange(1)
    },
    // 频次详情及关闭
    alarmCountDialog(row) {
      if (row.alarmCount) {
        this.recordId = row.id || row.recordId
        this.dialogVisible = true
      }
    },
    countDetailHandCancle() {
      this.dialogVisible = false
      this.recordId = null
    },
    // 行点击事件
    handleRowClick(row, column, event) {
      console.log(['deviceName', 'deviceSerNo', 'deviceSerno'].includes(column.property), column.property)
      if (['deviceName', 'deviceSerNo', 'deviceSerno'].includes(column.property)) {
        this.sbxqShow(row.deviceId)
      }
    },
    sbxqhandCancle() {
      this.dialogWidth = '1000px'
      this.sbxqdialogFormVisible = false
    },

    sbxqShow(val) {
      this.deviceId = val
      getDeviceAttribute({
        deviceId: val
      }).then((response) => {
        let data = response.data.data
        data.deviceData.sort((a, b) => a.index - b.index)
        this.deviceDetailModel = data
        this.sbxqdialogFormVisible = true
      })
    },
    // 选择及关闭告警
    handleSelectionChange(val) {
      this.selected = val
    },
    selectAll() {
      for (let index = 0; index < this.dataList.length; index++) {
        const element = this.dataList[index]
        console.log(this.$refs)
        this.$refs.table.$refs.table.toggleRowSelection(element, true)
      }
    },
    closeAlarm() {
      if (this.selected.length === 0) {
        this.Alert({ alertType: 'nodata' })
        return
      }
      this.closeLoading = true
      updateOrder({
        type: 0,
        ids: this.selected.map(ele => ele.id)
      }).then(res => {
        this.closeLoading = false
        this.Alert({ alertType: 'update', message: '关闭成功！' })
        this.getList()
      }).then(() => {
        this.closeLoading = false
      })
    },
    // 定时刷新
    changeSetTime(val) {
      setLampUpdateTime(val)
      this.timeRemaining = val
      window.clearInterval(this.timer)
      this.timer = null
      this.changeTimeRemaining()
      this.$nextTick(() => {
        this.setTime = val
      })
    },
    changeTimeRemaining() {
      window.clearInterval(this.timer)
      this.timer = null
      this.timer = setInterval(() => {
        this.timeRemaining = this.timeRemaining - 1
        if (this.timeRemaining === 0) {
          this.timeRemaining = this.setTime
          this.getList(false)
        }
      }, 1000)
    },
    refresh() {
      this.getList(false)
    },
    tabChange(val) {
      this.dialogWidth = ~~val === 4 ? '95%' : '1000px'
    }
  }
}
</script>

<style scoped>
.table-div {
  padding-top: 1vh;
}

.table-button {
  margin-top: 0.926vh;
  margin-bottom: 0.926vh;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}

/deep/.deviceSearchDiv {
  margin-right: 0.56vh;
  display: inline-block;
}

.top-input {
  position: relative;
}

.img {
  position: absolute;
  right: 5px;
  top: 5px;
}

.setting-btn {
  margin-left: 0.56vh;
  height: 36px;
  width: 100px;
  font-size: 14px;
  background: #409eff;
  border-radius: 4px;
  display: inline-block;
  vertical-align: top;
  color: #fff;
  line-height: 36px;
}

.set-time {
  width: 33%;
  text-align: center;
  float: left;
  border-right: 1px solid #fff;
}

.time {
  display: inline-block;
  width: 66%;
  text-align: center;
  cursor: pointer;
}

.header-search {
  /* position: relative; */
  /* min-width: 990px; */
  background-color: rgba(37, 46, 63, 0.6);
  border-radius: 4px;
  padding: 20px 0px 20px 15px;
}

.content-table {
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 0.926vh;
  margin-top: 0.926vh;
  flex: 1;
  background-color: rgba(37, 46, 63, 0.6);
}
</style>
