<template>
  <div class="app-container rule-config">
    <!-- tabs -->
    <el-tabs v-model="activeTab" class="tabs" @tab-click="handleClick">
      <el-tab-pane label="批量配置" name="0" />
      <el-tab-pane label="精准配置" name="1" />
    </el-tabs>
    <!-- form -->
    <el-form ref="form" inline :model="formData">
      <el-form-item prop="ruleName">
        <el-input v-model="formData.ruleName" placeholder="请输入规则名称" clearable />
      </el-form-item>
      <el-form-item v-if="activeTab === '0'" prop="deviceType">
        <el-cascader ref="selectDeptRef" v-model="formData.deviceType" :show-all-levels="false" style="width: 180px"
          clearable :options="deviceTypes" :props="{
            label: 'value',
            value: 'key',
            children: 'deviceTypeList',
            checkStrictly: true,
            multiple: true,
          }" placeholder="设备类型" :collapse-tags="true" />
      </el-form-item>
      <el-form-item prop="alarmType">
        <el-select v-model="formData.alarmType" placeholder="请选择告警类型" clearable>
          <el-option v-for="item in alarmTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item prop="alarmLevel">
        <el-select v-model="formData.alarmLevel" placeholder="请选择告警等级" clearable>
          <el-option v-for="item in alarmLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item prop="owner">
        <el-select v-model="formData.owner" clearable filterable :loading="selectLoading" placeholder="请选择接单人"
          class="form-item-width">
          <el-option v-for="item in projectManagerOptions" :key="item.key" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">{{
          $t("button.search")
        }}</el-button>
        <el-button type="primary" @click="handleClear">{{
          $t("button.clear")
        }}</el-button>
      </el-form-item>
      <el-button type="primary" style="float: right" 
        v-if="checkButtonPermission('324261')"
        @click="handleCreate">新增规则</el-button>
    </el-form>
    <!-- table -->
    <el-table v-loading="listLoading" :data="tableList" element-loading-text="数据正在加载中..." border stripe fit
      highlight-current-row style="width: 100%" @selection-change="handleSelectionChange">
      <el-table-column type="index" :label="$t('table.id')" width="100" />
      <el-table-column align="center" label="规则名称" min-width="200px" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.ruleName }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="activeTab === '0'" align="center" label="设备类型" min-width="200px">
        <template slot-scope="scope">
          <span>{{ scope.row.deviceTypeName }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="告警类型" min-width="200px">
        <template slot-scope="scope">
          <span>{{ valueToLabel(scope.row.alarmType, alarmTypeOptions) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="告警等级" min-width="200px">
        <template slot-scope="scope">
          <span>
            {{ alarmLevelOptionsTo(scope.row.alarmLevel) || '-' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="接单人" min-width="200px">
        <template slot-scope="scope">
          <span>
            {{ scope.row.ownerName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="自动派单" min-width="100px">
        <template slot-scope="scope">
          <span v-if="scope.row.autoDispatch === 1">√</span>
          <span v-if="scope.row.autoDispatch === 0">×</span>
        </template>
      </el-table-column>
      <!--       <el-table-column align="center" label="短信提醒" min-width="100px">
        <template slot-scope="scope">
          <span v-if="scope.row.sendMessages === 1">√</span>
          <span v-if="scope.row.sendMessages === 0">×</span>
        </template>
      </el-table-column> -->
      <el-table-column align="center" label="创建时间" min-width="180px">
        <template slot-scope="scope">
          {{ formatTiem(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('table.actions')" min-width="120px">
        <template slot-scope="scope">
          <div v-if="hasAnyActionPermission(['324262', '324263'])">
            <el-button 
              v-if="checkButtonPermission('324262')"
              type="text" 
              size="medium" 
              @click="showDetail(scope.row)"
            >{{ $t("button.edit") }}</el-button>
            <el-button 
              v-if="checkButtonPermission('324263')"
              type="text" 
              size="medium" 
              @click="handleDeletes(scope.row.ruleId)"
            >{{ $t("button.delete") }}</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- pagination -->
    <div class="pagination-container">
      <el-pagination :current-page="formData.page" :page-sizes="[10, 20, 30, 50, 100, 200]" :page-size="formData.limit"
        layout="total, sizes, prev, pager, next, jumper" :total="total" background @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
      <div class="pagination-button">
        <span>一键开关</span>
        <el-switch v-model="start" active-color="#13ce66" inactive-color="#cccccc" @change="changeSwitch" />
      </div>

    </div>
    <!-- dialog -->
    <detail v-if="dialogFormVisible" :key-data="keyData" :is-detail="isDetail" :rule-type="activeTab"
      @success="handleSuccess" @close="dialogFormVisible = false" />
  </div>
</template>

<script>
import { getDeviceType } from '@/api/system/dic/dic'
import {
  orderRuleList,
  orderRuleDelete,
  selectMiniPersonList,
  orderRuleUpdateState
} from '@/api/otherResource/ruleConfig'
import searchInput from '@/components/SearchInput/index.vue'
import detail from './detail'
import moment from 'moment'
import permission from '@/views/otherResource/soc/mixins/permission'

export default {
  components: {
    detail,
    // eslint-disable-next-line vue/no-unused-components
    searchInput
  },
  mixins: [permission],
  data() {
    return {
      keyData: null,
      isDetail: null,
      activeTab: 0,
      start: false,
      formData: {
        ruleName: null,
        deviceType: [],
        alarmType: null,
        alarmLevel: null,
        owner: null,
        page: 1,
        limit: 10
      },
      total: null,
      tableList: [],
      listLoading: true,
      deviceTypes: [],
      alarmTypeOptions: [
        {
          label: '离线告警',
          value: 1
        },
        {
          label: '性能告警',
          value: 2
        }
      ],
      filtrateTypeOptions: [
        {
          label: '全局过滤',
          value: 1
        },
        {
          label: '关键字过滤',
          value: 2
        }
      ],
      alarmLevelOptions: [{
        label: '紧急',
        value: '1'
      }, {
        label: '严重',
        value: '2'
      }, {
        label: '一般',
        value: '3'
      }, {
        label: '次要',
        value: '4'
      }],
      projectManagerOptions: [],
      selectLoading: false,
      dialogFormVisible: false
    }
  },
  computed: {
    selectDeviceTypes() {
      let deviceTypes = []
      if (!this.formData.deviceType) return null
      for (let index = 0; index < this.formData.deviceType.length; index++) {
        const element = this.formData.deviceType[index]
        if (!element[element.length - 1].includes('noChild')) deviceTypes = deviceTypes.concat(element[element.length - 1].split(','))
      }
      const result = [...new Set(deviceTypes)]
      return result.length > 0 ? result : null
    }
  },
  created() {
    this.fetchData(this.activeTab)
    this.getDeviceTypes()
    this.getIntersetions()
  },
  methods: {
    formatTiem(value) {
      return moment(value).format('yyyy-MM-DD HH:mm:ss')
    },
    blurSelect() {
      if (!this.projectManagerOptions.length) {
        this.getIntersetions()
      }
    },
    alarmLevelOptionsTo(alarm) {
      if (!alarm) return
      return alarm.split(',').map(key => {
        return this.alarmLevelOptions.find(item => key === item.value).label
      }).join(',')
    },
    handleClick() {
      this.formData = {
        ruleName: null,
        deviceType: [],
        alarmType: null,
        alarmLevel: null,
        owner: null,
        page: 1,
        limit: 10
      }
      this.fetchData(this.activeTab)
    },
    getIntersetions(userName = '') {
      this.selectLoading = true
      selectMiniPersonList({
        userName
      }).then((res) => {
        this.selectLoading = false
        this.projectManagerOptions = res.data.data.map(item => {
          return {
            label: item.userName,
            value: item.userId
          }
        })
      }).catch(() => {
        this.selectLoading = false
      })
    },
    fetchData(type, value) {
      this.listLoading = true
      orderRuleList({
        ...this.formData,
        ruleType: type,
        deviceTypeList: value
      }).then(({ data }) => {
        this.tableList = data.data
        this.total = data.total
        this.listLoading = false
      })
    },
    valueToLabel(value, list) {
      const result = list.find((item) => {
        if (item.value === value) {
          return item
        }
      })
      return result?.label
    },
    getDeviceTypes() {
      getDeviceType({ isSource: 1 }).then((res) => {
        this.deviceTypes = this.getTreeData(
          res.data.data.filter((ele) => !ele.hidden)
        )
      })
    },
    getTreeData(data, isChild) {
      return data.map((ele) => {
        if (ele.deviceTypeList.length > 0) {
          ele.deviceTypeList = this.getTreeData(ele.deviceTypeList, true)
          ele.key = ele.deviceTypeList.map((ele) => ele.key).join(',')
        } else {
          ele.deviceTypeList = null
          if (!isChild) ele.key = 'noChild,' + ele.key
        }
        return ele
      })
    },
    showDetail(data) {
      this.isDetail = true
      this.keyData = data.ruleId
      this.dialogFormVisible = true
    },
    handleCreate() {
      this.keyData = 0
      this.isDetail = false
      this.dialogFormVisible = true
    },
    handleSizeChange(val) {
      this.formData.limit = val
      this.fetchData(this.activeTab)
    },
    handleCurrentChange(val) {
      this.formData.page = val
      this.fetchData(this.activeTab)
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleDeletes(id) {
      const ids = []
      ids.push(id)
      if (id > 0) {
        this.$confirm('规则删除后无法再开启自动派单与短信提示，是否删除？', '提示').then(() => {
          orderRuleDelete(ids).then((response) => {
            if (response.data.code === 200) {
              this.$notify({
                title: '成功',
                message: '删除成功',
                type: 'success',
                duration: 2000
              })
            } else {
              this.$notify({
                title: '失败！',
                message: '删除数据失败！',
                type: 'info',
                duration: 2000
              })
            }
            this.setPage()
            this.fetchData(this.activeTab)
          })
        })
      }
    },
    // 删除时，获取最新页码
    setPage() {
      const page = Math.ceil((this.total - 1) / this.formData.limit) // 向上取整计算页数
      const nowpage = this.formData.page > page ? page : this.formData.page
      this.formData.page = nowpage < 1 ? 1 : nowpage
    },
    handleSearch() {
      this.formData.page = 1
      this.fetchData(this.activeTab, this.selectDeviceTypes)
    },
    handleClear() {
      this.$refs['form'].resetFields()
      this.fetchData(this.activeTab)
    },
    handleSuccess() {
      this.fetchData(this.activeTab)
      this.dialogFormVisible = false
    },
    changeSwitch(value) {
      orderRuleUpdateState({
        operation: value ? 1 : 0,
        ruleType: Number(this.activeTab)
      }).then(res => {
        this.$message({
          type: 'success',
          message: res.data.message
        })
        this.fetchData(this.activeTab)
      })
    }
  }
}
</script>

<style lang="scss">
.rule-config {
  .tabs {
    height: 4.9996vh;
  }

  .pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .pagination-button {
    font-size: 1.2038vh;
    color: #606266;
  }

  .pagination-button span {
    position: relative;
    top: 0.0926vh;
  }
}
</style>
