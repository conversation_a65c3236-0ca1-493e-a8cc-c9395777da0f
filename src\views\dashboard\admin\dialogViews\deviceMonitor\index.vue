<template>
  <div class="deviceMonitorTab">
    <div v-if="!deviceLoading" style="float: right;">
      <el-select v-model="selectTimeDateType" clearable placeholder="请选择" class="form-item-width"
        @change="selectChange">
        <el-option label="按当天搜索" :value="1" />
        <el-option label="按近7天搜索" :value="2" />
        <el-option label="按近30天搜索" :value="3" />
      </el-select>
    </div>
    <div style="clear:both;">
      <el-tabs v-if="deviceMonitorData.length > 0" v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane v-for="(item, index) in deviceMonitorData" :key="index" :label="item.title" lazy>
          <echartItem v-if="item.statisticsReport !== null && item.statisticsReport.length > 0" :key-model="data" />
          <div v-else style="text-align:center;margin-top: 5vh;">暂无数据！</div>
        </el-tab-pane>
      </el-tabs>
      <div v-else style="text-align: center">暂无数据！</div>
    </div>
  </div>
</template>

<script>
import echartItem from './echartItem'

export default {
  components: {
    echartItem
  },
  props: ['deviceMonitorData', 'queryType', 'deviceLoading'],
  data() {
    return {
      activeName: '0',
      data: null,
      selectTimeDateType: 1 // 第二个选择框选择的内容
    }
  },
  watch: {
    deviceMonitorData: {
      deep: true,
      handler(val) {
        this.activeName = '0'
        this.data = this.deviceMonitorData[0]
      }
    },
    queryType: {
      deep: true,
      handler(val) {
        this.selectTimeDateType = parseInt(this.queryType == 0 ? 1 : this.queryType)
      }
    }
  },
  created() {
    this.selectTimeDateType = 1
    this.data = this.deviceMonitorData[0]
    console.log(this.deviceMonitorData, 99)
  },
  methods: {
    handleClick(tab, event) {
      this.data = this.deviceMonitorData[tab.index]
    },
    selectChange(value) {
      if (typeof value === 'string') {
        return
      }
      this.selectTimeDateType = value
      this.$emit('changeDateType', this.selectTimeDateType)
    }
  }
}
</script>

<style>
.deviceMonitorTab .el-tabs--card>.el-tabs__header {
  border-bottom: 2px solid #304B9B !important;
}

.deviceMonitorTab .el-tabs--card>.el-tabs__header .el-tabs__item {
  background-color: #434D68;
  color: #ffffff;
  border-left: unset !important;
  width: unset !important;
}

.deviceMonitorTab .el-tabs--card>.el-tabs__header .el-tabs__nav {
  border: unset !important;
}

.deviceMonitorTab .el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
  border: unset !important;
  color: #ffffff !important;
  background-color: #4262C1 !important;
}

.deviceMonitorTab .el-tabs--card>.el-tabs__header .el-tabs__item {
  font-size: 1.066668vh !important;
  border-radius: 3px !important;
  margin-left: 0.533334vh;
}

.deviceMonitorTab .el-tabs__item {
  padding: 0 0.88889vh !important;
  height: 2.8vh !important;
  line-height: 2.8vh !important;
}

.deviceMonitorTab .el-tabs__header {
  margin: 0 0 0px !important;
}

.li-tab {
  padding-top: 10px;
  cursor: pointer;
  border: 1px solid #47A4D9;
  color: #ffffff;
  width: 140px;
  height: 40px;
  display: inline-block;
  margin: 0;
  text-align: center;
}
</style>
