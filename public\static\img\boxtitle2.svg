<svg width="464" height="54" viewBox="0 0 464 54" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H464V54H262H253H0V0Z" fill="url(#paint0_linear_2712_3840)" fill-opacity="0.3"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H464V54H262H253H0V0Z" fill="url(#paint1_radial_2712_3840)" fill-opacity="0.5"/>
<rect opacity="0.4" width="464" height="2" fill="url(#paint2_linear_2712_3840)"/>
<rect y="52" width="464" height="2" fill="url(#paint3_linear_2712_3840)"/>
<rect y="5" width="40" height="44" fill="#D8D8D8" fill-opacity="0.01"/>
<mask id="mask0_2712_3840" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="5" width="40" height="44">
<rect y="5" width="40" height="44" fill="white"/>
</mask>
<g mask="url(#mask0_2712_3840)">
<g opacity="0.15" filter="url(#filter0_d_2712_3840)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 10L17 27L0 44L-17 27L0 10Z" fill="#E9F8FF"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 21L6 27L0 33L-6 27L0 21Z" fill="#E9F8FF"/>
</g>
<defs>
<filter id="filter0_d_2712_3840" x="-21" y="2" width="46" height="46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="-2"/>
<feGaussianBlur stdDeviation="3"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.913725 0 0 0 0 0.972549 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2712_3840"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2712_3840" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2712_3840" x1="113.25" y1="47.41" x2="464" y2="47.41" gradientUnits="userSpaceOnUse">
<stop offset="0.000409747" stop-color="#4962E3"/>
<stop offset="0.590579" stop-color="#455EDD" stop-opacity="0.1"/>
<stop offset="1" stop-color="#455DDC" stop-opacity="0.01"/>
</linearGradient>
<radialGradient id="paint1_radial_2712_3840" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(-2.87211e-05 27) scale(232 7016.29)">
<stop stop-color="#4962E3"/>
<stop offset="0.346216" stop-color="#4861E1" stop-opacity="0.742972"/>
<stop offset="0.589742" stop-color="#475FDF" stop-opacity="0.418371"/>
<stop offset="0.688332" stop-color="#465FDF" stop-opacity="0.3"/>
<stop offset="0.7923" stop-color="#465EDD" stop-opacity="0.2"/>
<stop offset="1" stop-color="#455DDC" stop-opacity="0.02"/>
</radialGradient>
<linearGradient id="paint2_linear_2712_3840" x1="0" y1="1" x2="464" y2="1" gradientUnits="userSpaceOnUse">
<stop offset="0.000406332" stop-color="#6A81FF" stop-opacity="0.01"/>
<stop offset="0.499922" stop-color="#6A81FF"/>
<stop offset="1" stop-color="#6A81FF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint3_linear_2712_3840" x1="0" y1="53" x2="464" y2="53" gradientUnits="userSpaceOnUse">
<stop offset="0.000406332" stop-color="#6A81FF" stop-opacity="0.01"/>
<stop offset="0.175884" stop-color="#6A81FF"/>
<stop offset="1" stop-color="#6A81FF" stop-opacity="0.01"/>
</linearGradient>
</defs>
</svg>
