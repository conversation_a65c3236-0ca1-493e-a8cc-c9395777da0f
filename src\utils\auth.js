import Cookies from 'js-cookie'

const TokenKey = 'szdl-web-User-Token'
const EntSernoKey = 'szdl-web-ent-serno'
const UserKey = 'szdl-web-userId'
const CurrentIp = 'szdl-web-currentIp'
const CurrentPort = 'szdl-web-currentPort'
const CurrentUserInfoKey = 'szdl-web-currentUserInfo'
const refleshToken = 'szdl-web-reflesh-Token'
const lampUpdateTime = 'szdl-web-lampUpdateTime'
const encryptUserIdKey = 'szdl-web-lampUpdateTime'
const isUpdateNewLogin = 'szdl-web-isUpdateNewLogin'
const isUpdateIsShowSpot = 'szdl-web-isUpdateIsShowSpot'
export function getCurrentIp() {
  return localStorage.getItem(CurrentIp)
}

export function setCurrentIp(ip) {
  return localStorage.setItem(CurrentIp, ip)
}

export function getCurrentPort() {
  return localStorage.getItem(CurrentPort)
}

export function setCurrentPort(port) {
  return localStorage.setItem(CurrentPort, port)
}

export function getToken() {
  return localStorage.getItem(TokenKey)
}

export function setToken(token) {
  return localStorage.setItem(TokenKey, token)
}

export function getUserId() {
  return localStorage.getItem(UserKey)
}

export function setUserId(userId) {
  return localStorage.setItem(UserKey, userId)
}

export function getEncryptUserId() {
  return localStorage.getItem(encryptUserIdKey)
}

export function setEncryptUserId(encryptUserId) {
  return localStorage.setItem(encryptUserIdKey, encryptUserId)
}

export function setEntSerno(entSerno) {
  return localStorage.setItem(EntSernoKey, entSerno)
}

export function setUserInfo(userInfo) {
  return localStorage.setItem(CurrentUserInfoKey, userInfo)
}

export function getUserInfo(userInfo) {
  return localStorage.getItem(CurrentUserInfoKey)
}

export function getEntSerno() {
  return localStorage.getItem(EntSernoKey)
}

export function removeUserId() {
  return localStorage.removeItem(UserKey)
}

export function removeToken() {
  return localStorage.removeItem(TokenKey)
}

export function removeEntSerno() {
  return localStorage.removeItem(EntSernoKey)
}

export function removeEncryptUserId() {
  return localStorage.removeItem(encryptUserIdKey)
}

export function getRfleshToken() {
  return localStorage.getItem(refleshToken)
}

export function setRfleshToken(token) {
  return localStorage.setItem(refleshToken, token)
}

export function setLampUpdateTime(time) {
  return localStorage.setItem(lampUpdateTime, time)
}

export function getLampUpdateTime(time) {
  return localStorage.getItem(lampUpdateTime)
}

export function setIsNewLogin(isNewLogin) {
  return localStorage.setItem(isUpdateNewLogin, isNewLogin)
}

export function getIsNewLogin(isNewLogin) {
  return localStorage.getItem(isUpdateNewLogin)
}

export function getApi(env) {
  if (getCurrentIp() === null || getCurrentIp() === '' || getCurrentIp() === undefined) {
    env.BASE_URL = 'http://' + env.DEFAULT_IP
    return 'http://' + env.DEFAULT_IP + ':' + env.DEFAULT_API_PORT + env.BASE_API
  } else {
    if (!(getCurrentIp().indexOf('localhost') > -1 || getCurrentIp().indexOf('127.0.0.1') > -1)) {
      env.DEFAULT_IP = getCurrentIp()
    }
    if (getCurrentPort() !== '') {
      if (!(getCurrentPort() === '80')) {
        env.DEFAULT_EXTRANET_API_PORT = getCurrentPort()
      }
    }
    return 'http://' + env.DEFAULT_IP + ':' + env.DEFAULT_EXTRANET_API_PORT + env.BASE_API
  }
}
