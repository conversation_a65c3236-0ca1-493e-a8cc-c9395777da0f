import request from '@/utils/request'

export function logSystemList(data, pageName) {
  return request({
    url: `/szdlService/api/logManage/${pageName}`,
    method: 'post',
    data
  })
}

export function logSystemListES(data) {
  return request({
    url: `/esService/ES/query`,
    method: 'post',
    data
  })
}

export function logOfflineList(data) {
  return request({
    url: `/devService/api/deviceOfflineRecover/listOfflineRecoverRecord`,
    method: 'post',
    data
  })
}
