<template>
  <el-dialog width="50%" :title="ruleType == 0 ? '批量规则配置' : '精准规则配置'" :visible.sync="dialogVisible"
    :before-close="handleClose">
    <el-form ref="formData" :model="formData" :rules="rules" label-width="11.11vh" class="demo-formData">
      <el-form-item label="工单规则名称" prop="ruleName">
        <el-input v-model="formData.ruleName" placeholder="请输入规则名称" />
      </el-form-item>
      <el-form-item v-if="ruleType === '0'" label="告警设备类型" prop="deviceType">
        <cascader v-if="!keyData" v-model="formData.deviceType" />
        <el-input v-else v-model="formData.deviceTypeName" disabled />
      </el-form-item>
      <el-form-item v-if="ruleType === '1'" label="告警设备" required>
        <el-button type="primary" @click="isShowDeviceModal = true">添加</el-button>
        <!-- 添加设备弹窗 -->
        <device-modal v-if="isShowDeviceModal" @success="successDeviceModal" @close="isShowDeviceModal = false" />
        <!-- 内部弹窗 -->
        <div>
          <el-table border :data="deviceList" style="margin-top: 1.85vh;">
            <el-table-column type="index" label="序号" width="100" />
            <el-table-column prop="deviceName" label="资源名称" min-width="16.67vh" />
            <el-table-column prop="deviceSerno" label="唯一编码" min-width="16.67vh" />
            <el-table-column prop="sysTypeName" label="专题类型" min-width="9.26vh" />
            <el-table-column prop="deviceTypeName" label="设备类型" min-width="9.26vh" />
            <el-table-column label="所属区域" min-width="9.26vh">
              <template slot-scope="scope">
                <span>{{ scope.row.belongArea || scope.row.areaName }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作" min-width="7.41vh">
              <template slot-scope="scope">
                <el-button type="text" size="medium" @click="handleRemove(scope.row)">
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!-- 必填校验 -->
        <div v-if="dataError" class="el-form-item__error">
          请选择告警设备
        </div>
      </el-form-item>
      <!-- <el-form-item label="告警类型" prop="alarmType">
        <el-select v-model="formData.alarmType" placeholder="请选择告警类型">
          <el-option v-for="item in alarmTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="formData.alarmType === 2" label="告警等级" prop="alarmLevel">
        <el-checkbox-group v-model="formData.alarmLevel">
          <el-checkbox v-for="item in alarmLevelOptions" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item> -->
      <el-form-item label="接单人" prop="owner">
        <el-select v-model="formData.owner" multiple clearable filterable :loading="selectLoading" placeholder="请选择接单人"
          class="form-item-width">
          <el-option v-for="item in projectManagerOptions" :key="item.key" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否自动派单" prop="autoDispatch">
        <el-switch v-model="formData.autoDispatch" @change="changeSwitch" />
      </el-form-item>
      <!--       <el-form-item v-show="formData.autoDispatch" label="是否短信提醒" prop="sendMessages">
        <el-switch v-model="formData.sendMessages" />
      </el-form-item> -->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit('formData')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import {
  orderRuleGet,
  orderRuleAddRule,
  orderRuleUpdate,
  selectMiniPersonList
} from '@/api/otherResource/ruleConfig'
import cascader from './components/cascader.vue'
import deviceModal from './components/deviceModal.vue'

export default {
  name: 'AlarmFilterRuleConfigDetail',

  components: {
    cascader,
    deviceModal
  },

  props: {
    keyData: {
      type: Number,
      required: true
    },
    isDetail: {
      type: Boolean,
      default: false
    },
    ruleType: {
      type: [Number, String],
      required: true,
      default: 0 // 0:批量配置;1:精准配置
    }
  },
  data() {
    return {
      formData: {
        owner: [],
        alarmLevel: [],
        autoDispatch: true,
        sendMessages: false,
        alarmType: 1
      },
      deviceList: [],
      rules: {
        ruleName: [
          { required: true, message: '请输入工单规则名称', trigger: 'blur' },
          { max: 20, message: '不超过20字符', trigger: 'blur' },
          { pattern: /^[\u4E00-\u9FA5A-Za-z0-9_\-@#$%^&（）【】]{1,20}$/, message: '规则名称支持中文字符、英文字符、数字与以下特殊符号-@#$%&（）【】规则名称长度不能超过20个字符' }
        ],
        deviceType: [
          { required: true, message: '请选择告警设备类型', trigger: 'change' }
        ],
        alarmType: [
          { required: true, message: '请选择告警类型', trigger: 'change' }
        ],
        alarmLevel: [
          { type: 'array', required: true, message: '请至少选择一个告警等级', trigger: 'change' }
        ],
        owner: [
          { type: 'array', required: true, message: '请输入接单人', trigger: 'change' }
        ]
      },
      alarmTypeOptions: [
        {
          label: '离线告警',
          value: 1
        },
        {
          label: '性能告警',
          value: 2
        }
      ],
      alarmLevelOptions: [{
        label: '紧急',
        value: '1'
      }, {
        label: '严重',
        value: '2'
      }, {
        label: '一般',
        value: '3'
      }, {
        label: '次要',
        value: '4'
      }],
      projectManagerOptions: [],
      selectLoading: false,
      dialogVisible: false,
      isShowDeviceModal: false,
      dataError: false
    }
  },

  async mounted() {
    await this.getIntersetions()
    if (this.keyData) {
      this.dialogVisible = false
      await orderRuleGet(this.keyData).then(({ data }) => {
        const result = data.data
        const autoDispatch = !!result.autoDispatch
        const sendMessages = !!result.sendMessages
        const alarmLevel = result.alarmLevel?.split(',') || []
        const owner = result.owner?.split(',').map(item => Number(item)) || []
        this.formData = { ...result, autoDispatch, sendMessages, alarmLevel, owner }
        this.deviceList = result.deviceInfoList || []
        this.dialogVisible = true
      }).catch(() => {
        this.dialogVisible = true
      })
    } else {
      this.dialogVisible = true
    }
  },
  methods: {
    checkDeviceList() {
      if (!this.deviceList.length) {
        this.dataError = true
      } else {
        this.dataError = false
      }
    },
    // 是否自动派单
    changeSwitch(value) {
      if (value === false) this.formData.sendMessages = false
    },
    getIntersetions(userName = '') {
      this.selectLoading = true
      return selectMiniPersonList({
        userName
      }).then((res) => {
        this.selectLoading = false
        this.projectManagerOptions = res.data.data.map(item => {
          return {
            label: item.userName,
            value: item.userId
          }
        })
      }).catch(() => {
        this.selectLoading = false
      })
    },
    handleSubmit(formName) {
      if (this.ruleType === '1' && !this.deviceList.length) {
        this.dataError = true
      }
      this.$refs[formName].validate((valid) => {
        if (valid && !this.dataError) {
          const { alarmLevel, deviceType, autoDispatch, sendMessages, owner } = this.formData
          const apiUrl = !this.keyData ? orderRuleAddRule : orderRuleUpdate
          const params = {
            ...this.formData,
            ruleId: this.keyData ? this.keyData : null,
            ruleType: Number(this.ruleType),
            deviceType: typeof deviceType === 'string' ? deviceType.split('-')[0] : deviceType,
            alarmLevel: alarmLevel.join(','),
            autoDispatch: autoDispatch ? 1 : 0,
            sendMessages: sendMessages ? 1 : 0,
            deviceIdList: this.deviceList.map(item => item.deviceId),
            owner: owner.join(',')
          }
          apiUrl(params).then(res => {
            this.$emit('success')
            this.$message({
              message: res.data.message,
              type: 'success'
            })
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 去重
    unique(arr) {
      const map = new Map()
      for (const item of arr) {
        if (!map.has(item.deviceId)) {
          map.set(item.deviceId, item)
        }
      }
      return [...map.values()]
    },
    // 添加
    successDeviceModal(value) {
      this.deviceList = this.unique([...this.deviceList, ...value])
      this.isShowDeviceModal = false
      this.checkDeviceList()
    },
    // 移除
    handleRemove(row) {
      const index = this.deviceList.indexOf(row)
      this.deviceList.splice(index, 1)
      this.checkDeviceList()
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss">
.filter-list-style {
  margin-bottom: 20px;

  &:last-child {
    margin: 0;
  }
}
</style>
