import request from '@/utils/request'

// 资源标签管理-获取列表
export function deviceTagList(data) {
  return request({
    url: '/cmdbService/api/deviceTag/deviceTagList',
    method: 'post',
    data
  })
}

// 资源标签管理-添加
export function insertDeviceTag(data) {
  return request({
    url: '/cmdbService/api/deviceTag/insertDeviceTag',
    method: 'post',
    data
  })
}

// 资源标签管理-编辑
export function updateDeviceTag(data) {
  return request({
    url: '/cmdbService/api/deviceTag/updateDeviceTag',
    method: 'post',
    data
  })
}

// 资源标签管理-删除
export function deleteDeviceTag(id) {
  return request({
    url: `/cmdbService/api/deviceTag/deleteDeviceTag?tagId=${id}`,
    method: 'post',
  })
}

// 资源标签管理-获取标签下设备列表
export function deviceTagRelationList(data) {
  return request({
    url: '/cmdbService/api/deviceTag/deviceTagRelationList',
    method: 'post',
    data
  })
}

// 资源标签管理-标签下删除设备
export function deleteDeviceTagRelation(data) {
  return request({
    url: '/cmdbService/api/deviceTag/deleteDeviceTagRelation',
    method: 'post',
    data
  })
}

// 资源标签管理-标签下添加设备
export function addDeviceTagRelation(data) {
  return request({
    url: '/cmdbService/api/deviceTag/addDeviceTagRelation',
    method: 'post',
    data
  })
}