import request from '@/utils/request'
export function fetchList(data) {
  return request({
    url: '/userService/api/pos/list',
    method: 'post',
    data
  })
}

export function fetchDetail(data) {
  return request({
    url: '/userService/api/pos/detail',
    method: 'post',
    data
  })
}

export function fetchDelete(posIds) {
  var data = { posIds }
  return request({
    url: '/userService/api/pos/delete',
    method: 'post',
    data
  })
}

export function fetchCreate(data) {
  return request({
    url: '/userService/api/pos/create',
    method: 'post',
    data
  })
}

export function fetchUpdate(data) {
  return request({
    url: '/userService/api/pos/update',
    method: 'post',
    data
  })
}
