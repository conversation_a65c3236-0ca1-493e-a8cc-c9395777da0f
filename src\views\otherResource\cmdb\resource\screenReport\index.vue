<template>
  <div class="main">
    <div class="main-grid">
      <div class="card full search-container">
        <div class="search-item">
          <el-radio-group v-model="quickSearchMode" @change="quickSearchChange">
            <el-radio-button label="1">本日</el-radio-button>
            <el-radio-button label="2">本周</el-radio-button>
            <el-radio-button label="3">本月</el-radio-button>
            <el-radio-button label="4">本年</el-radio-button>
          </el-radio-group>
        </div>
        <div class="search-item">
          <el-dropdown trigger="click" @command="searchModeChange">
            <el-button :type="quickSearchMode === '' ? 'primary' : 'default'">自定义时间<i
                class="el-icon-arrow-down el-icon--right" /></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="0"> 按日 </el-dropdown-item>
              <el-dropdown-item :command="1"> 按周 </el-dropdown-item>
              <el-dropdown-item :command="2"> 按月 </el-dropdown-item>
              <el-dropdown-item :command="3"> 按季 </el-dropdown-item>
              <el-dropdown-item :command="4"> 按年 </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <div class="search-item">
          <el-checkbox-group v-model="searchTypeValue">
            <el-checkbox border :label="1">有源</el-checkbox>
            <el-checkbox border :label="0">无源</el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="search-item">
          <el-select v-model="reportShowTypes" placeholder="请选择报表内容" multiple clearable collapse-tags>
            <el-option label="专题统计" value="1" />
            <el-option label="区域统计" value="2" />
            <el-option label="资源统计" value="3" />
            <el-option label="厂商统计" value="4" />
            <el-option label="资源厂商统计" value="5" />
          </el-select>
        </div>
        <div class="search-item">
          <el-button 
            v-if="checkButtonPermission('322401')"
            type="primary" 
            :loading="pageLoading" 
            @click="getData"
          >生成</el-button>
          <el-button @click="reset"
            style="background-color: rgba(150, 170, 222, 0.4);border-color:rgba(150, 170, 222, 0.4);color: #fff;">重置</el-button>
          <el-button 
            v-if="checkButtonPermission('322402')"
            :loading="exportLoading" 
            @click="handleExport"
            style="background-color: rgba(150, 170, 222, 0.4);border-color:rgba(150, 170, 222, 0.4);color: #fff;">导出报表</el-button>
        </div>
      </div>
      <div v-loading="topLoading" class="card chart-item" style="display: flex; justify-content: space-between;">
        <span />
        <div class="item-box">
          <div class="icon icon-SJ" />
          <div class="text">
            <span>{{ overveiw.installCount }}</span>
            <span>设备上架</span>
          </div>
        </div>
        <div class="item-box">
          <div class="icon icon-XJ" />
          <div class="text">
            <span>{{ overveiw.deactivationCount }}</span>
            <span>设备下架</span>
          </div>
        </div>
        <span />
      </div>
      <div v-loading="topLoading" class="card chart-item" style="display: flex; justify-content: space-between;">
        <span />
        <div class="item-box">
          <div class="icon icon-PK" />
          <div class="text">
            <span>{{ overveiw.inventoryLoss }}</span>
            <span>资产盘亏下架</span>
          </div>
        </div>
        <div class="item-box">
          <div class="icon icon-DQ" />
          <div class="text">
            <span>{{ overveiw.expirationOfMaintenance }}</span>
            <span>维保到期下架</span>
          </div>
        </div>
        <div class="item-box">
          <div class="icon icon-BF" />
          <div class="text">
            <span>{{ overveiw.obsolescence }}</span>
            <span>设备报废下架</span>
          </div>
        </div>
        <div class="item-box">
          <div class="icon icon-QT" />
          <div class="text">
            <span>{{ overveiw.otherReason }}</span>
            <span>其他原因下架</span>
          </div>
        </div>
        <span />

      </div>
      <div v-show="reportShowTypes.includes('1')" v-loading="chartLoading" class="card full">
        <div class="card-title">专题统计</div>
        <alarm-change :data="chartData" />
      </div>
      <div v-show="reportShowTypes.includes('2')" class="card full">
        <div class="card-title" style="margin-bottom: 1.48vh;">区域统计</div>
        <alarmTable :options="area.options" :data="area.data" :loading="area.loading" />
      </div>
      <div v-show="reportShowTypes.includes('3')" class="card full">
        <div class="card-title" style="margin-bottom: 1.48vh;">资源类型统计</div>
        <alarmTable :options="resource.options" :data="resource.data" :loading="resource.loading" />
      </div>
      <div v-show="reportShowTypes.includes('4')" class="card full">
        <div class="card-title" style="margin-bottom: 1.48vh;">厂商统计</div>
        <alarmTable :options="manufacturer.options" :data="manufacturer.data" :loading="manufacturer.loading" />
      </div>
      <div v-show="reportShowTypes.includes('5')" class="card full">
        <div class="card-title" style="margin-bottom: 1.48vh;">资源厂商统计</div>
        <alarmTable :options="resourceManufacturer.options" :data="resourceManufacturer.data"
          :loading="resourceManufacturer.loading" />
      </div>
    </div>

    <el-dialog v-dialogDrag :close-on-click-modal="false" title="选择时间范围" :visible.sync="dialogFormVisible"
      width="46.3vh" destroy-on-close>
      <selectTime :date-type="selectTimeDateType" :show-dialog="dialogFormVisible"
        @handCancle="e => dialogFormVisible = false" @handCreate="selectTimeCreate" />
    </el-dialog>
  </div>
</template>
<script>
import moment from 'moment'
import alarmTable from './new/alarmTable'
import alarmChange from './new/alarmChange'
import selectTime from './selectTime'
import {
  getOverview,
  getSubject,
  getAreaDataList,
  getResourceDataList,
  getManufacturerDataList,
  getResourceManufacturerDataList
} from '@/api/PMS/agentManagement/alarmStatisticalAnalysis'
import download from '@/views/otherResource/soc/mixins/download'
import permission from '@/views/otherResource/soc/mixins/permission'

export default {
  components: {
    alarmTable,
    alarmChange,
    selectTime
  },
  mixins: [download, permission],
  data() {
    return {
      query: {
        startDate: '',
        endDate: '',
        isSource: ''
      },
      reportShowTypes: ['1', '2', '3', '4', '5'],
      area: {
        options: [
          { label: '区域', prop: 'areaName' },
          { label: '总数', prop: 'total' },
          { label: '上架', prop: 'installCount' },
          { label: '下架（资产盘亏）', prop: 'inventoryLoss' },
          { label: '下架（维保到期）', prop: 'expirationOfMaintenance' },
          { label: '下架（设备报废）', prop: 'obsolescence' },
          { label: '下架（其他原因）', prop: 'otherReason' },
          { label: '维护', prop: 'repairCount' },
          { label: '超期', prop: 'exceedLimitCount' }
        ],
        data: [],
        loading: false
      },
      resource: {
        options: [
          { label: '资源类型', prop: 'typeName' },
          { label: '总数', prop: 'total' },
          { label: '上架', prop: 'installCount' },
          { label: '下架（资产盘亏）', prop: 'inventoryLoss' },
          { label: '下架（维保到期）', prop: 'expirationOfMaintenance' },
          { label: '下架（设备报废）', prop: 'obsolescence' },
          { label: '下架（其他原因）', prop: 'otherReason' },
          { label: '维护', prop: 'repairCount' },
          { label: '超期', prop: 'exceedLimitCount' }
        ],
        data: [],
        loading: false
      },
      manufacturer: {
        options: [
          { label: '厂商', prop: 'manufacturerName' },
          { label: '资源类型', prop: 'typeName' },
          { label: '总数', prop: 'total' },
          { label: '上架', prop: 'installCount' },
          { label: '下架（资产盘亏）', prop: 'inventoryLoss' },
          { label: '下架（维保到期）', prop: 'expirationOfMaintenance' },
          { label: '下架（设备报废）', prop: 'obsolescence' },
          { label: '下架（其他原因）', prop: 'otherReason' },
          { label: '维护', prop: 'repairCount' },
          { label: '超期', prop: 'exceedLimitCount' }
        ],
        data: [],
        loading: false
      },
      resourceManufacturer: {
        options: [
          { label: '资源类型', prop: 'typeName' },
          { label: '厂商', prop: 'manufacturerName' },
          { label: '总数', prop: 'total' },
          { label: '上架', prop: 'installCount' },
          { label: '下架（资产盘亏）', prop: 'inventoryLoss' },
          { label: '下架（维保到期）', prop: 'expirationOfMaintenance' },
          { label: '下架（设备报废）', prop: 'obsolescence' },
          { label: '下架（其他原因）', prop: 'otherReason' },
          { label: '维护', prop: 'repairCount' },
          { label: '超期', prop: 'exceedLimitCount' }
        ],
        data: [],
        loading: false
      },
      quickSearchMode: '1',
      searchTypeValue: [0, 1],
      overveiw: {
        installCount: 0,
        deactivationCount: 0,
        expirationOfMaintenance: 0,
        inventoryLoss: 0,
        obsolescence: 0,
        otherReason: 0
      },
      chartData: null,
      tableLoading: false,
      exportLoading: false,
      chartLoading: false,
      topLoading: false,
      dialogFormVisible: false,
      selectTimeDateType: 0
    }
  },
  computed: {
    pageLoading() {
      return this.topLoading || this.chartLoading || this.area.loading || this.resource.loading || this.manufacturer.loading || this.resourceManufacturer.loading
    }
  },
  created() {
    this.quickSearchChange('1')
    this.getData()
  },
  methods: {
    validateSearchType() {
      if (this.searchTypeValue.length === 0) {
        this.$message.error('至少选择一种有源/无源设备类型！')
        return false
      }
      if (this.searchTypeValue.length === 1) {
        this.query.isSource = this.searchTypeValue[0]
      } else if (this.searchTypeValue.length === 2) {
        this.query.isSource = undefined
      }
      return true
    },
    async getData() {
      if (this.validateSearchType()) {
        this.getOverview()
        this.getSubject()
        this.getTableData()
      }
    },
    getOverview() {
      this.topLoading = true
      getOverview(this.query).then(res => {
        this.overveiw = res.data.data
        this.topLoading = false
      }).catch(() => {
        this.topLoading = false
      })
    },
    getSubject() {
      this.chartLoading = true
      getSubject(this.query).then(res => {
        this.chartData = res.data.data
        this.chartLoading = false
      }).catch(() => {
        this.chartLoading = false
      })
    },
    getTableData() {
      ['area', 'resource', 'manufacturer', 'resourceManufacturer'].forEach((ele, index) => {
        this[ele].loading = true;
        [getAreaDataList, getResourceDataList, getManufacturerDataList, getResourceManufacturerDataList][index](this.query)
          .then(res => {
            this[ele].data = res.data.data
            this[ele].loading = false
          }).catch(() => {
            this[ele].loading = false
          })
      })
    },
    reset() {
      this.quickSearchMode = '1'
      this.quickSearchChange('1')
      this.searchTypeValue = [0, 1]
      this.reportShowTypes = ['1', '2', '3', '4', '5']
    },
    quickSearchChange(v) {
      switch (v) {
        case '1':
          this.query.startDate = moment().startOf('day').format('yyyy-MM-DD HH:mm:ss')
          this.query.endDate = moment().format('yyyy-MM-DD HH:mm:ss')
          break
        case '2':
          this.query.startDate = moment().locale('en').startOf('week').format('yyyy-MM-DD HH:mm:ss')
          this.query.endDate = moment().format('yyyy-MM-DD HH:mm:ss')
          break
        case '3':
          this.query.startDate = moment().startOf('month').format('yyyy-MM-DD HH:mm:ss')
          this.query.endDate = moment().format('yyyy-MM-DD HH:mm:ss')
          break
        case '4':
          this.query.startDate = moment().startOf('year').format('yyyy-MM-DD HH:mm:ss')
          this.query.endDate = moment().format('yyyy-MM-DD HH:mm:ss')
      }
    },
    searchModeChange(v) {
      if (v === '') return
      this.selectTimeDateType = v
      this.dialogFormVisible = true
    },
    selectTimeCreate(dataValue) {
      this.quickSearchMode = ''
      if (this.selectTimeDateType === 0) {
        // 日
        this.query.startDate = moment(dataValue[0]).format('yyyy-MM-DD HH:mm:ss')
        this.query.endDate = moment(dataValue[1]).endOf('day').format('yyyy-MM-DD HH:mm:ss')
      } else if (this.selectTimeDateType === 1) {
        // 周
        this.query.startDate = moment(dataValue).locale('en').startOf('week').format('yyyy-MM-DD HH:mm:ss')
        this.query.endDate = moment(dataValue).locale('en').endOf('week').format('yyyy-MM-DD HH:mm:ss')
      } else if (this.selectTimeDateType === 2 || this.selectTimeDateType === 3) {
        // 按月/按季
        this.query.startDate = moment(dataValue[0]).format('yyyy-MM-DD HH:mm:ss')
        this.query.endDate = moment(dataValue[1]).endOf('month').format('yyyy-MM-DD HH:mm:ss')
      } else {
        // 按年
        this.query.startDate = moment(dataValue[0]).format('yyyy-MM-DD HH:mm:ss')
        this.query.endDate = moment(dataValue[1]).endOf('year').format('yyyy-MM-DD HH:mm:ss')
      }
      this.dialogFormVisible = false
    },
    handleSizeChange(val) {
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.getTableData()
    },
    handleExport() {
      if (!this.validateSearchType()) return
      // 处理loading
      this.exportLoading = true
      const data = { ...this.query, reportShowTypes: this.reportShowTypes }
      const timeRange = moment(data.startDate).format('yyyy.MM.DD') + '-' + moment(data.endDate).format('yyyy.MM.DD')
      const url = '/cmdbService/instance-statistics/export'
      const title = `资源报表(${timeRange})`

      this.download({
        url,
        data,
        method: 'post',
        fileName: `${title}.doc`
      }, 'application/msword').then(res => {
        this.exportLoading = false
      }).catch(() => {
        this.exportLoading = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.card {
  background: rgba(37, 46, 63, 0.6);
  border-radius: 4px;
  padding: 16px;
  box-shadow: 0 0 3px #ccc;

  .card-title {
    //background-color: #f5f7fa;
    color: white;
    font-size: 18px;
    line-height: 24px;
    padding: 6px 24px;
    margin-bottom: 10px;
  }

  .item-box {
    overflow: hidden;
    text-align: center;

    .icon {
      width: 6vh;
      height: 6vh;
      background-size: 100% 100%;
      margin: 0 auto;
      float: left;
    }

    .icon-SJ {
      background-image: url('/static/img/device/SJ.png')
    }

    .icon-XJ {
      background-image: url('/static/img/device/XJ.png')
    }

    .icon-PK {
      background-image: url('/static/img/device/PK.png')
    }

    .icon-DQ {
      background-image: url('/static/img/device/DQ.png')
    }

    .icon-BF {
      background-image: url('/static/img/device/BF.png')
    }

    .icon-QT {
      background-image: url('/static/img/device/QT.png')
    }

    .text {
      float: left;
      margin-left: 1vh;
      text-align: left;

      span {
        display: block;
        height: 3vh;
        line-height: 3vh;
      }

      span:nth-child(1) {
        font-weight: 800;
        font-size: 2.2vh;
        //color: rgb(102, 102, 102);
        color: #fff;
      }

      span:nth-child(2) {
        font-size: 1.3vh;
        color: rgb(152, 152, 152);

      }
    }
  }
}

.main {
  overflow-y: scroll;
  height: calc(100vh - 70px);

  .main-grid {
    padding: 12px;
    display: grid;
    grid-template-columns: 1fr 2fr;
    grid-row-gap: 12px;
    grid-column-gap: 12px;

    .full {
      grid-column: 1/3;
    }

    .search-container {
      display: flex;

      .search-item {
        margin-right: 16px;

        .el-checkbox {
          margin-right: 0;
        }
      }
    }
  }
}
</style>
