/*
 * @Author: xiaoH<PERSON>
 * @Date: 2021-05-31 15:35:03
 * @LastEditors: xiaoHui
 * @LastEditTime: 2021-07-07 10:50:14
 * @Description:
 * @Version: 1.0.0
 * @FilePath: \itsm-ui-svn\src\api\cmdb\collection.js
 */
import request from '@/utils/request'

const url = '/cmdbService'
export function list(params) {
  return request({
    url: url + '/collection/getPage',
    method: 'post',
    data: params
  })
}

export function save(params) {
  return request({
    url: url + '/collection/save',
    method: 'post',
    data: params
  })
}

export function del(params) {
  return request({
    url: url + '/collection/delete',
    method: 'delete',
    data: params
  })
}

export function download(data) {
  return request({
    url: url + '/collection/downloadTemplate',
    method: 'get',
    params: data,
    responseType: 'blob'
  })
}

export function exportData(params) {
  return request({
    url: url + '/collection/exportData',
    method: 'get',
    params: params
  })
}

export function downloadReport(data) {
  return request({
    url: url + '/collection/downloadReport',
    method: 'get',
    params: data,
    responseType: 'blob'
  })
}
