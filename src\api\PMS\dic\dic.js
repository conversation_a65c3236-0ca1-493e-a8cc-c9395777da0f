import request from '@/utils/request'
export function fetchList(data) {
  return request({
    url: '/userService/api/dict/list',
    method: 'post',
    data
  })
}

export function fetchListByType(dicType) {
  const data = { dicType }
  return request({
    url: '/userService/api/dict/list',
    method: 'post',
    data
  })
}

export function fetchDetail(queryData) {
  return request({
    url: '/userService/api/dict/detail',
    method: 'post',
    queryData
  })
}

export function fetchDelete(dicIds) {
  var data = { dicIds }
  return request({
    url: '/userService/api/dict/delete',
    method: 'post',
    data
  })
}

export function fetchLinkageTree() {
  return request({
    url: '/userService/api/dict/getDicLinkageList',
    method: 'post'
  })
}

export function fetchCreate(data) {
  return request({
    url: '/userService/api/dict/create',
    method: 'post',
    data
  })
}

export function fetchUpdate(data) {
  return request({
    url: '/userService/api/dict/update',
    method: 'post',
    data
  })
}
