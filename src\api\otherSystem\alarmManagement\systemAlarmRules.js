
import request from '@/utils/request'

// 列表
export function fetchList(data) {
  return request({
    url: '/devService/api/alertRule/list',
    method: 'post',
    data
  })
}
// 添加
export function fetchCreate(data) {
  return request({
    url: '/devService/api/alertRule/insert',
    method: 'post',
    data
  })
}
// 详情
export function fetchDetail(data) {
  return request({
    url: '/devService/api/alertRule/get',
    method: 'post',
    data
  })
}

// 编辑
export function fetchUpdate(data) {
  return request({
    url: '/devService/api/alertRule/update',
    method: 'post',
    data
  })
}
// 删除
export function fetchDelete(data) {
  return request({
    url: '/devService/api/alertRule/delete',
    method: 'post',
    data
  })
}
// 设备属性
export function deviceProperties(data) {
  return request({
    url: '/devService/api/alertRule/queryModelAttr',
    method: 'post',
    data
  })
}

