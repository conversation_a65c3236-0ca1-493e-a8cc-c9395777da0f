.logo {
    float: left;

    width: 44.44450vh;;
    height: 100%;
    padding-right: 0vh;

}

.el-popover {
    // left: 210px !important;
    // top: 60px !important;
    width: 99%;
    // background-color: #465387 !important;
}

.el-menu {
    height: 7.4080vh;
    background: url(/static/img/top1.png) round !important;
}

.el-menu--horizontal>.el-menu-item {
    color: #ffffff;
}

.el-menu--horizontal>.el-menu-item:not(.is-disabled):focus,
.el-menu--horizontal>.el-menu-item:not(.is-disabled):hover,
.el-menu--horizontal>.el-submenu .el-submenu__title:hover {
    background-color: #409eff;
}

.el-menu--horizontal>.el-submenu .el-submenu__title {
    height: 3.3336vh;
    line-height: 3.3336vh;
    color: #ffffff;
    font-weight: bolder;
}

.el-menu--horizontal>.el-menu-item {
    height: 7.4080vh;
    line-height: 7.4080vh;
    color: #ffffff;
    font-weight: bolder;
    text-align: center;
    position: relative;
    padding: 0 1.5vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Add underline for active menu items - positioned directly under text */
.el-menu--horizontal>.el-menu-item.is-active::after {
    content: '';
    position: absolute;
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 2vh);
    height: 2px;
    background-color: #409eff;
    border-radius: 1px;
}

.el-menu--horizontal .el-menu-item:not(.is-disabled):focus,
.el-menu--horizontal .el-menu-item:not(.is-disabled):hover {
    color: #ffffff;
    font-weight: bolder;
}

.el-menu--horizontal>.el-menu-item.is-active {
    color: #ffffff !important;
    background-color: transparent !important;
}

.el-menu--horizontal>.el-submenu.is-active .el-submenu__title {
    color: #ffffff;
}

.el-menu-item:focus,
.el-menu-item:hover {
    background-color: #409eff !important;
}

.el-menu--horizontal>.el-submenu:focus .el-submenu__title,
.el-menu--horizontal>.el-submenu:hover .el-submenu__title {
    color: #ffffff;
    font-weight: bolder;
}

.el-scrollbar .el-menu-item,
.el-submenu__title {
    height: 3.3336vh !important;
    line-height: 3.3336vh !important;
}

.ht-logo {
    width: 34.4472vh;
    height: 7.4080vh;
    margin-top: 1.2964vh;
    float: left;
    margin-left: 0.9260vh;
}

.hamburger-container {
    padding: 1.6668vh 1.3890vh !important;
    float: right;
}

.el-menu-item {
    font-size: 1.2964vh;
    padding: 0vh 1.8520vh;
    margin: 0.1852vh 1.8520vh;
    border-radius: 0.4630vh;
}

.el-submenu__title {
    font-size: 1.2964vh;
    padding: 0vh 1.8520vh;
    margin: 0.1852vh 1.8520vh;
    border-radius: 0.4630vh;
}

#app .hideSidebar .el-submenu>.el-submenu__title {
    padding: 0 1.8520vh !important;
}

.wy-detail-div {
    float: left;
    width: 15.7420vh;
}

.wy-detail-div li {
    line-height: 2.7780vh;
    font-size: 1.3890vh;
    cursor: pointer;
}

.wy-detail-div li:hover {
    background-color: #a69e9e1c;
}

.wy-detail-div ul {
    list-style-type: none;
}

.wy-detail-title {
    font-weight: bolder;
    font-size: 1.4816vh;
    margin-left: 1.8520vh;
}

.left-menu .el-menu-item.is-active {
    color: #ffffff;
    font-weight: bolder;
    background-color: #409eff !important;
}

.left-menu .el-menu-item {
    color: hsla(0,0%,100%,.9)!important;
    font-weight: 700;
    border-bottom: unset !important;
    font-size: 1.4816vh;
    padding: 0vh 0.9260vh !important;
    font-family: Arial, Helvetica, sans-serif;
    height: 3.7040vh;
    line-height: 3.7040vh;
    border-radius: 0.2778vh;
}

.el-menu.el-menu--horizontal {
    border-bottom: unset !important;
}

/* Navigation bar menu items styling */
.navBarMenu {
    text-align: center !important;
    position: relative;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.navBarMenu .navA {
    width: 100% !important;
    text-align: center !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.menuDetail {
    display: flex;
    margin-left: 3.7040vh;
    overflow: auto;
    height: 80vh;
    width: 95%;
    flex-wrap: wrap;
}

.menuDetail_item {
    width: 20.3720vh;
}

.subLink {
    font-size: 1.5742vh;
    font-weight: bolder;
    color: #FFFFFF;
}

.threeSubLink {
    color: #d3d5da;
    font-size: 1.3890vh;
    height: 4.1670vh;
    line-height: 4.1670vh;
    font-weight: bolder;
    margin-left: 0.9260vh;
}

a:hover {
    color: #7190ea;
}

.threeSubLink:hover {
    background-color: #7190ea;
    color: #EFF0F2;
}

.subMenu {
    color: white !important;
    font-weight: bolder;
    font-size: 1.3890vh;
    margin-left: 0.9260vh;
}

.fourSubLink {
    color: #D3D5DA;
    font-size: 1.1112vh;
    height: 3.1484vh;
    line-height: 3.1484vh;
    font-weight: bolder;
    margin-left: 1.6668vh;
}

.fourSubLink:hover {
    background-color: #7190ea;
    color: #EFF0F2;
}




.headMenu {
    font-size: 1.2964vh;
    font-weight: bold;
    color: #FFFFFF !important;
    opacity: unset !important;
    text-align: left
}

.headMenu.el-menu-item.is-disabled {
    // background: #191E32 !important;
    cursor: default !important;
    padding: 0vh 1.3890vh !important;
}

.dialog-footer {
    text-align: center !important;
}

.information .el-form-item__label {
    background-color: #32bba0;
    text-align: center !important;
    border: #32bba0 0.0926vh solid;
    line-height: 3.7040vh !important;
    height: 3.7040vh !important;
    color: #eeeeee;
}

.information .el-form-item__content {
    border: #32bba0 0.0926vh solid;
    text-align: center !important;
    height: 3.7040vh;
}

.label_green {
    border-radius: 0.3704vh;
    box-shadow: 0 0.1852vh 0.3704vh rgba(0, 0, 0, .12), 0 0 0.5556vh rgba(0, 0, 0, .04);
    padding: 0.1852vh 1.3890vh;
    color: #3AC5A8;
    background: #ebf9f6;
    border-color: #b0e8dc;
    border: 0.0926vh solid;
}

.label_red {
    border-radius: 0.3704vh;
    box-shadow: 0 0.1852vh 0.3704vh rgba(0, 0, 0, .12), 0 0 0.5556vh rgba(0, 0, 0, .04);
    padding: 0.1852vh 1.3890vh;
    color: #C9353F;
    background: #fef0f0;
    border-color: #fbc4c4;
    border: 0.0926vh solid;
}

.label_gray {
    border-radius: 0.3704vh;
    box-shadow: 0 0.1852vh 0.3704vh rgba(0, 0, 0, .12), 0 0 0.5556vh rgba(0, 0, 0, .04);
    padding: 0.1852vh 1.3890vh;
    color: #909399;
    background: #f4f4f5;
    border-color: #d3d4d6;
    border: 0.0926vh solid;
}

.v-modal {
    position: unset !important;
    background: unset !important;
}

.wyzydiv .el-tree-node__content {
    white-space: normal;
}

.baseInfoManage {
    .form-item-width {
        width: 25.9280vh !important;
    }
}

.blueTitleTag {
    height: 1.9446vh;
    width: 0.4630vh;
    background: #409EFF;
    margin-right: 0.4630vh;
}

.blueTitle {
    display: flex;
    height: 5vh;
    align-items: center;
    font-weight: 900;
}


// .form-item-texarea {
//     width: 680px;
// }

// .form-item-number {
//     width: 120px;
// }
.el-popper {
    margin-top: 0vh !important;
}

.To-do-items {
    padding-top: 0.9vh;
    padding-left: 0.88889vh;
    clear: both;
    line-height: 1.8vh
}

.To-do-items-bg {
    background: #C9353F;
    border-radius: 0.5556vh;
    height: 9vh;
    border: #C9353F 0.0926vh solid;
}

.To-do-items_icon {
    float: left;
    padding: 1vh;
    background: url(/static/img/query.png) round !important;
    width: 5.33334vh;
    height: 5.33334vh;
    margin-left: 2.84445vh;
    margin-top: 0.9vh
}

.To-do-items_icon2 {
    float: left;
    padding: 1vh;
    background: url(/static/img/unexecuted.png) round !important;
    width: 5.33334vh;
    height: 5.33334vh;
    margin-left: 2.84445vh;
    margin-top: 0.9vh
}

.To-do-items_cn {
    color: #FFFFFF;
    float: left;
    text-align: center;
    padding-top: 1.2vh;
    padding-left: 1.77778vh
}

// 改变系统滚动条样式 start
.docletItem::-webkit-scrollbar {
    width: 0.6482vh;
    height: 0.9260vh;
    background-color: transparent;
}

.docletItem:-webkit-scrollbar-track {
    background: linear-gradient(to right, #2FB69C, #3AC5A8);
}

.docletItem::-webkit-scrollbar-thumb {
    background: linear-gradient(to right, #2FB69C, #3AC5A8);
    border-radius: 0.5556vh;
}

#codeMainAll::-webkit-scrollbar {
    width: 0.6482vh;
    height: 0.9260vh;
    background-color: transparent;
}

#codeMainAll:-webkit-scrollbar-track {
    background: linear-gradient(to right, #2FB69C, #3AC5A8);
}

#codeMainAll::-webkit-scrollbar-thumb {
    background: linear-gradient(to right, #2FB69C, #3AC5A8);
    border-radius: 0.5556vh;
}

.patrolPlant::-webkit-scrollbar {
    width: 0.6482vh;
    height: 0.9260vh;
    background-color: transparent;
}

.patrolPlant:-webkit-scrollbar-track {
    background: linear-gradient(to right, #2FB69C, #3AC5A8);
}

.patrolPlant::-webkit-scrollbar-thumb {
    background: linear-gradient(to right, #2FB69C, #3AC5A8);
    border-radius: 0.5556vh;
}

.patrolRecordDiv::-webkit-scrollbar {
    width: 0.6482vh;
    height: 0.9260vh;
    background-color: transparent;
}

.patrolRecordDiv:-webkit-scrollbar-track {
    background: linear-gradient(to right, #2FB69C, #3AC5A8);
}

.patrolRecordDiv::-webkit-scrollbar-thumb {
    background: linear-gradient(to right, #2FB69C, #3AC5A8);
    border-radius: 0.5556vh;
}

.deviceSearchItem::-webkit-scrollbar {
    width: 0.6482vh;
    height: 0.9260vh;
    background-color: transparent;
}

.deviceSearchItem:-webkit-scrollbar-track {
    background: linear-gradient(to right, #999999, #999999);
}

.deviceSearchItem::-webkit-scrollbar-thumb {
    background: linear-gradient(to right, #999999, #999999);
    border-radius: 0.5556vh;
}

.areaItemSearch::-webkit-scrollbar {
    width: 0.6482vh;
    height: 0.9260vh;
    background-color: transparent;
}

.areaItemSearch:-webkit-scrollbar-track {
    background: linear-gradient(to right, #999999, #999999);
}

.areaItemSearch::-webkit-scrollbar-thumb {
    background: linear-gradient(to right, #999999, #999999);
    border-radius: 0.5556vh;
}

// 改变系统滚动条样式 end

.el-date-editor .el-range-separator {
    width: 10% !important;
}

.el-table .cell {
    text-align: center !important;
}

.Bill {
    list-style: none;
    margin: 0vh;
    padding: 0;
}

.Bill li a {
    text-align: center;
    padding: 1.5vh;
    border-bottom: 0.0926vh solid #C9353F;
    padding-left: 0vh;
    height: 7vh;
    line-height: 1.8520vh;
    color: #ffffff;
    display: block;
    background: #C9353F;
    font-size: 1.2964vh
}

.Bill li a:hover {
    background: #ca99a6;
    border-bottom: 0.0926vh solid #ca99a6;
}

//   图片附件样式
.picDetail {
    margin-left: 6.4820vh;
}

.picDetail .el-upload-list__item {
    width: 9.2600vh;
    height: 9.2600vh;
}

.picDetail .el-upload-list__item-status-label {
    display: none !important;

}

.picDetail .el-upload--picture-card {
    display: none;
}

.picDetail .el-upload-list__item-delete {
    display: none !important;
}

#user-add-box .el-form-item {
    width: 47%;
}

.patrolRecord .el-form-item__content {
    width: 69.4500vh;
}

.No-query {
    line-height: 3.7040vh;
}

.a:focus,
a:hover {
    color: #7190ea !important;
}

.el-menu-item:focus,
.el-menu-item:hover {
    color: #7190ea !important;
}

// 改变系统滚动条样式 start
.docletItem::-webkit-scrollbar {
    width: 0.6482vh;
    height: 0.9260vh;
    background-color: transparent;
}

.docletItem:-webkit-scrollbar-track {
    background: linear-gradient(to right, #409EFF, #409EFF);
}

.docletItem::-webkit-scrollbar-thumb {
    background: linear-gradient(to right, #409EFF, #409EFF);
    border-radius: 0.5556vh;
}

.tableDiv::-webkit-scrollbar {
    width: 0.6482vh;
    height: 0.9260vh;
    background-color: transparent;
}

.tableDiv:-webkit-scrollbar-track {
    background: linear-gradient(to right, rgba(9, 72, 148, 1), rgba(9, 72, 148, 1));
}

.tableDiv::-webkit-scrollbar-thumb {
    background: linear-gradient(to right, rgba(9, 72, 148, 1), rgba(9, 72, 148, 1));
    border-radius: 0.5556vh;
}

// 改变系统滚动条样式 end


.achivementTable tbody tr:hover>td {
    background-color: #ffffff !important;
}

// .el-select__input {
//     width: 100% !important;
// }

.el-checkbox__inner {
    border: 0.0926vh solid #6A7286 !important;
}

.bg1 .el-input--medium .el-input__inner {
    background-color: #192038 !important;
}

.bg1 .el-dialog__body {
    background-color: #181f35 !important;
    color: rgba(255, 255, 255, 0.9) !important; // Make PMS dialog text lighter
}

/* PMS弹窗内文字可见性改进 */
.bg1 .el-dialog__body .el-form-item__label,
.bg1 .el-dialog__body .el-text,
.bg1 .el-dialog__body p,
.bg1 .el-dialog__body span,
.bg1 .el-dialog__body div {
    color: rgba(255, 255, 255, 0.9) !important; // Make all PMS dialog text lighter
}

.bg1 .el-dialog__body .text-muted,
.bg1 .el-dialog__body .text-secondary,
.bg1 .el-dialog__body .el-form-item__content {
    color: rgba(255, 255, 255, 0.85) !important; // Slightly dimmed but visible
}

/* PMS弹窗关闭按钮增强 */
.bg1 .el-dialog__headerbtn .el-dialog__close {
    color: rgba(255, 255, 255, 0.9) !important;
    font-size: 18px !important;
    font-weight: bold !important;
    background: rgba(0, 142, 254, 0.3) !important;
    border-radius: 50% !important;
    width: 24px !important;
    height: 24px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.3s ease !important;
    border: 1px solid rgba(0, 142, 254, 0.5) !important;
}

.bg1 .el-dialog__headerbtn .el-dialog__close:hover {
    color: #ffffff !important;
    background: rgba(0, 142, 254, 0.6) !important;
    border-color: rgba(0, 142, 254, 1) !important;
    box-shadow: 0 0 8px rgba(0, 142, 254, 0.8) !important;
    transform: scale(1.1) !important;
}

.main-menu li.active {
    .main_menu_button {
        position: absolute;
        top: 70vh;
        left: 56.88896vh;
        width: 8.88890vh;
        cursor: pointer;
        background: url(/static/img/menu-button-active.svg )no-repeat;
        height: 11vh;
        background-size: 100%;
    }
}

.bg1 .el-radio__inner {
    border: 0.0926vh solid #ffffff !important;
}

.bg1 .el-date-editor .el-range-input {
    color: #ffffff;
    background-color: #171E34;
}

.el-tabs--card>.el-tabs__header .el-tabs__item {
    background-color: #E7E7E7;
    color: #606266;
    border-radius: 0.4630vh;
    font-weight: bolder;
    font-size: 1.4816vh;
}

.el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
    border: 0.0926vh solid #ffffff;
    color: #ffffff;
    background-color: #409EFF;
}

.bg1 .el-tabs--card>.el-tabs__header .el-tabs__item {
    background-color: rgba(9, 72, 148, 1) !important;
    color: #ffffff;
    border-radius: 0.4630vh;
    font-weight: bolder;
    font-size: 1.4816vh;
    border-left: 0.0926vh solid rgba(9, 72, 148, 1) !important;
}

.bg1 .el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
    border: 2px solid rgba(0, 142, 254, 1) !important; // Add consistent border
    color: #ffffff !important;
    background: linear-gradient(180deg, rgba(0, 145, 250, 1) 0%, rgba(9, 72, 148, 1) 100%) !important; // Use gradient like global
    box-shadow: 0 0 15px rgba(0, 142, 254, 0.8), inset 0 0 8px rgba(255, 255, 255, 0.2) !important; // Add glow
    transform: translateY(-1px) !important; // Minimal movement to prevent overflow
    position: relative !important;
    z-index: 10 !important;
    margin: 1px !important; // Add margin to contain effects
    font-weight: bold !important;
}

.el-notification .iconfont-red {
    font-size: 2.7780vh;
    margin-top: 0.9260vh;
    color: #C9353F;
}

.el-form--inline .el-form-item {
    display: inline-flex !important;
    ;
}

.LodingTitle {
    min-width: 6.4820vh;
}

/* 隐藏logo */
.iconqingxiLOGO {
    display: none !important;
}

// .el-tooltip__popper.is-dark {
//     background: #303133 !important;
//     color: #FFF !important;
// }

//根据规范修改公共样式start
.bg1 .el-tabs--card>.el-tabs__header {
    border-bottom: unset !important;
}

.bg1 .el-tabs--card>.el-tabs__header .el-tabs__nav {
    border: unset !important;
}

// Hide all ::after pseudo-elements that create line segments under tabs in PMS
.bg1 .el-tabs__item::after {
    display: none !important;
}

.bg1 .el-tabs__nav-wrap::after {
    display: none !important;
}

.bg1 .el-tabs__active-bar {
    display: none !important;
}

.bg1 .el-form-item__label {
    color: #ffffff !important;
}

.bg1 .el-table--border td,
.bg1 .el-table--border th {
    border-right: unset !important;
}

.bg1 .el-table td,
.bg1 .el-table th.is-leaf {
    border-bottom: 0.0926vh solid #2B3E78 !important;
}

.bg1 .el-pagination.is-background .el-pager li:not(.disabled):hover {
    color: rgba(0, 142, 254, 1) !important; // Use consistent blue hover color
    background-color: rgba(0, 142, 254, 0.2) !important;
}

.bg1 .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: rgba(0, 142, 254, 1) !important; // Changed to blue as requested
    color: #ffffff !important;
    border: 1px solid rgba(0, 142, 254, 1) !important;
}

// Improve visibility of unselected pagination items
.bg1 .el-pagination.is-background .el-pager li {
    color: rgba(255, 255, 255, 0.8) !important; // Make unselected pages more visible
}

.bg1 .el-pagination.is-background .el-pager li.disabled {
    color: rgba(255, 255, 255, 0.3) !important; // Make disabled pages dimmed but visible
}

.bg1 .el-table thead th {
    background: #3753A7 !important;
    color: #ffffff !important;
    font-weight: 600 !important; // Made bolder than table content
}

.bg1 .el-table--medium td, .el-table--medium th {
    padding: 0.4630vh 0 !important;
}

.bg1 .el-table__fixed-right::before, .el-table__fixed::before {
    background-color: unset !important;
}

.bg1 .el-table--border,
.bg1 .el-table--group {
    border: 0.0926vh solid #2A314B !important;
}

.bg1 .el-table {
    color: #ffffff !important;
}

.bg1 .el-pagination__total {
    color: #ffffff !important;
}

.bg1 .el-tag.el-tag--danger {
    background-color: #C9353F !important;
}

.bg1 .el-input__inner {
    background-color: #22315D !important;
    border: 0.0926vh solid #3550A0 !important;
    color: #ffffff !important;
}

.bg1 .el-table--border::after,
.bg1 .el-table--group::after,
.bg1 .el-table::before {
    background-color: #1b233b !important;
}

.bg1 .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #3753A7 !important;
    border-color: #3753A7;
}

.bg1 .el-pagination.is-background .btn-next,
.bg1 .el-pagination.is-background .btn-prev,
.bg1 .el-pagination.is-background .el-pager li {
    background-color: rgba(9, 72, 148, 1) !important; // Use consistent background color
    color: rgba(255, 255, 255, 0.8) !important; // Make more visible
    border-radius: 0.4630vh;
}

.bg1 .el-pagination.is-background .btn-next:hover,
.bg1 .el-pagination.is-background .btn-prev:hover {
    color: rgba(0, 142, 254, 1) !important; // Add hover effect
    background-color: rgba(0, 142, 254, 0.2) !important;
}

.bg1 .el-pagination__jump {
    color: rgba(255, 255, 255, 0.9) !important; // Make jump text more visible
}

.bg1 .el-pagination__total {
    color: rgba(255, 255, 255, 0.9) !important; // Make total text more visible
}

// Additional comprehensive pagination styles for PMS
.bg1 .el-pagination .el-pager li.active {
    background-color: rgba(0, 142, 254, 1) !important; // Blue background for active page
    color: #ffffff !important; // White text on blue background
}

.bg1 .el-pagination:not(.is-background) .el-pager li {
    color: rgba(255, 255, 255, 0.8) !important;
    background-color: rgba(9, 72, 148, 1) !important;
}

.bg1 .el-pagination:not(.is-background) .el-pager li.active {
    background-color: rgba(0, 142, 254, 1) !important;
    color: #ffffff !important;
}

// High specificity selectors for PMS to override Element UI defaults
.bg1 .el-pagination .el-pager li.number.active {
    background-color: rgba(0, 142, 254, 1) !important;
    color: #ffffff !important;
    border-color: rgba(0, 142, 254, 1) !important;
}

.bg1 .el-pagination.is-background .el-pager li.number.active {
    background-color: rgba(0, 142, 254, 1) !important;
    color: #ffffff !important;
    border-color: rgba(0, 142, 254, 1) !important;
}

// Ultimate override for PMS active pagination - highest specificity
body .bg1 .el-pagination .el-pager li.active,
body .bg1 .el-pagination .el-pager li.number.active,
body .bg1 .el-pagination.is-background .el-pager li.active,
body .bg1 .el-pagination.is-background .el-pager li.number.active {
    background-color: rgba(0, 142, 254, 1) !important;
    color: #ffffff !important;
    border-color: rgba(0, 142, 254, 1) !important;
}

.bg1 .el-table--enable-row-hover .el-table__body tr:hover>td {
    background-color: unset !important;
}

.bg1 .el-table--striped .el-table__body tr.el-table__row--striped.current-row td,
.bg1 .el-table__body tr.current-row>td,
.bg1 .el-table__body tr.hover-row.current-row>td,
.bg1 .el-table__body tr.hover-row.el-table__row--striped.current-row>td,
.bg1 .el-table__body tr.hover-row.el-table__row--striped>td,
.bg1 .el-table__body tr.hover-row>td {
    background-color: #1B2441 !important;
}

.bg1 .el-input-number__decrease,
.bg1 .el-input-number__increase {
    background: #1C274C !important;
    color: #ffffff !important;
}

.bg1 .el-input-number__decrease {
    border-right: 0.0926vh solid #1C274C !important;
}

.bg1 .el-input-number__increase {
    border-left: 0.0926vh solid #1C274C !important;
}

/* PMS统一下拉框样式 - 确保所有下拉框都使用渐变蓝色 */
.bg1 .el-select-dropdown,
.bg1 .el-cascader-panel,
.bg1 .el-dropdown-menu,
.bg1 .el-popover,
.bg1 .el-picker-panel,
.bg1 .el-autocomplete-suggestion,
.bg1 .el-time-panel,
.bg1 .el-date-picker {
    background: linear-gradient(180deg, rgba(4, 104, 192, 0.9) 0%, rgba(5, 84, 159, 0.9) 100%) !important;
    border: 1px solid rgba(0, 142, 254, 0.6) !important;
    box-shadow: 0 4px 12px rgba(0, 142, 254, 0.3) !important;
}

/* PMS下拉框选项基础样式 */
.bg1 .el-select-dropdown__item,
.bg1 .el-cascader-node,
.bg1 .el-dropdown-menu__item,
.bg1 .el-autocomplete-suggestion__item,
.bg1 .el-time-spinner__item {
    color: #eee !important;
    background-color: transparent !important;
}

/* PMS下拉框悬停效果 */
.bg1 .el-select-dropdown__item.hover,
.bg1 .el-select-dropdown__item:hover,
.bg1 .el-cascader-node:hover,
.bg1 .el-dropdown-menu__item:hover,
.bg1 .el-autocomplete-suggestion__item:hover,
.bg1 .el-time-spinner__item:hover {
    background: linear-gradient(90deg, rgba(0, 142, 254, 0.6) 0%, rgba(0, 145, 250, 0.4) 100%) !important;
    color: #00d3e9 !important;
}

/* PMS下拉框选中状态 */
.bg1 .el-select-dropdown__item.selected,
.bg1 .el-cascader-node.is-active,
.bg1 .el-dropdown-menu__item.is-active,
.bg1 .el-autocomplete-suggestion__item.highlighted,
.bg1 .el-time-spinner__item.active {
    background: linear-gradient(90deg, rgba(0, 145, 250, 0.8) 0%, rgba(9, 72, 148, 0.8) 100%) !important;
    color: #ffffff !important;
}

/* PMS强化下拉框文字颜色 - 特别针对数字文本如"10s", "20s"等 */
.bg1 .el-select-dropdown__item span,
.bg1 .el-select-dropdown__item div,
.bg1 .el-select-dropdown__item,
.bg1 .el-dropdown-menu__item span,
.bg1 .el-dropdown-menu__item div,
.bg1 .el-dropdown-menu__item {
    color: rgba(255, 255, 255, 0.9) !important; // 确保所有PMS下拉框文字都是浅色
}

/* PMS针对可能的深色数字文本 */
.bg1 .el-select-dropdown__item .el-option__text,
.bg1 .el-select-dropdown__item .el-option__label,
.bg1 .el-dropdown-menu__item .text,
.bg1 .el-dropdown-menu__item .label {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* PMS最高优先级选择器 - 确保覆盖所有Element UI默认样式 */
body .bg1 .el-select-dropdown,
body .bg1 .el-cascader-panel,
body .bg1 .el-dropdown-menu,
body .bg1 .el-popover,
body .bg1 .el-picker-panel,
body .bg1 .el-autocomplete-suggestion,
body .bg1 .el-time-panel,
body .bg1 .el-date-picker {
    background: linear-gradient(180deg, rgba(4, 104, 192, 0.9) 0%, rgba(5, 84, 159, 0.9) 100%) !important;
    border: 1px solid rgba(0, 142, 254, 0.6) !important;
    box-shadow: 0 4px 12px rgba(0, 142, 254, 0.3) !important;
}

/* PMS覆盖任何可能的白色背景 */
body .bg1 .el-select-dropdown.el-popper,
body .bg1 .el-cascader-panel.el-popper,
body .bg1 .el-dropdown-menu.el-popper,
body .bg1 .el-popover.el-popper,
body .bg1 .el-picker-panel.el-popper {
    background: linear-gradient(180deg, rgba(4, 104, 192, 0.9) 0%, rgba(5, 84, 159, 0.9) 100%) !important;
    border: 1px solid rgba(0, 142, 254, 0.6) !important;
}

/* PMS特殊处理一些可能遗漏的下拉框组件 */
.bg1 .el-select__popper,
.bg1 .el-cascader__dropdown,
.bg1 .el-autocomplete__popper,
.bg1 .el-time-picker__popper,
.bg1 .el-date-picker__popper {
    background: linear-gradient(180deg, rgba(4, 104, 192, 0.9) 0%, rgba(5, 84, 159, 0.9) 100%) !important;
    border: 1px solid rgba(0, 142, 254, 0.6) !important;
    box-shadow: 0 4px 12px rgba(0, 142, 254, 0.3) !important;
}

/* PMS确保所有popper类型的下拉框都使用渐变蓝色 */
.bg1 .el-popper[x-placement^="bottom"],
.bg1 .el-popper[x-placement^="top"],
.bg1 .el-popper[x-placement^="left"],
.bg1 .el-popper[x-placement^="right"] {
    background: linear-gradient(180deg, rgba(4, 104, 192, 0.9) 0%, rgba(5, 84, 159, 0.9) 100%) !important;
    border: 1px solid rgba(0, 142, 254, 0.6) !important;
}

/* PMS最高优先级 - 确保所有下拉框内的文字都是浅色 */
body .bg1 .el-select-dropdown__item,
body .bg1 .el-select-dropdown__item *,
body .bg1 .el-dropdown-menu__item,
body .bg1 .el-dropdown-menu__item *,
body .bg1 .el-cascader-node,
body .bg1 .el-cascader-node *,
body .bg1 .el-autocomplete-suggestion__item,
body .bg1 .el-autocomplete-suggestion__item * {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* PMS特别处理实时配置等数字文本 */
body .bg1 .el-select-dropdown__item:not(.is-disabled),
body .bg1 .el-dropdown-menu__item:not(.is-disabled) {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* PMS禁用状态的选项使用稍微暗一些的颜色 */
body .bg1 .el-select-dropdown__item.is-disabled,
body .bg1 .el-dropdown-menu__item.is-disabled {
    color: rgba(255, 255, 255, 0.5) !important;
}

/* PMS特别修复实时告警页面设置齿轮图标下拉框中的时间选项文字颜色 */
.bg1 .el-popover .el-radio-group .el-radio,
.bg1 .el-popover .el-radio-group .el-radio__label,
.bg1 .el-popover .el-radio__label {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* PMS确保所有弹出框内的单选按钮文字都是浅色 */
.bg1 .el-popover .el-radio,
.bg1 .el-popover .el-radio *,
.bg1 .el-popover .el-checkbox,
.bg1 .el-popover .el-checkbox * {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* PMS针对设备故障管理实时告警页面的特定样式 */
.bg1 .el-popover[x-placement^="bottom"] .el-radio-group .el-radio__label,
.bg1 .el-popover[x-placement^="top"] .el-radio-group .el-radio__label {
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 500 !important;
}

/* PMS最高优先级 - 确保所有弹出框内的单选按钮文字都是浅色 */
body .bg1 .el-popover .el-radio__label,
body .bg1 .el-popover .el-radio-group .el-radio__label,
body .bg1 .el-popover .el-checkbox__label {
    color: rgba(255, 255, 255, 0.95) !important;
}

/* PMS特别针对时间设置弹出框 */
body .bg1 .el-popover .el-radio__label:after,
body .bg1 .el-popover .el-radio__label:before {
    color: rgba(255, 255, 255, 0.95) !important;
}

/* PMS覆盖任何可能的深色文本样式 */
.bg1 .el-popover .el-radio__input + .el-radio__label {
    color: rgba(255, 255, 255, 0.95) !important;
}

.bg1 .el-tag.el-tag--info {
    background-color: #1C274C !important;
    border-color: #31374b !important;
}

.bg1 .el-input-group__append,
.bg1 .el-input-group__prepend {
    border: unset !important;
}

.bg1 .el-table__body-wrapper {
    background-color: #1b233b !important;
}

.bg1 .el-textarea__inner {
    background-color: #1b233b !important;
    color: #ffffff !important;
    border: 0.0926vh solid #1b233b !important;
}

.bg1 .el-checkbox__label {
    color: #ffffff !important;
}

.bg1 .el-table__header-wrapper thead th {
    background: #3753A7 !important;
    color: #FFFFFF !important;
    // border: 0.1px solid #192038;
}

.bg1 .el-dialog {
    border: 0.0926vh solid #2B3E78 !important;
}

.bg1 .el-dialog__header {
    padding: 1.3890vh 1.8520vh 0.9260vh;
    background-color: #3753A7 !important;
    color: #ffffff !important;
}

.bg1 .el-dialog__title {
    color: #ffffff !important;
}

.el-switch.is-checked .el-switch__core {
    border-color: #409EFF !important;
    background-color: #409EFF !important;
}

.bg1 .el-button--primary {
    color: #ffffff !important;
    background: #3753A7 !important;
    border: 0.0926vh solid #3753A7 !important;
}

.bg1 .deviceSearchDiv .el-input-group__append {
    background-color: #3753A7;
}

label {
    font-weight: 500 !important;
}
//根据规范修改公共样式end

.mapAlarmNotify {
    background-color: #336BDC !important;
    border: 0.0926vh solid #336BDC !important;
    color: #ffffff !important;
    opacity: 0.8;
}

.mapAlarmNotifyFont {
    color: #ffffff !important;
}

.mapAlarmNotifyFont:hover {
    color: #ffffff !important;
}

.mapAlarmNotify .el-notification__closeBtn {
    color: #ffffff !important;
}
