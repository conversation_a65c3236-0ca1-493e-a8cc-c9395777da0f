<template>
  <div :class="classObj" class="app-wrapper">
    <div style="height: 6.4820vh;">
      <sidebar class="sidebar-container" />
      <navbar />
    </div>
    <div ref="mainElement" class="main-container" :style="{ 'margin-left': (sidebar.opened ? leftWith2 : leftWith1) }">
      <app-main />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { Navbar, Sidebar, AppMain } from './components'
import ResizeMixin from './mixin/ResizeHandler'
export default {
  name: 'Layout',
  components: {
    Navbar,
    Sidebar,
    AppMain
  },
  mixins: [ResizeMixin],
  data() {
    return {
      leftWith1: '0vh',
      leftWith2: '24.0760vh'
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar'
    ]),
    sidebar() {
      return this.$store.state.app.sidebar
    },
    device() {
      return this.$store.state.app.device
    },
    fixedHeader() {
      return this.$store.state.settings.fixedHeader
    },
    classObj() {
      return {
        hideSidebar: false, // 修改侧边栏是否隐藏为必定不隐藏
        openSidebar: true,
        withoutAnimation: false,
        // hideSidebar: !this.sidebar.opened,
        // openSidebar: this.sidebar.opened,
        // withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    }
  },
  mounted() {
    if (this.$route.name === 'Dashboard') {
      this.handleClickOutside()
    } else {
      this.toggleSideBar()
    }
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/openSideBar', { withoutAnimation: false })
    },
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
@import "~@/styles/variables.scss";

.app-wrapper {
  background-color: rgba(9, 72, 148, 1);
  @include clearfix;
  position: relative;
  // height: 100%;
  min-height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 5.0004vh)
}

.mobile .fixed-header {
  width: 100%;
}
</style>
