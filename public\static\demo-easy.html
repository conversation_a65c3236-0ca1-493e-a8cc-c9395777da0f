<!doctype html>
<html>
<head>
    <title></title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Cache-Control" content="no-cache, must-revalidate" />
    <meta http-equiv="Expires" content="0" />
</head>
<style>
.plugin {
    width: 800px;
    height: 500px;
}
</style>
<body>
<div>
    <div id="divPlugin" class="plugin"></div>
</div>
</body>
<script src="js/jquery-1.7.1.min.js"></script>
<script src="js/webVideoCtrl.js"></script>
<script src="js/demo.js"></script>
<script>
 function initPlugin(info) {
	// console.log(info)
	if (info === null || info === undefined || info.ip === null || info.port === null || info.account === null || info.pwd === null) {
		// alert("无此摄像头信息,或摄像头信息不全");
		return '无此摄像头信息,或摄像头信息不全'
	}
	// 检查插件是否已经安装过
	if (WebVideoCtrl.I_CheckPluginInstall() == -1) {
		// console.log("您还未安装过插件，双击开发包目录里的WebComponents.exe安装！");
		return
	}
	var oPlugin = {
        iWidth: 800, // plugin width
        iHeight: 500 // plugin height
    }
	var oLiveView = {
        iProtocol: 1, // protocol 1：http, 2:https
        szIP: info.ip, // protocol ip
        szPort: info.port, // protocol port
        szUsername: info.account, // device username
        szPassword: info.pwd, // device password
        iStreamType: 2, // stream 1：main stream  2：sub-stream  3：third stream  4：transcode stream
        iChannelID: 1, // channel no
        bZeroChannel: false // zero channel
    }

	var iRet = WebVideoCtrl.I_Login(oLiveView.szIP, oLiveView.iProtocol, oLiveView.szPort, oLiveView.szUsername, oLiveView.szPassword, {
		success: function(xmlDoc) {
			console.log(oLiveView.szIP + ' 登录成功！')
			// 开始预览
			var szDeviceIdentify = oLiveView.szIP
			setTimeout(function() {
				var iRet2 = WebVideoCtrl.I_StartRealPlay(szDeviceIdentify, {
					iStreamType: oLiveView.iStreamType,
					iChannelID: oLiveView.iChannelID,
					bZeroChannel: oLiveView.bZeroChannel
				})
			}, 1000)
		},
		error: function() {
			console.log(oLiveView.szIP + ' 登录失败！')
		}
	})
	// 关闭浏览器
   $(window).unload(function() {
        WebVideoCtrl.I_Stop()
   })
}
$(function() {

})

</script>
</html>