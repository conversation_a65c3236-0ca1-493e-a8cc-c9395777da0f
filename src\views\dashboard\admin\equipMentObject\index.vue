<template>
  <div style="height: 100%">
    <chart-box title="设备概况" style="height: 29%">
      <div class="sbglDiv1">
        <div class="sbglDiv" @click="showEquipMentTotalList">
          <div class="sbglBox">
            <div class="left-sbgl">
              <div class="sbgl-icon">
                <div class="icon icon-total"></div>
              </div>
              <div class="sbgl-text">总量</div>
            </div>
            <div class="right-sbgl">
              <div class="sbgl-number">{{ keyModel.deviceTotalNum || 0 }}</div>
            </div>
          </div>
        </div>
        <div class="sbglDiv" @click="showOnlineList">
          <div class="sbglBox">
            <div class="left-sbgl">
              <div class="sbgl-icon">
                <div class="icon icon-online"></div>
              </div>
              <div class="sbgl-text">在线</div>
            </div>
            <div class="right-sbgl">
              <div class="sbgl-number">{{ keyModel.deviceOnlineNum || 0 }}</div>
            </div>
          </div>
        </div>
        <div class="sbglDiv"  @click="outOfContact">
          <div class="sbglBox">
            <div class="left-sbgl">
              <div class="sbgl-icon">
                <div class="icon icon-off-contact"></div>
              </div>
              <div class="sbgl-text">离线</div>
            </div>
            <div class="right-sbgl">
              <div class="sbgl-number">{{ keyModel.deviceLoseNum || 0}}</div>
            </div>
          </div>
        </div>
        <div class="sbglDiv" @click="onlinePercentDialogShow = true">
          <div class="sbglBox">
            <div class="left-sbgl">
              <div class="sbgl-icon">
                <div class="icon icon-online-percent"></div>
              </div>
              <div class="sbgl-text">在线率</div>
            </div>
            <div class="right-sbgl">
              <div class="sbgl-number">{{ getPercent() }}%</div>
            </div>
          </div>
        </div>
      </div>
    </chart-box>
    <chart-box title="设备占比分析" style="height: 28%">
      <ratioAnalysis :key-model="keyModel.accountList" :device-total-num="keyModel.deviceTotalNum" />
    </chart-box>
    <chart-box title="在线率排行" style="height: 43%">
      <!-- <baseAnalysis :key-model="keyModel.basisList" /> -->
      <!-- <work-order /> -->
      <failRateRank :key-model="alarmOverviewData.onlineRateList" />
    </chart-box>
    <div class="bg1">
      <div class="equipMentDiaLogDiv">
        <el-dialog ref="dialog" v-dialogDrag append-to-body class="layerManager dashboard-dialog" :title="title"
          :visible.sync="equipMentTotalListValue.showDialog" :width="equipMentTotalListDialogWidth"
          :before-close="handleClose">
          <equipMentTotalList ref="equipMentTotalList" :sys-type="sysType" v-bind="equipMentTotalListValue"
            :selected-area-code="selectedAreaCode" @positionMap="positionMap" @resetChoose="resetChoose" />
        </el-dialog>
        <el-dialog ref="onlinPercentDialog" v-dialogDrag append-to-body class="layerManager dashboard-dialog"
          title="历史在线率列表" :visible.sync="onlinePercentDialogShow" width="142.22vh">
          <online-percent ref="onlinePercent" :sys-type="sysType" :show="onlinePercentDialogShow" />
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import ratioAnalysis from './ratioAnalysis'
import baseAnalysis from './baseAnalysis'
import equipMentTotalList from './equipMentTotalList'
import onlinePercent from './onlinePercent'
import chartBox from '../chartBox'
import workOrder from './workOrder'
import failRateRank from '../alarmOverview/failRateRank'
import { fetchDeviceTypeList } from '../../../../api/system/dic/dic'
export default {
  components: {
    ratioAnalysis,
    baseAnalysis,
    equipMentTotalList,
    chartBox,
    onlinePercent,
    workOrder,
    failRateRank
  },
  props: ['keyModel', 'sysType', 'alarmOverviewData', 'selectedAreaCode'],
  data() {
    return {
      equipMentTotalListValue: {
        showDialog: false,
        isClear: true,
        deviceStatus: null,
        stateFilterShow: false
      },
      equipMentTotalListDialogWidth: '142.22vh',
      title: '',
      onlinePercentDialogShow: false,
      deviceNum: 34,
    }
  },
  mounted() {
    console.log('equipMentObject', this.keyModel)
    /* this.getDeviceNum() */
  },
  methods: {
    // 总数
    showEquipMentTotalList() {
      this.$emit('changeOpacityOne')
      this.title = '设备总量列表'
      this.equipMentTotalListValue.stateFilterShow = true
      this.initList(null)
      this.equipMentTotalListValue.deviceStatus = null
      this.showList()
    },
    // 在线
    showOnlineList() {
      this.$emit('changeOpacityOne')
      this.title = '在线设备列表'
      this.equipMentTotalListValue.stateFilterShow = false
      this.initList(0)
      this.equipMentTotalListValue.deviceStatus = 0
      this.showList()
    },
    // 离线
    outOfContact() {
      this.$emit('changeOpacityOne')
      this.title = '离线设备列表'
      this.equipMentTotalListValue.stateFilterShow = false
      this.initList(1)
      this.equipMentTotalListValue.deviceStatus = 1
      this.showList()
    },
    giveAnAlarm() {
      this.$emit('changeOpacityOne')
      this.title = '设备告警列表'
      this.equipMentTotalListValue.stateFilterShow = false
      this.initList(2)
      this.equipMentTotalListValue.deviceStatus = 2
      this.showList()
    },
    getPercent() {
      let num = this.keyModel.deviceOnlineNum / this.keyModel.deviceTotalNum * 100
      num < 100 && (num = num.toFixed(2))
      return num || '0.00'
    },
    initList(value) {
      if (this.$refs.equipMentTotalList) {
        if (!this.equipMentTotalListValue.isClear && this.equipMentTotalListValue.deviceStatus !== value) {
          this.equipMentTotalListValue.isClear = true
          setTimeout(() => {
            this.$refs.dialog.$el.firstElementChild.lastElementChild.classList.remove('dialogBodyNone')
            this.$refs.dialog.$el.firstChild.style.left = 0
            this.equipMentTotalListDialogWidth = '142.22vh'
            this.$refs.equipMentTotalList.tableHeight = 570
            this.$refs.dialog.$el.firstChild.style.opacity = 1
          })
        }
      }
    },
    //获取设备种类
    /* getDeviceNum() {
      fetchDeviceTypeList().then((res) => {
        if (res.data) {
          this.deviceNum = res.data.data.length;
        }
      })
    }, */
    showList() {
      this.equipMentTotalListValue.showDialog = true
      // 不需要清空，说明没有关闭弹窗，只是点击了定位地图，现在要重新打开，不要重置数据
      if (!this.equipMentTotalListValue.isClear) {
        this.fangdaDialog()
      } else {
        setTimeout(() => {
          if (this.$refs.equipMentTotalList) {
            this.$refs.equipMentTotalList.tableHeight = 570
            this.$refs.dialog.$el.firstChild.style.left = 0
            this.$refs.dialog.$el.firstChild.style.top = 0
          }
        })
      }
    },
    // 缩小dialog
    smallDialog() {
      // 缩小弹窗，不需要重置数据
      this.equipMentTotalListValue.isClear = false
      let width = 142.22 // 宽度固定是80vw
      let left = 0 // 固定为0
      let height = 570 // 报表的高度设置为500
      let timer = null
      timer = setInterval(() => {
        width -= 17.77778 // 宽度每次减10
        left += 10.5 // 距离左侧的距离每次移动5.9vw
        height -= 71.25 // 报表的高度每次缩短 6.2vh
        if (width < 5) { // 宽度小于5，停止定时器
          width = 5
          clearInterval(timer)
          // 关闭窗口
          this.equipMentTotalListValue.showDialog = false
        }
        if (width < 50) { // 宽度大概在50vw左右的时候，关闭分页器
          this.$refs.equipMentTotalList.tableConfig.showPagination = false
        }
        if (width < 10) { // 宽度小于10的时候，dialog的body隐藏
          this.$refs.dialog.$el.firstElementChild.lastElementChild.className += ' dialogBodyNone'
        }
        this.equipMentTotalListDialogWidth = width + 'vh'
        // this.$refs.equipMentTotalList.tableConfig.tableHeight = height;
        this.$refs.equipMentTotalList.tableHeight = height
        this.$refs.dialog.$el.firstChild.style.left = -left + 'vh'
        this.$refs.dialog.$el.firstChild.style.opacity = width / 100
      }, 10)
    },
    // 放大
    fangdaDialog() {
      // 移除隐藏dialog的body的class
      this.$refs.dialog.$el.firstElementChild.lastElementChild.classList.remove('dialogBodyNone')
      // 获取dialog的宽度，距离左侧的距离，报表当前的高度，dialog当前的opacity
      let width = parseInt(this.$refs.dialog.$el.firstChild.style.width)
      let left = parseInt(this.$refs.dialog.$el.firstChild.style.left)
      let height = parseInt(this.$refs.equipMentTotalList.tableHeight)
      let opacity = parseInt(this.$refs.dialog.$el.firstChild.style.opacity)
      let timer = null
      timer = setInterval(() => {
        width += 17.77778 // 宽度每次放大10vw
        left += 10.5 // 左侧距离每次移动6vw
        height += 71.25 // 报表高度每次增长6.2vh
        opacity += 0.2 // 透明度每次增加0.2
        if (width >= 142.22) {
          // 当宽度大于80的时候，置为默认的参数
          this.$refs.dialog.$el.firstChild.style.left = 0
          this.equipMentTotalListDialogWidth = '142.22vh'
          this.$refs.equipMentTotalList.tableHeight = 570
          this.$refs.dialog.$el.firstChild.style.opacity = 1
          clearInterval(timer)
          return
        }
        if (width > 88.889) {
          // 宽度大于50vw的时候，开启分页器
          this.$refs.equipMentTotalList.tableConfig.showPagination = true
        }
        this.equipMentTotalListDialogWidth = width + 'vh'
        this.$refs.dialog.$el.firstChild.style.left = left + 'vh'
        this.$refs.equipMentTotalList.tableHeight = height + 'vh'
        this.$refs.dialog.$el.firstChild.style.opacity = opacity
      }, 10)
    },
    handleClose(done) {
      this.$emit('changeOpacityOnes')
      this.equipMentTotalListValue.isClear = true
      this.$refs.equipMentTotalList.searchClear = false
      done()
    },
    positionMap(rowData) {
      this.smallDialog()
      // 显示设备
      this.$EventBus.$emit('showDevice', rowData)
      // 选择某一个设备
      this.$EventBus.$emit('deviceData', rowData)
    },
    resetChoose(rowData) {
      this.$EventBus.$emit('resetChoose', rowData)
    }
  }
}
</script>

<style scoped>
.mainSx {
  width: 0.266666vh;
  height: 2vh;
  float: left;
  margin-right: 0.5vh;
  margin-left: 0.888889vh;
}

.equipMentTotalTitleDiv {
  position: relative;
  line-height: 24px;
  font-size: 18px;
  color: #EFF0F2 !important;
}

.narrowDialog {
  position: absolute;
  top: 0;
  right: 20px;
  cursor: pointer;
  line-height: 32px;
}

.fangdaDialogDiv {
  position: absolute;
  right: 1.777778vh;
  top: 3vh;
  font-size: 18px;
  cursor: pointer;
  z-index: 11;
}

.equipMentDiaLogDiv .el-dialog__wrapper .el-dialog .el-dialog__body {
  cursor: default;
}

.dialogBodyNone {
  display: none;
}
</style>
<style scoped>
.sbglDiv1 {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  width: 100%;
  flex-wrap: wrap;
  padding: 2% 1%;
}

.sbglDiv {
  width: 44%;
  border-radius: 5px;
  text-align: center;
  margin-left: -3%;
  cursor: pointer;
  height: 38%;
  background: url(~@/assets/img/tip-bg.png) no-repeat;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sbglBox {
  width: 90%;
  height: 90%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.left-sbgl {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 55%;
  height: 100%;
}

.sbgl-text {
  font-size: 1.067vh;
  color: rgba(233, 248, 255, 0.6);
  line-height: 5vh;
  margin-left: 0.5vh;
  width: 4vh;
  text-align: left;
}

.right-sbgl {
  width: 45%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sbgl-number {
  width: 100%;
  height: 2vh;
  text-align: right;
  font-size: 2vh;
  color: #FACA52;
  line-height: 2vh;
}

.sbglFont-text {
  color: #ffffff;
  /* font-weight: bolder; */
  text-align: center;
  font-size: 1.422vh;
  margin-top: 0.8vh;
}

.sbglFont-number {
  color: #01ffff;
  /* font-weight: bolder; */
  text-align: center;
  font-size: 2.66667vh;
  margin: 0.8vh 0;
}

.sbgl-icon {
  width: 50%;
  height: 70%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon {
  width: 80%;
  height: 100%;
  background-size: 100% 100%;
  margin: 0 auto;
}

.icon-total {
  background-image: url('/static/img/temporary.png')
}

.icon-online {
  background-image: url('/static/img/temporary.png')
}

.icon-off-contact {
  background-image: url('/static/img/temporary.png')
}

.icon-online-percent {
  background-image: url('/static/img/temporary.png')
}
</style>
