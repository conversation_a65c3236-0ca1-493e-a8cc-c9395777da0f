<template>
  <div v-if="route!==null">
    <!-- <el-menu-item v-if="!children" :disabled="true" :index="route.path" class="headMenu" :class="{'submenu-title-noDropdown':!isNest}">
       <span v-if="route.meta&&route.meta.title">{{route.meta.title}}</span>
    </el-menu-item> -->
    <div class="menu-wrapper">
      <template v-for="item in routes.filter(ele => !ele.hidden)">
        <router-link v-if="!item.children" :key="item.name" :to="route.path+'/'+item.path">
          <el-menu-item :index="route.path+'/'+item.path" :class="{'submenu-title-noDropdown':!isNest}">
            <svg-icon v-if="item.meta&&item.meta.icon&&item.meta.icon.indexOf('el-') === -1" :icon-class="item.meta.icon" />
            <i v-else-if="item.meta&&item.meta.icon&&item.meta.icon.indexOf('el-') !== -1" :class="item.meta.icon" />
            <span v-if="item.meta&&item.meta.title">{{ item.meta.title }}</span>
          </el-menu-item>
        </router-link>

        <el-submenu v-else :key="item.name+'1'" :index="item.path||item.name">
          <template slot="title">
            <svg-icon v-if="item.meta&&item.meta.icon&&item.meta.icon.indexOf('el-') === -1" :icon-class="item.meta.icon" />
            <i v-else-if="item.meta&&item.meta.icon&&item.meta.icon.indexOf('el-') !== -1" :class="item.meta.icon" />
            <span v-if="item.meta&&item.meta.title">{{ item.meta.title }}</span>
          </template>

          <template v-for="child in item.children.filter(ele => ele && !ele.hidden)">
            <el-submenu v-if="child.children&&child.children.length>0" :key="child.name+'1'" :index="child.name||child.path">
              <template slot="title">
                <svg-icon v-if="child.meta&&child.meta.icon&&child.meta.icon.indexOf('el-') === -1" :icon-class="child.meta.icon" />
                <i v-else-if="child.meta&&child.meta.icon&&child.meta.icon.indexOf('el-') !== -1" :class="child.meta.icon" />
                <span v-if="child.meta&&child.meta.title">{{ child.meta.title }}</span>
              </template>
              <!--多级嵌套路由-->
              <sidebar-item-for-choose :key="child.name" :is-nest="true" class="nest-menu" :route="child" />
            </el-submenu>
            <router-link v-else :key="child.name" :to="item.path+'/'+child.path">
              <el-menu-item :index="item.path+'/'+child.path.split('?')[0]">
                <svg-icon v-if="child.meta&&child.meta.icon&&child.meta.icon.indexOf('el-') === -1" :icon-class="child.meta.icon" />
                <i v-else-if="child.meta&&child.meta.icon&&child.meta.icon.indexOf('el-') !== -1" :class="child.meta.icon" />
                <span v-if="child.meta&&child.meta.title">{{ child.meta.title }}</span>
              </el-menu-item>
            </router-link>
          </template>
        </el-submenu>
      </template>
    </div>
  </div>
</template>
<style rel="stylesheet/scss" lang="scss" scoped>
.nini{background: #021b5f; border-bottom: 1px solid #2399f1; height:50px;}
.nini ul li a:hover :active{ background:#03175c}
</style>
<script>
export default {
  name: 'SidebarItemForChoose',
  props: {
    children: {
      type: String
    },
    route: {
      type: Object
    },
    isNest: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    routes: function() {
      return this.route.children || []
    }
  },
  methods: {
    hasOneShowingChildren(children) {
      const showingChildren = children.filter(item => {
        return !item.hidden
      })
      if (showingChildren.length === 1) {
        return true
      }
      return false
    }
  }
}
</script>

<style scoped>
.svg-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

.svg-external-icon {
  background-color: currentColor;
  mask-size: cover!important;
  display: inline-block;
}
</style>
