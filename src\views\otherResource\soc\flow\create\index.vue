<!--
 * @Descripttion: 
 * @Author: wangl
 * @version: 
 * @Date: 2025-03-04 09:08:12
 * @LastEditors: wangl
 * @LastEditTime: 2025-03-25 09:03:37
-->
<template>
  <div class="app-container">
    <search v-model="query" @search="get" />
    <el-table v-loading="waiting" :data="dataList" v-bind="tableConfig" v-on="tableEventConfig">
      <el-table-column width="100" type="index" label="序号" v-bind="columnConifg" />
      <el-table-column min-width="18.52vh" prop="name" label="流程名称" v-bind="columnConifg" />
      <el-table-column min-width="18.52vh" prop="actExtTypeName" label="流程类型" v-bind="columnConifg" />
      <el-table-column width="240" prop="description" label="说明" v-bind="columnConifg" />
      <el-table-column v-bind="columnFixConifg" :width="tableButtonWidth">
        <template slot-scope="scope">
          <div v-if="hasAnyActionPermission(['324231'])">
            <template v-for="item in tableButtonList">
              <el-button 
                v-if="item.visible(scope) && checkButtonPermission('324231')" 
                :key="item.name + random.getUuid()" 
                type="text"
                @click="item.click(scope)"
              >{{ item.name }}</el-button>
            </template>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <app-pager />
  </div>
</template>
<script>
import { listPage } from '@/views/otherResource/soc/mixins'
import { flowListPage } from '@/views/otherResource/soc/flow/mixins/'
import search from './search'
import permission from '@/views/otherResource/soc/mixins/permission'

export default {
  components: { search },
  mixins: [listPage, flowListPage, permission],
  data() {
    return {
      query: {
        isDeploy: '1',
        modelType: '0'
      }
    }
  },
  computed: {
    tableButtonList() {
      return [
        {
          name: '发起工单',
          click: (scope) => this.openCreateFlow({ modelKey: scope.row.modelKey, extendCreateUrl: scope.row.extendCreateUrl, isOpenWindow: false }),
          visible: (scope) => true
        }
      ]
    }
  },
  created() {
    this.get()
  },
  methods: {
    get() {
      this.getBase({
        url: '/flowService/act-de-model/getList',
        params: this.query
      })
    }
  }
}
</script>
