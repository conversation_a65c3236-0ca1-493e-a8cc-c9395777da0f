<template>
  <div class="app-container">
    <!-- S 头部查询 -->
    <TopSearch v-if="!customeFormShow" :comput-height="computHeight" :form="form" @toggle-custom-form="toggleCustomForm"
      @query="queryData(tabActiveName)" @reset="reset" />
    <!-- E 头部查询 -->

    <!-- S 自定义查询 -->
    <div v-else class="custom border">
      <div class="header">
        自定义查询
        <el-button class="btn-close" icon="el-icon-close" circle @click="toggleCustomForm" />
      </div>
      <el-scrollbar class="scrollbar">
        <div class="custom-search">
          <el-form ref="customForm" size="medium" :model="customForm" label-width="10.19vh">
            <el-row style="overflow: hidden;">
              <el-row>
                <el-row>
                  <el-col style="width: 27.78vh;">
                    <el-form-item label="资源类型" prop="assets">
                      <el-select v-model="customForm.typeCode" placeholder="请选择" @change="resourceCiTypeCodeChange">
                        <el-option v-for="item in resCiTypeList" :key="item.typeCode" :label="item.cname"
                          :value="item.typeCode" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col style="width: 27.78vh;">
                    <el-form-item label="资源名称" prop="deviceName">
                      <el-input v-model.trim="customForm.deviceName" placeholder="请输入资源名称" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col style="width: 27.78vh;">
                    <el-form-item label="唯一编码" prop="deviceSerno">
                      <el-input v-model.trim="customForm.deviceSerno" placeholder="请输入唯一编码" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col style="width: 27.78vh;">
                    <el-form-item label="行政区" prop="zoneCode">
                      <sys-dict-area-select v-model="customForm.zoneCode" />
                    </el-form-item>
                  </el-col>
                  <el-col style="width: 27.78vh;">
                    <el-form-item label="所属道路" prop="areaCode">
                      <sys-dict-area-road-select v-model="customForm.areaCode" />
                    </el-form-item>
                  </el-col>
                  <el-col style="width: 27.78vh;">
                    <el-form-item v-if="customForm.sysType !== '3'" label="所属路口" prop="areaCode">
                      <sysDictIntersetions v-model="customForm.intersectionCode" />
                    </el-form-item>
                  </el-col>

                  <el-col style="width: 27.78vh;">
                    <el-form-item label="运维团队" prop="teamId">
                      <sys-team-select v-model="customForm.teamId" />
                    </el-form-item>
                  </el-col>
                  <!--                 <el-col style="width: 300px;">
                    <el-form-item label="标签" prop="tagId">
                      <tag-select v-model="customForm.tagId" />
                    </el-form-item>
                  </el-col> -->
                  <el-col style="width: 27.78vh;">
                    <el-form-item label="设备状态" prop="deviceUseState">
                      <el-select v-model="customForm.deviceUseState" placeholder="请选择设备状态" multiple collapse-tags>
                        <el-option label="使用中" value="1" />
                        <el-option label="已超期" value="2" />
                        <el-option label="维护中" value="3" />
                        <el-option label="已下架" value="4" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-collapse-transition>
                    <div v-show="infoMoreShow">
                      <el-col v-for="attr in isQueryResourceCiTypeAttr" :key="'attr' + attr.cname"
                        style="width: 27.78vh;">
                        <el-form-item :label="attr.cname">
                          <template v-if="attr.typedef == 'STR'">
                            <el-input :ref="attr.ename" v-model="customForm.param[attr.ename]" clearable
                              @blur="validate(attr.ename, attr.checkValueRegex)" />
                          </template>
                          <template v-else-if="attr.typedef == 'ENUMS'">
                            <el-select :ref="attr.ename" v-model="customForm.param[attr.ename]" placeholder="请选择"
                              filterable clearable>
                              <el-option v-for="option in attr.selectOption" :key="option.label" :label="option.label"
                                :value="option.value" />
                            </el-select>
                          </template>
                          <template v-else-if="attr.typedef == 'YYYYMMDD'">
                            <el-date-picker :ref="attr.ename" v-model="customForm.param[attr.ename]"
                              format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" type="date"
                              placeholder="选择日期" />
                          </template>
                          <template v-else>
                            <el-input :ref="attr.ename" v-model="customForm.param[attr.ename]" clearable
                              @blur="validate(attr.ename, attr.checkValueRegex)" />
                          </template>
                        </el-form-item>
                      </el-col>
                    </div>
                  </el-collapse-transition>
                  <el-col style="width: 27.78vh;">
                    <el-button type="primary" size="medium" icon="el-icon-search" style="margin-left: 4.63vh"
                      @click="customClick()">查询
                    </el-button>
                  </el-col>
                </el-row>
              </el-row>
            </el-row>
          </el-form>
          <img v-if="!infoMoreShow" :src="require('../assets/down.png')" class="moreImg" @click="infoMoreShow = true">
          <img v-else :src="require('../assets/up.png')" class="moreImg" @click="infoMoreShow = false">
        </div>
      </el-scrollbar>
    </div>
    <!-- E 自定义查询-->

    <div class="content-table">
      <!-- S 通用查询表格属性菜单 -->
      <template v-if="isCommonQuery">
        <!-- S 下面一级 tab 主题 菜单 -->
        <el-tabs v-model="firstTabActiveName" @tab-click="handleFirstTabClick">
          <el-tab-pane key="0" label="全部" name="0" />
          <el-tab-pane v-for="item in subjectList" :key="item.typeValue" :label="item.typeName"
            :name="item.typeValue" />
        </el-tabs>
        <!-- S 下面一级 tab 主题 菜单 -->
        <!-- S 下面二级tab菜单 -->
        <el-tabs v-model="tabActiveName" @tab-click="handleTabClick">
          <el-tab-pane v-for="item in resCiTypeList" :key="item.typeCode" :label="item.cname" :disabled="item.disabled"
            :name="item.typeCode" />
        </el-tabs>
        <!-- E 下面二级tab菜单 -->
      </template>
      <!-- E 通用查询表格属性菜单 -->

      <!-- S 自定义查询表格属性菜单 -->
      <template v-else>
        <el-tabs v-show="tabShow" v-model="tabActiveName" :class="{ noData: !tabLists.length }"
          @tab-click="handleTabClick">
          <el-tab-pane v-for="item in tabLists" :key="item.typeCode" :label="item.cname" :disabled="item.disabled"
            :name="item.typeCode" />
        </el-tabs>
      </template>
      <!-- E 自定义查询表格属性菜单 -->
      <div style="padding-bottom: 10px;">
        <div v-if="checkButtonPermission(322111)" style="margin-right: 2rem;display: inline-block">
          <el-button-group>
            <el-button type="primary" size="medium" icon="el-icon-plus" @click="handleSave">上架
            </el-button>
          </el-button-group>
        </div>
        <el-button-group>
          <el-button v-if="checkButtonPermission(322112)" type="primary" size="medium" icon="el-icon-upload" @click="openImport('importInstance')">资源导入
          </el-button>
          <el-button v-if="checkButtonPermission(322113)" type="primary" size="medium" icon="el-icon-download" @click="handleDownloadModelExcel">资源导入模板
          </el-button>
        </el-button-group>
        <import-instance-file v-if="v === 'importInstance'" :is-dialog="true"
          @saved="handleTabClick({ name: tabActiveName })" />
        <div style="margin-left: 1rem;display: inline-block" />
        <!-- <el-button-group>
          <el-button type="primary" size="medium" icon="el-icon-upload" @click="openImport('importInstanceRel')">关系导入
          </el-button>
          <el-button type="primary" size="medium" icon="el-icon-download" @click.stop="handleDownloadModelRelExcel">关系导入模板
          </el-button>
        </el-button-group> -->
        <update-import-file v-if="v === 'importInstanceRel'" upload-url="/cmdbService/confCiInstanceRel/importExcel"
          :is-dialog="true" />
        <div v-if="checkButtonPermission(322114)" style="margin-left: 2rem;display: inline-block">
          <el-button-group>
            <el-button type="primary" size="medium" icon="el-icon-download" @click.stop="handleExportExcelServer">资源导出
            </el-button>
          </el-button-group>
        </div>
        <!-- <div style="float:right;display: inline-block"> -->
        <!-- <el-button type="primary" size="medium" icon="el-icon-plus" style="margin-right: 5px;" @click="handleQRCodePage">打印二维码
          </el-button> -->
        <!-- <el-popover placement="bottom" width="100" trigger="click">
            <el-checkbox v-for="attr in instanceData" :key="'checkbox-' + attr.ename" :label="attr.cname"
              :checked="attr.isQuery === 1" @change="changeColumn(attr)">{{ attr.cname }}
            </el-checkbox>
            <el-button slot="reference" size="medium" type="primary" icon="el-icon-setting">自定义列管理
            </el-button>
          </el-popover>
        </div> -->
      </div>
      <el-table ref="table" v-loading="instanceTableLoading" size="medium"
        :header-cell-style="{ color: 'rgba(0, 0, 0, 0.4)', fontWeight: 'border', fontSize: '14px', backgroundColor: '#F5F7FA' }"
        :data="tableData" :max-height="$store.getters.tableMaxHeight - 150" border stripe min-width="100%"
        :cell-class-name="cellClassName" element-loading-text="数据加载中" @cell-dblclick="routeToDetail">
        <el-table-column type="index" label="序号" width="60px" header-align="center" align="center"
          show-overflow-tooltip />
        <!-- <el-table-column width="60px" label="拓扑" header-align="center" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <svg-icon v-if="scope.row.deviceUseState == 4" icon-class="topo"
              style="cursor:not-allowed;filter: grayscale(1);" />
            <svg-icon v-else icon-class="topo" @click.native.stop="systemInstanceSelect(scope)" />
          </template>
        </el-table-column> -->
        <el-table-column width="180px" label="资源名称" prop="deviceName" header-align="center" align="center"
          show-overflow-tooltip />
        <el-table-column
          v-if="tabActiveName && resCiTypeList.filter(p => p.typeCode === tabActiveName).length > 0 && resCiTypeList.filter(p => p.typeCode === tabActiveName)[0].isSource === '1'"
          width="180px" label="唯一编码" prop="deviceSerno" header-align="center" align="center" show-overflow-tooltip />
        <el-table-column v-else width="180px" label="资源编码" prop="instanceCode" header-align="center" align="center"
          show-overflow-tooltip />
        <el-table-column label="所属区域" prop="zoneName" header-align="center" align="center" show-overflow-tooltip />
        <el-table-column min-width="90px" label="设备状态" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.deviceUseState == 1" type="success">使用中</el-tag>
            <el-tag v-else-if="scope.row.deviceUseState == 2" type="warning">已超期</el-tag>
            <el-tag v-else-if="scope.row.deviceUseState == 3">维护中</el-tag>
            <el-tag v-else-if="scope.row.deviceUseState == 4" type="danger">已下架</el-tag>
            <el-tag v-else type="info">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column min-width="230px" label="运维团队" prop="teamName" header-align="center" align="center"
          show-overflow-tooltip />
        <div v-for="item in instanceData" :key="'column-' + item.ename">
          <el-table-column v-if="item.isQuery === 1" :label="item.cname" :prop="item.ename"
            :min-width="getWidth(item.cname)" show-overflow-tooltip header-align="center" align="center">
            <template slot-scope="scope">
              <div v-if="item.typedef == 'ENUMS'">
                {{ getDictItem(scope.row[item.ename], item.ename) }}
              </div>
              <div v-else-if="item.typedef == 'YYYYMMDD'">
                {{ dateFormatFunction(scope.row[item.ename]) }}
              </div>
              <div v-else>{{ scope.row[item.ename] }}</div>
            </template>
          </el-table-column>
        </div>
        <!-- 下架时间 -->
        <el-table-column label="下架时间" prop="scrapDate" header-align="center" align="center" show-overflow-tooltip />
        <!-- 下架原因 -->
        <el-table-column label="下架原因" header-align="center" align="center">
          <template slot-scope="scope">
            <span>{{ delReasonOptions[scope.row.delReason] }}</span>
          </template>
        </el-table-column>
        <!-- 操作 -->
        <el-table-column v-if="hasAnyActionPermission([322115, 322116, 322117])" label="操作" min-width="200px" fixed="right" header-align="center">
          <template slot-scope="scope">
            <el-button v-if="checkButtonPermission(322115)" type="text" :disabled="scope.row.deviceUseState == 4"
              @click="handleSave(scope.row)">编辑</el-button>
            <el-button v-if="checkButtonPermission(322116)" type="text" :disabled="scope.row.deviceUseState == 4"
              @click="downloadQrcode(scope.row)">二维码下载</el-button>
            <!-- <el-button type="text" @click="handleDel(scope.row)">删除</el-button> -->
            <el-button v-if="checkButtonPermission(322117)" type="text" :disabled="scope.row.deviceUseState == 4"
              @click="handleDown(scope.row)">下架</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :current-page.sync="pager.page" :page-size.sync="pager.size"
        :page-sizes="[10, 20, 30, 50, 100, 200]" layout="total, sizes, prev, pager, next, jumper" :total="totalPage" />
      <!-- <el-dialog v-dialogDrag title="拓扑展示" :visible.sync="topoSystemdialogVisible" width="95%" top="10vh">
        <instanceTopoL :id="instanceSn" text-color="#fff" :instance-sn="instanceSn" />
      </el-dialog> -->
    </div>
    <instance v-if="v === 'edit'"
      v-bind="{ id: Number(deviceId), typeCode: Number(tabActiveName), deviceType, sysType: firstTabActiveName }"
      :is-dialog="true" @saved="queryData(tabActiveName)" />
    <qrPage v-if="qrPageVisible" ref="qrPage" :check-type-code="tabActiveName" @handleQrPageClose="handleQrPageClose" />
    <el-dialog v-dialogDrag title="资源关系配置" :visible.sync="relativeVisible" width="30%">
      <el-table ref="multipleTable" :data="relativeTableData" tooltip-effect="dark" style="width: 100%"
        @selection-change="handleRelativeSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column width="150px" label="资源名称" prop="deviceName" />
        <el-table-column width="280px" label="所属区域" prop="areaName" />
        <el-table-column width="150px" label="运维团队" prop="teamName" />
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="relativeVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleModelFileDownload">确 定</el-button>
      </span>
    </el-dialog>
    <down-modal ref="downModal" @success="queryData(tabActiveName)" />
  </div>
</template>

<script>
import fileSaver from 'file-saver'
import instance from '@/views/otherResource/cmdb/resource/instance'
import { dictSubjectList, getDictSubjectRel } from '@/api/otherResource/cmdb/DictSubject'
import {
  findCommonQueryAndCustomizeQuery,
  getConfCiInstanceRelByInstanceIda
} from '@/api/otherResource/cmdb/confInstance'
import { queryAllCiTypeResource, queryAllCiTypeList } from '@/api/otherResource/cmdb/confCiType'
import {
  queryCustomAttriByParams,
  queryAttriFromRoute,
  getAllAttributeList
} from '@/api/otherResource/cmdb/confCiTypeAttr'
import { downloadReport } from '@/api/otherResource/cmdb/collection'
import { dateFormat } from '@/views/otherResource/cmdb/util/util'
import { json2excel } from '@/views/otherResource/cmdb/util/excel/setMethods.js'
import TopSearch from './components/topSearch.vue'
import downModal from './components/downModal.vue'
import qrPage from '@/views/otherResource/cmdb/resource/search/components/qrPage'
import importInstanceFile from '@/views/otherResource/cmdb/resource/search/components/import-instance-file/index'
import sysTeamSelect from '@/views/otherResource/soc/components/sys-team-select'
import { sysDictAreaSelect, sysDictAreaRoadSelect, sysDictIntersetions } from '@/views/otherResource/cmdb/admin/components'
import { sysDictSelect } from '@/views/otherResource/soc/components'
import instanceTopoL from '@/views/otherResource/cmdb/resource/components/instance-topo/index.vue'
import { getSysTypeList } from '@/api/main/admin'
import tagSelect from '@/components/tagSelect'
import download from '@/views/otherResource/soc/mixins/download'
import permission from '@/views/otherResource/soc/mixins/permission'

export default {
  components: {
    instanceTopoL,
    sysDictSelect,
    sysTeamSelect,
    sysDictAreaSelect,
    sysDictAreaRoadSelect,
    sysDictIntersetions,
    importInstanceFile,
    TopSearch,
    instance,
    tagSelect,
    qrPage,
    downModal
  },
  mixins: [download, permission],
  data() {
    return {
      v: '',
      selectNode: {},
      hoverNode: {},
      isShowHoverNode: false,
      isShowHoverEdge: false,
      edgeLabelList: [],
      hoverNodeStyle: {},
      hoverEdgeStyle: {},
      qrPageVisible: false,
      relativeVisible: false,
      relativeTableData: [],
      selectRelative: [],
      tabShow: false,
      deviceId: '',
      tableHeight: 594,
      contentHeight: '400px',
      computHeight: 'auto',
      commonQueryTypeCode: '',
      favourCheckColumn: '', // 自定义查询  字段选择
      form: {
        ip: '',
        teamId: '',
        tagId: undefined,
        zoneCode: '',
        areaCode: '',
        deviceName: '',
        deviceSerno: '',
        deviceUseState: ['1', '2', '3']
      },
      systemTopoList: [],
      infoMoreShow: false,
      firstTabActiveName: '0',
      tabActiveName: '3001',
      // deviceType: 1,
      customeFormShow: false,
      isUpdate: false,
      isCommonQuery: true,
      instanceTableLoading: false,
      topoSystemdialogVisible: false,
      relateTypeCode: '',
      pager: { page: 1, size: 10 },
      totalPage: 0,
      dictItemOption: {},
      dictItemGlobalMap: {},
      singleInstanceData: {},
      selectOption: {},
      tabLists: [],
      typeLists: [],
      typeArr: [],
      relateCiTypeOption: [],
      resourceCiTypeArr: [],
      resourceCiTypeAttr: [],
      isQueryResourceCiTypeAttr: [],
      useStatusSelectOption: [],
      instanceData: [],
      customForm: { // 自定义查询
        param: {},
        typeCode: '3001',
        deviceUseState: ['1', '2', '3'],
        deviceName: '',
        deviceSerno: '',
        zoneCode: '',
        areaCode: '',
        teamId: '',
        tagId: undefined
      },
      lastResourceCiTypeCode: '3001', // 自定义查询 上一次ciType类型
      tableData: [],
      timer: null,
      subjectList: [],
      resCiTypeList: [],
      resCiTypeListBak: [], // 被 主题 过滤之前的 类型列表
      modelTabSecondActive: '3001',
      // 资源资源 导入地址
      action: `/cmdbService/resCiInstance/importExcel`,
      // 关联关系 导入地址
      actionRel: `/cmdbService/confCiInstanceRel/importExcel`,
      importLoading: false,
      modelLocalCheckBoxList: [],
      modelAllCheckBoxList: [],
      modelCheckList: [],
      type: 0,
      resource: [[], []],
      topoCuttent: {},
      instanceSn: null,
      delReasonOptions: {
        1: '设备报废',
        2: '维保到期',
        3: '资产盘亏',
        4: '其他'
      }
    }
  },
  computed: {
    deviceType() {
      return this.resCiTypeListBak.find(i => i.typeCode === this.tabActiveName)?.id || 1
    }
  },
  watch: {
    'pager.page'() {
      this.queryData(this.isCommonQuery ? this.tabActiveName : '')
    },
    'pager.size'() {
      this.queryData(this.isCommonQuery ? this.tabActiveName : '')
    },
    // tabActiveName(v, o) {
    //   this.deviceType = this.resCiTypeListBak.find(i => i.typeCode === v)?.id || 1
    // },
    infoMoreShow: {
      immediate: true,
      handler(val) {
        if (!val) {
          this.handleTableLayout(400, this.tabLists.length ? 490 : 420)
        } else {
          this.handleTableLayout(500, 500)
        }
      }
    },
    topoSystemdialogVisible(val) {
      if (!val) {
        this.instanceSn = null
      }
    }
  },
  created() {
    this.searchCiType()
  },
  mounted() {
    this.handleTabClick({ name: this.tabActiveName })
  },
  methods: {
    searchCiType() {
      queryAllCiTypeResource().then(res => {
        if (res.data.data != null && res.data.data.length > 0) {
          this.resourceCiTypeArr = res.data.data
        } else {
          this.instanceData = []
        }
      })
      queryAllCiTypeList().then(res => {
        this.resCiTypeList = res.data.data
        this.resCiTypeListBak = res.data.data
        getAllAttributeList().then(res => {
          this.modelAllCheckBoxList = res.data.data
          res.data.data.forEach(ele => {
            if (ele.typeCode === '3001') {
              this.modelLocalCheckBoxList.push(ele)
            }
            if (ele.isNeed === 1) {
              this.modelCheckList.push(ele.typeAttrId)
            }
          })
        })
      })
      getSysTypeList({}).then(res => {
        this.subjectList = res.data.data
      })
    },
    // 点击主题tab
    handleFirstTabClick({ name }) {
      if (name === '0') {
        this.resCiTypeList = this.resCiTypeListBak
      } else {
        getDictSubjectRel(name).then(res => {
          this.resCiTypeList = []
          res.data.data.forEach(ele1 => {
            this.resCiTypeListBak.forEach(ele2 => {
              if (ele1.id === ele2.id) {
                this.resCiTypeList.push(ele2)
              }
            })
          })
          console.log(this.resCiTypeList)
          this.tabActiveName = this.resCiTypeList.length > 0 ? this.resCiTypeList[0].typeCode : ''
          this.handleTabClick({ name: this.tabActiveName })
        })
      }
    },
    // 点击tab切换
    handleTabClick({ name }) {
      this.tabActiveName = name || ''
      this.isQueryResourceCiTypeAttr = []
      queryCustomAttriByParams({ deviceType: this.deviceType, isQuery: 1 }).then(res => {
        if (res.data.data != null && res.data.data.length > 0) {
          this.isQueryResourceCiTypeAttr = res.data.data[3].newConfCiTypeAttriList
          this.instanceData = res.data.data[3].newConfCiTypeAttriList
          this.queryData(this.tabActiveName)
        } else {
          this.resourceCiTypeAttr = []
          this.instanceData = []
        }
      })
    },
    resourceCiTypeCodeChange(item) {
      this.tabActiveName = item
      this.lastResourceCiTypeCode = item
      this.isQueryResourceCiTypeAttr = []
      queryCustomAttriByParams({ deviceType: this.deviceType, isQuery: 1 }).then(res => {
        if (res.data.data != null && res.data.data.length > 0) {
          this.isQueryResourceCiTypeAttr = res.data.data[3].newConfCiTypeAttriList
          this.instanceData = res.data.data[3].newConfCiTypeAttriList
          this.queryData(item)
          this.customForm.param = {}
        } else {
          this.resourceCiTypeAttr = []
          this.instanceData = []
        }
      })
    },
    customClick() {
      if (this.lastResourceCiTypeCode === this.customForm.typeCode) {
        this.queryData()
      } else {
        this.lastResourceCiTypeCode = this.customForm.typeCode
        this.isQueryResourceCiTypeAttr = []
        queryCustomAttriByParams({ typeCode: this.customForm.typeCode, isQuery: 1 }).then(res => {
          if (res.data.data != null && res.data.data.length > 0) {
            this.isQueryResourceCiTypeAttr = res.data.data[3].newConfCiTypeAttriList
            this.instanceData = res.data.data[3].newConfCiTypeAttriList
            this.queryData()
            this.customForm.param = {}
          } else {
            this.resourceCiTypeAttr = []
            this.instanceData = []
          }
        })
      }
    },
    customQuery() {
      const params = {
        ...this.pager,
        params: this.customForm
      }
      findCommonQueryAndCustomizeQuery(params).then(res => {
        this.tableData = []
        res.data.data.records.forEach(ele => {
          if (ele.info !== null) {
            const temp = Object.assign(ele, JSON.parse(ele.info))
            this.tableData.push(temp)
          } else {
            this.tableData.push(ele)
          }
        })
        this.totalPage = res.data.data.total
        if (this.favourCheckColumn !== '') {
          this.instanceData.forEach(ele => {
            if (JSON.parse(this.favourCheckColumn).length > 0) {
              JSON.parse(this.favourCheckColumn).forEach(ele2 => {
                if (ele.typeAttrId === ele2) {
                  ele.isQuery = 0
                }
              })
            } else {
              ele.isQuery = 0
            }
          })
        }
        this.instanceTableLoading = false
        this.$refs.table.doLayout()
      })
    },

    // 点击查询数据
    queryData(val = '') {
      this.instanceTableLoading = true
      this.tableData = []
      if (!this.isCommonQuery) {
        this.customQuery()
      } else {
        this.tabActiveName = val
        this.typeCode = val

        this.generalQuery()
      }
      this.$refs.table.doLayout()
    },
    generalQuery() {
      this.toggleFirstDisabled(true)
      const params = {
        ...this.pager,
        params: {
          param: {},
          teamId: this.form.teamId || this.customForm.teamId,
          tagId: this.form.tagId || this.customForm.tagId,
          zoneCode: this.form.zoneCode || this.customForm.zoneCode,
          areaCode: this.form.areaCode || this.customForm.areaCode,
          deviceName: this.form.deviceName || this.customForm.deviceName,
          deviceSerno: this.form.deviceSerno || this.customForm.deviceSerno,
          typeCode: this.typeCode,
          deviceUseState: this.form.deviceUseState,
          sysType: this.firstTabActiveName != '0' ? this.firstTabActiveName : ''
        }
      }
      findCommonQueryAndCustomizeQuery(params).then(res => {
        this.tableData = []
        res.data.data.records.forEach(ele => {
          if (ele.info !== null) {
            const temp = Object.assign(ele, JSON.parse(ele.info))
            this.tableData.push(temp)
          } else {
            this.tableData.push(ele)
          }
        })
        this.totalPage = res.data.data.total
        this.instanceTableLoading = false
        this.$refs.table.doLayout()
      })
    },

    changeColumn(attr) {
      if (attr.isQuery === 1) {
        this.$set(attr, 'isQuery', 0)
      } else {
        this.$set(attr, 'isQuery', 1)
      }
      this.$nextTick(() => {
        this.$refs.table.doLayout()
      })
    },
    // 打开topo图
    systemInstanceSelect({ row }) {
      this.instanceSn = row.deviceSerno
      this.topoSystemdialogVisible = true
    },
    reset() {
      this.handleReset()
      this.queryData(this.tabActiveName)
    },
    handleReset() {
      this.form = {
        ip: '',
        teamId: '',
        tagId: undefined,
        zoneCode: '',
        deviceName: '',
        deviceSerno: '',
        deviceUseState: ['1', '2', '3']
      }
    },
    getWidth(str) {
      if (str) {
        let strLen = 0
        for (let i = 0; i < str.toString().length; i++) {
          if (str.toString().charCodeAt(i) > 255) {
            strLen += 2
          } else {
            strLen += 1
          }
        }
        return strLen < 3 ? '100px' : strLen * 8 + 100 + 'px'
      }
    },
    handleToggle(dir) {
      this.tabShow = false
      if (dir === 'down') {
        this.computHeight = '38px'
        this.isCommonQuery = false
        this.handleReset()
        this.tabActiveName = '3001'
        this.customForm.typeCode = '3001'
        this.tabLists = []
        this.customClick()
      } else if (dir === 'up') {
        this.infoMoreShow = false
        this.computHeight = 'auto'
        this.customeFormShow = false
        this.tabActiveName = this.customForm.typeCode || '3001'
        this.customForm.typeCode = '3001'
        this.customForm.ciInstanceUseStatus = ''
        this.customForm.deviceName = ''
        this.customForm.deviceSerno = ''
        this.customForm.zoneCode = ''
        this.customForm.teamId = ''
        this.customForm.tagId = undefined
        this.customForm.deviceUseState = ['1', '2', '3']
        this.isCommonQuery = true
        this.commonQueryTypeCode = '3001'
        this.queryData(this.tabActiveName)
      }
    },

    showLoading() {
      return this.$loading({
        target: document.getElementById('tooltip-div'),
        lock: true,
        text: '数据处理中…',
        spinner: 'el-icon-loading',
        background: 'hsla(0,0%,100%,.9)'
      })
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v =>
        filterVal.map(j => (j === 'OPEN_OR_CLOSE' ? (parseInt(v[j]) || v[j] ? '是' : '否') : v[j]))
      )
    },

    validate(ename, ruleRsg) {
      const value = this.$refs[ename][0].value
      if (ruleRsg && value && !eval(ruleRsg).test(value)) {
        this.$message({
          showClose: true,
          message: `请输入正确的${ename}`,
          type: 'warning'
        })
      }
    },
    dateFormatFunction(time) {
      return dateFormat(time)
    },
    getDictItem(value, ename) {
      const dictItemMap = {}
      if (
        !this.dictItemGlobalMap[this.customForm.typeCode] ||
        !this.dictItemGlobalMap[this.customForm.typeCode][ename]
      ) {
        if (this.dictItemOption[ename]) {
          this.dictItemOption[ename].forEach(dictItem => {
            dictItemMap[dictItem.value] = dictItem.label
          })

          if (!this.dictItemGlobalMap[this.customForm.typeCode]) {
            this.dictItemGlobalMap[this.customForm.typeCode] = {}
          }
          this.dictItemGlobalMap[this.customForm.typeCode][ename] = dictItemMap
          return this.dictItemGlobalMap[this.customForm.typeCode][ename][value]
        }

        return value
      } else {
        return this.dictItemGlobalMap[this.customForm.typeCode][ename][value]
      }
    },
    commonQueryGetDictItem(value, ename) {
      const commonQueryTypeCode = this.commonQueryTypeCode
      const dictItemMap = {}
      if (
        !this.dictItemGlobalMap[commonQueryTypeCode] ||
        !this.dictItemGlobalMap[this.commonQueryTypeCode][ename]
      ) {
        if (this.dictItemOption[ename]) {
          this.dictItemOption[ename].forEach(dictItem => {
            dictItemMap[dictItem.value] = dictItem.label
          })

          if (!this.dictItemGlobalMap[commonQueryTypeCode]) {
            this.dictItemGlobalMap[commonQueryTypeCode] = {}
          }
          this.dictItemGlobalMap[commonQueryTypeCode][ename] = dictItemMap
          return this.dictItemGlobalMap[commonQueryTypeCode][ename][value]
        }

        return value
      } else {
        return this.dictItemGlobalMap[commonQueryTypeCode][ename][value]
      }
    },
    // 跳转详情页面
    routeToDetail(row) {
      this.$router.push({
        path: '/resource/detail/index',
        query: {
          deviceId: row.deviceId,
          typeCode: row.typeCode,
          cname: row.deviceName
        }
      })
    },
    getCommonQueryCname() {
      const obj = this.tabLists.find(item => item.typeCode == this.tabActiveName)
      return obj.cname || ''
    },
    getCustomizeCname() {
      const { pathLabels = [''] } = this.$refs['editCascader'].getCheckedNodes()[0]
      return pathLabels[pathLabels.length - 1]
    },
    cellClassName({ columnIndex }) {
      if (columnIndex < 2) {
        return 'center'
      }
    },

    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(() => {
          done()
        })
        .catch(() => {
        })
    },

    // 属性菜单禁用启用
    toggleFirstDisabled(disabled = false) {
      this.typeArr.forEach(item => (item.disabled = disabled))
      this.tabLists.forEach(item => (item.disabled = disabled))
    },

    // 表格上面属性菜单查询
    getAttriByParams(typeCode, callback) {
      queryCustomAttriByParams({
        typeCode: typeCode,
        isQuery: 1
      }).then(res => {
        const { data = [], code = -1, msg = '' } = res.data
        if (code !== 0) return this.$message.error(msg || '请求出错,请稍后重试!')
        // 查询type的属性先将表格头进行渲染
        this.instanceData = data.length ? data : []

        this.resourceCiTypeAttr = data.length ? data : []
        if (typeof callback === 'function') callback()
      })
    },

    // 处理表格布局
    handleTableLayout(cHeight = 0, tHeight = 0) {
      this.$nextTick(() => {
        if (cHeight) {
          this.contentHeight = window.innerHeight - cHeight + 'px'
        }
        if (tHeight) {
          this.tableHeight = window.innerHeight - tHeight + 'px'
        }
        this.$refs.table.doLayout()
      })
    },
    changeCiTypeAttr() {
      const param = { typeCode: this.customForm.typeCode + '' }
      queryCustomAttriByParams({
        typeCode: param.typeCode,
        isQuery: 1
      }).then(res => {
        if (res.data.data != null && res.data.data.length > 0) {
          this.queryData()
        }
      })
      this.getAttriByParams(param.typeCode)
    },
    toggleCustomForm() {
      this.isCommonQuery = false
      this.customeFormShow = !this.customeFormShow
      this.handleToggle(this.customeFormShow ? 'down' : 'up')
    },
    handleModelSecondTabClick(ele) {
      queryAttriFromRoute({ 'typeCode': ele.name }).then(ele => {
        this.modelLocalCheckBoxList = ele.data.data
      })
    },
    openImport(v) {
      this.v = v
    },
    handleDownloadModelExcel() {
      const type = this.resCiTypeList.filter(p => this.tabActiveName === p.typeCode)[0]
      this.getData({
        url: '/cmdbService/resCiInstance/newExportImportExcel',
        params: { id: type.id },
        responseType: 'blob',
        success: (r) => {
          const blob = new Blob([r.data], {
            type: 'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          })
          fileSaver.saveAs(blob, type.cname + '导入模板.xlsx')
        },
        error: (r) => {
          if (r.data.msg) this.notify.warn(r.data.msg)
        }
      })
    },
    handleModelFileDownload() {
      const tableData = []
      const tHeader = []
      const filterVal = []
      this.modelAllCheckBoxList.forEach(ele => {
        if (this.modelCheckList.indexOf(ele.typeAttrId) > -1) {
          tHeader.push(ele.cname + '-' + ele.ename)
          filterVal.push(ele.ename)
        }
      })

      const title = '实例导入模板'
      const excelData = [
        {
          tHeader: tHeader, // sheet表一头部
          filterVal: filterVal, // 表一的数据字段
          tableDatas: tableData, // 表一的整体json数据
          sheetName: title + '-' + this.modelTabSecondActive // 表一的sheet名字
        }
      ]
      json2excel(excelData, title, true, 'xlsx')
      this.modelCheckList = []
    },
    // 导入 关联关系 模板下载
    handleDownloadModelRelExcel() {
      const a = document.createElement('a')
      const evt = document.createEvent('MouseEvents')
      a.download = '资源关系导入模板'
      a.href = '/static/cdn/资源关系导入模板.xlsx'
      evt.initEvent('click', true, true)
      a.dispatchEvent(evt)
      window.URL.revokeObjectURL(a.href)
    },
    // 上传成功 清除上传文件 重新获取数据
    onSuccess(response) {
      this.uploadLoading && this.uploadLoading.close()
      this.$refs.upload.clearFiles()
      if (response.code === 0) {
        this.$message.success(response.msg)
        this.queryData(this.tabActiveName)
      } else {
        this.$message.error(response.msg)
        this.downloadErrorReport(response.data.filename)
      }
      this.importLoading = false
    },
    // 上传之前
    beforeUpload() {
      this.uploadLoading = this.$loading({
        target: document.getElementById('tooltip-div'),
        lock: true,
        text: '正在导入中,请耐心等待3-5分钟…',
        spinner: 'el-icon-loading',
        background: 'hsla(0,0%,100%,.9)'
      })
      this.importLoading = true
    },
    // 上传失败
    onError() {
      this.uploadLoading && this.uploadLoading.close()
      this.$message.error('上传失败!')
      this.importLoading = false
    },
    // 下载错误报告
    async downloadErrorReport(filename) {
      const res = await downloadReport({ filename: filename })
      if (!res.data) {
        return this.$message.warnning('文件出错!')
      }
      this.createBlobFile(res.data, filename)
    },
    // 创建二进制文件
    createBlobFile(data, name, type = 'application/vnd.ms-excel') {
      const blob = new Blob([data], { type: type })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = name
      a.click()
      URL.revokeObjectURL(a.href) // 释放URL对象
    },
    handleExportExcel() {
      if (!this.tableData.length) {
        this.$alert('暂无可导出的数据', { type: 'warning' })
        return
      }

      const loading = this.showLoading()
      this.instanceTableLoading = true

      if (this.isCommonQuery) {
        const obj = Object.assign(
          { page: 1, size: 65000 },
          {
            params: {
              teamId: this.form.teamId || this.customForm.teamId,
              tagId: this.form.tagId || this.customForm.tagId,
              areaCode: this.form.areaCode || this.customForm.areaCode,
              zoneCode: this.form.zoneCode || this.customForm.zoneCode,
              deviceName: this.form.deviceName || this.customForm.deviceName,
              deviceSerno: this.form.deviceSerno || this.customForm.deviceSerno,
              typeCode: this.tabActiveName,
              deviceUseState: this.customForm.deviceUseState,
              sysType: this.firstTabActiveName != '0' ? this.firstTabActiveName : ''
            }
          }
        )

        this.exportExcel(obj, loading)
      } else {
        const obj = Object.assign(
          { page: 1, size: 65000 },
          {
            params: this.customForm
          }
        )
        this.exportExcel(obj, loading)
      }
    },
    // 导出
    exportExcel(obj, loading) {
      const tableData = []
      findCommonQueryAndCustomizeQuery(obj).then(res => {
        res.data.data.records.forEach(ele => {
          const temp = Object.assign(ele, JSON.parse(ele.info))
          tableData.push(temp)
        })

        const tHeader = ['资源名称', '唯一编码', '所属区域', '运维团队']
        const filterVal = ['deviceName', 'deviceSerno', 'zoneName', 'teamName']
        this.instanceData.forEach(item => {
          filterVal.push(item.ename)
          tHeader.push(item.cname)
        })

        const title = '实例导出报表'
        const excelData = [
          {
            tHeader: tHeader, // sheet表一头部
            filterVal: filterVal, // 表一的数据字段
            tableDatas: tableData, // 表一的整体json数据
            sheetName: title // 表一的sheet名字
          }
        ]

        json2excel(excelData, title, true, 'xlsx')
        loading.close()
        this.instanceTableLoading = false
      })
    },
    // 后端导出excel
    handleExportExcelServer() {
      // 处理loading
      const loading = this.showLoading()
      this.instanceTableLoading = true
      const data = this.isCommonQuery ? {
        page: 1,
        size: 65000,
        params: {
          teamId: this.form.teamId || this.customForm.teamId,
          tagId: this.form.tagId || this.customForm.tagId,
          areaCode: this.form.areaCode || this.customForm.areaCode,
          zoneCode: this.form.zoneCode || this.customForm.zoneCode,
          deviceName: this.form.deviceName || this.customForm.deviceName,
          deviceSerno: this.form.deviceSerno || this.customForm.deviceSerno,
          typeCode: this.tabActiveName,
          deviceUseState: this.form.deviceUseState,
          sysType: this.firstTabActiveName != '0' ? this.firstTabActiveName : ''
        }
      } : { page: 1, size: 65000, params: this.customForm }
      const url = '/cmdbService/resCiInstance/exportExcel'
      const title = `实例导出报表`

      this.download({
        url,
        data,
        method: 'post',
        fileName: `${title}.xlsx`
      }, 'application/vnd.ms-excel').then(res => {
        loading.close()
        this.instanceTableLoading = false
      }).catch(() => {
        loading.close()
        this.instanceTableLoading = false
      })
    },
    // 新增 数据
    handleSave(row) {
      console.log('row.deviceId', row.deviceId)
      if (row) {
        this.deviceId = row.deviceId
        this.v = 'edit'
      } else {
        this.deviceId = ''
        this.v = 'edit'
      }
    },
    handleDel(row) {
      this.delData({
        url: '/cmdbService/resCiInstance/' + row.deviceId,
        success: () => this.queryData(this.tabActiveName)
      })
    },
    // 下架
    async handleDown(row) {
      if (row.alarmState == 1) {
        await this.$confirm('此设备有告警信息未处理，是否下架？', '提示', {
          type: 'warning'
        })
      }
      if (row.onlineState == 1) {
        await this.$confirm('此设备当前状态为在线，是否下架？', '提示', {
          type: 'warning'
        })
      }
      this.$refs.downModal.show(row)
    },
    // 资源关系配置
    handleRelative(row) {
      this.deviceId = row.deviceId
      const obj = Object.assign(
        { page: 1, size: 65000 },
        {
          params: {
            typeCode: this.commonQueryTypeCode
          }
        }
      )
      const that = this
      findCommonQueryAndCustomizeQuery(obj).then(res => {
        res.data.data.list.forEach(ele => {
          const temp = Object.assign(ele, JSON.parse(ele.info))
          this.relativeTableData.push(temp)
        })
        this.relativeTableData = this.relativeTableData.filter(ele => ele.deviceId != that.deviceId)
        this.relativeVisible = true
      })
    },
    findSelectRelative() {

    },
    handleRelativeSelectionChange(val) {
      this.selectRelative = val
    },
    handleQRCodePage() {
      this.qrPageVisible = true
      this.$nextTick(() => {
        this.$refs.qrPage.init()
      })
    },
    handleQrPageClose() {
    },
    downloadQrcode(row) {
      this.getData({
        url: '/cmdbService/resCiInstance/instanceQrCodeGeneral',
        method: 'post',
        data: { id: row.deviceId },
        responseType: 'blob'
      }).then(res => {
        const aLink = document.createElement('a')
        const blob = new Blob([res.data], { type: 'application/msword' }) // 设置下载的格式为zip
        aLink.href = URL.createObjectURL(blob)
        aLink.setAttribute('download', 'download-doc') // 设置下载文件名称
        aLink.click()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .noData {
    .el-tabs__header {
      margin-bottom: 0 !important;
    }
  }

  /* Fix tab styling for asset list navigation */
  .el-tabs__header {
    margin: 0 0 20px 0 !important;
  }

  .el-tabs__nav {
    display: flex !important;
    flex-direction: row !important;
  }

  .el-tabs__item {
    text-align: center !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: relative !important;
    padding: 0 20px !important;
    height: 40px !important;
    line-height: 40px !important;
    flex-shrink: 0 !important;
  }

  .el-tabs__item.is-active {
    color: #409EFF !important;
    font-weight: bold !important;
  }

  .el-tabs__item.is-active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 10px);
    height: 2px;
    background-color: #409EFF;
    border-radius: 1px;
  }

  .el-tabs__nav-wrap::after {
    height: 1px !important;
    background-color: #e4e7ed !important;
  }

  .el-form-item {
    .el-form-item__label {
      padding-right: 5px !important;
      font-weight: 400;
    }
  }

  .el-checkbox-group {
    padding-left: 30px;

    .el-checkbox {
      width: 80px;
      margin-right: 20px;

      .el-checkbox__label {
        font-size: 12px;
      }
    }
  }

  .el-table {

    td,
    th {
      &.center {
        text-align: center;

        .svg-icon {
          width: 25px;
          height: 25px;
          cursor: pointer;
        }
      }
    }
  }
}

.app-container {
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 0;
  background-color: inherit !important;
  box-shadow: none !important;

  .title {
    position: relative;
    margin-bottom: 10px;
    margin-left: 12px;
    font-size: 12px;

    &::after {
      position: absolute;
      top: 7px;
      left: -10px;
      height: 14px;
      content: '';
      border-left: 4px solid #66b1ff;
    }
  }

  .custom {
    background-color: #fff;
    transition-duration: 1s;

    .header {
      position: relative;
      height: 45px;
      padding: 0 20px;
      line-height: 45px;
      background-color: #409eff;

      .btn-close {
        position: absolute;
        top: 50%;
        right: 20px;
        padding: 0;
        background: transparent;
        border: none;
        transform: translateY(-50%);
      }
    }

    .custom-search {
      min-width: 980px;
      padding: 10px 0 25px 5px;

      .moreImg {
        position: absolute;
        bottom: 5px;
        left: 50%;
        z-index: 1000;
        width: 12px;
        height: 12px;
        cursor: pointer;
        transform: translateX(-50%);
      }
    }
  }

  .content-table {
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 10px;
    margin-top: 10px;
    flex: 1;
    background-color: rgba(37, 46, 63, 0.6);
  }
}

.template {
  width: 280px;
  margin-bottom: 10px;
}

.attr {
  float: left;
  width: calc(100% - 570px);
  margin-left: 10px;
}

::v-deep {
  .el-scrollbar__wrap {
    overflow: hidden;
    height: 100%;
  }

  .el-checkbox__label {
    display: inline-block;
    padding-left: 10px;
    line-height: 19px;
    font-size: 14px;
  }
}

.hover-menu {
  /*这个样式不写，右键弹框会一直显示在画布的左下角*/
  position: fixed;
  background: rgba(3, 3, 3, 0.6);
  border-radius: 5px;
}

.hover-menu ul {
  padding-left: 0px;
  list-style: none;
}

.hover-menu ul li {
  padding: 5px 10px;
  color: #ffff;
  border-bottom: 1px solid #ffffff;
  font-size: 14px;
  cursor: pointer;
  list-style: none;
}

.hover-menu ul li:hover {
  color: #659bc5;
}

.hover-menu ul li:last-child {
  border-bottom: none;
  padding: 5px 10px 0 10px;
}
</style>
