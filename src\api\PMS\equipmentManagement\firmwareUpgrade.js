import request from '@/utils/request'

const UpgradeURL = '/szdlService/api/deviceUpdate/'

// 获取固件升级任务列表
export function listDeviceUpdateRequest(data) {
  return request({
    url: UpgradeURL + 'listDeviceUpdate',
    method: 'post',
    data
  })
}

// 添加固件升级任务
export function insertDeviceUpdateRequest(data) {
  return request({
    url: UpgradeURL + 'insertDeviceUpdate',
    method: 'post',
    data
  })
}

// 批量删除固件升级任务
export function batchDeleteRequest(data) {
  return request({
    url: UpgradeURL + 'batchDelete',
    method: 'post',
    data
  })
}

// 编辑固件升级任务
export function updateFormRequest(data) {
  return request({
    url: UpgradeURL + 'updateForm',
    method: 'post',
    data
  })
}

// 删除任务的单个设备
export function deleteDeviceRequest(data) {
  return request({
    url: UpgradeURL + 'deleteDevice',
    method: 'post',
    data
  })
}

// 批量删除任务设备
export function batchDeviceDeleteRequest(data) {
  return request({
    url: UpgradeURL + 'batchDeviceDelete',
    method: 'post',
    data
  })
}

// 获取某条固件升级任务基本信息
export function getDeviceUpdateRequest(data) {
  return request({
    url: UpgradeURL + 'getDeviceUpdate',
    method: 'post',
    data
  })
}

// 任务设备升级列表
export function listDeviceWaitingUpdatePreviewRequest(data) {
  return request({
    url: UpgradeURL + 'listDeviceWaitingUpdatePreview',
    method: 'post',
    data
  })
}

// 选择设备列表
export function listDeviceWaitingUpdateRequest(data) {
  return request({
    url: UpgradeURL + 'listDeviceWaitingUpdate',
    method: 'post',
    data
  })
}

// 某条固件升级任务记录下的升级设备升级状态情况
export function getDeviceUpdateResultRequest(data) {
  return request({
    url: UpgradeURL + 'getDeviceUpdateResult',
    method: 'post',
    data
  })
}

// 升级单个设备
export function upgradeSingleRequest(data) {
  return request({
    url: UpgradeURL + 'upgradeSingle',
    method: 'post',
    data
  })
}

// 一键重新升级
export function upgradeMultieRequest(data) {
  return request({
    url: UpgradeURL + 'upgradeMulti',
    method: 'post',
    data
  })
}
