import request from '@/utils/request'

export function countTodayAlarm(data) {
  return request({
    url: '/pmsdevice/api/homePage/countTodayAlarm',
    method: 'get',
    params: data
  })
}
// 设备告警15天
export function halfMonthAlarm(data) {
  return request({
    url: '/pmsService/api/spot/deviceDayList',
    method: 'post',
    params: data
  })
}

// 车辆进出15天
export function halfMonthVehicle(data) {
  return request({
    url: '/pmsService/api/spot/parkingDayList',
    method: 'post',
    params: data
  })
}
// 获取设备监控数据
export function countDeviceBySys(data) {
  return request({
    url: '/pmsdevice/api/homePage/countDeviceBySys',
    method: 'get',
    params: data
  })
}

export function fetchCommunityData() {
  return request({
    url: '/pmsService/api/homeCountService/get',
    method: 'post'
  })
}

export function getInfoByCommunityId(data) {
  return request({
    url: '/pmsService/api/managerAreaOa/getInfoByCommunityId',
    method: 'post',
    data
  })
}

// 待处理报修数 待处理投诉数 待处理任务数 待处理工单数
export function fetchOrderStatistics(data) {
  return request({
    url: '/pmsdevice/api/homePage/getStatic',
    method: 'post',
    data
  })
}

export function fetchRepairList(data) {
  return request({
    url: '/pmsdevice/api/homePage/listRepair',
    method: 'post',
    data
  })
}

export function fetchTaskList(data) {
  return request({
    url: '/pmsdevice/api/homePage/listTask',
    method: 'post',
    data
  })
}

export function fetchComplainList(data) {
  return request({
    url: '/pmsdevice/api/homePage/listComplain',
    method: 'post',
    data
  })
}

export function fetchChargeList(data) {
  return request({
    url: '/pmsdevice/api/homePage/listCharge',
    method: 'post',
    data
  })
}

// 超维·城市新基建管理平台
export function getDeviceAttribute(data) {
  return request({
    url: '/devService/api/deviceAttribute/getDeviceAttribute',
    method: 'post',
    data
  })
}

export function getLeftOne(data) {
  return request({
    url: '/spotService/api/basisSpot/getLeftOne',
    method: 'post',
    data
  })
}

export function getLeftTwo(data) {
  return request({
    url: '/spotService/api/basisSpot/getLeftTwo',
    method: 'post',
    data
  })
}

export function getDevicePoint(data) {
  return request({
    url: '/spotService/api/basisSpot/getDevicePoint',
    method: 'post',
    data
  })
}
// basisSpot设备基础大屏-获取设备点位信息(流式查询)
export function getDevicePoints(params) {
  return request({
    url: '/spotService/api/basisSpot/getFluxDevicePoint',
    method: 'get',
    params
    // responseType: 'stream' // 设置响应类型为stream
  })
}

export function getRightOne(data) {
  return request({
    url: '/spotService/api/basisSpot/getRightOne',
    method: 'post',
    data
  })
}

export function getOfflineDetailList(data) {
  return request({
    url: '/devService/api/deviceOfflineAlert/alertCountList',
    method: 'post',
    data
  })
}

export function getRightTwo(data) {
  return request({
    url: '/spotService/api/basisSpot/getRightTwo',
    method: 'post',
    data
  })
}

export function findLeftOneDetail(data) {
  return request({
    url: '/spotService/api/basisSpot/findLeftOneDetail',
    method: 'post',
    data
  })
}

export function getUserPoint(data) {
  return request({
    url: '/spotService/api/basisSpot/getUserPoint',
    method: 'post',
    data
  })
}

export function getDeviceTypeBySys(data) {
  return request({
    url: '/spotService/api/basisSpot/newGetDeviceTypeBySys',
    method: 'post',
    data
  })
}

export function saveUserPoint(data) {
  return request({
    url: '/spotService/api/basisSpot/saveUserPoint',
    method: 'post',
    data
  })
}

export function pollingList(data) {
  return request({
    url: '/spotService/api/basisSpot/pollingList',
    method: 'post',
    data
  })
}
// 智能电表抄表记录
export function readMeterList(data) {
  return request({
    url: '/devService/api/deviceAttribute/queryReadMeterRecord',
    method: 'post',
    data
  })
}
// 气象仪气象记录
export function getReadWeatherRecord(data) {
  return request({
    url: '/devService/api/deviceAttribute/queryReadWeatherRecord',
    method: 'post',
    data
  })
}
export function pollingDetailList(data) {
  return request({
    url: '/spotService/api/basisSpot/pollingDetailList',
    method: 'post',
    data
  })
}

export function orderAlarmList(data) {
  return request({
    url: '/spotService/api/basisSpot/orderAlarmList',
    method: 'post',
    data
  })
}

export function faultList(data) {
  return request({
    url: '/spotService/api/basisSpot/faultList',
    method: 'post',
    data
  })
}

export function alarmDetail(data) {
  return request({
    url: '/spotService/api/basisSpot/alarmDetail',
    method: 'post',
    data
  })
}

export function alarmCountList(data) {
  return request({
    url: '/spotService/api/basisSpot/alarmCountList',
    method: 'post',
    data
  })
}

export function fetchthisTime(data) {
  return request({
    url: '/spotService/api/basisSpot/thisTime',
    method: 'post',
    data
  })
}

export function updateOrder(data) {
  return request({
    url: '/spotService/api/basisSpot/update',
    method: 'post',
    data
  })
}

export function creatOrder(data) {
  return request({
    url: '/spotService/api/basisSpot/creatOrder',
    method: 'post',
    data
  })
}

export function plauStart() {
  return request({
    url: '/api/play/start/44010200492000000003/34020000001320000001',
    method: 'get'
  })
}

export function findList(data) {
  return request({
    url: '/spotService/api/configSpot/findList',
    method: 'post',
    data
  })
}

// 获取待办数量
export function getBasisSpotToDo() {
  return request({
    url: '/spotService/api/basisSpot/toDo',
    method: 'post'
  })
}

export function roadList(data) {
  return request({
    url: '/spotService/api/configSpot/roadList',
    method: 'post',
    data
  })
}

export function suppliersProportion(data) {
  return request({
    url: '/spotService/api/basisSpot/suppliersProportion',
    method: 'post',
    data
  })
}

export function useTimeProportion(data) {
  return request({
    url: '/spotService/api/basisSpot/useTimeProportion',
    method: 'post',
    data
  })
}

export function queryCollectionReport(data) {
  return request({
    url: '/devService/api/deviceAttribute/queryCollectionReport',
    method: 'post',
    data
  })
}

export function getSysTypeList(data) {
  return request({
    url: '/cmdbService/api/sysType/list',
    method: 'post',
    data
  })
}

// 获取电力地图
export function getPowerMap(data) {
  return request({
    url: '/spotService/api/basisSpot/getPowerMap',
    method: 'post',
    data
  })
}

// 获取环网下变压器与配电箱的连线
export function getTransformerLine(data) {
  return request({
    url: '/spotService/api/basisSpot/getTransformerLine',
    method: 'post',
    data
  })
}

// 获取环网下变压器与配电箱的连线
export function getRingNetWorMapById(data) {
  return request({
    url: '/spotService/api/basisSpot/getRingNetWorMapById',
    method: 'post',
    data
  })
}

// 获取环网图
export function getTransformerMap(data) {
  return request({
    url: '/spotService/api/basisSpot/getTransformerMap',
    method: 'post',
    data
  })
}

// 20230411 设备概览统计图表

// 设备概览-总体统计分析-区位分析
export function getAreaAnalyse(params) {
  return request({
    url: '/spotService/api/basisSpot/statistics/getAreaAnalyse',
    method: 'get',
    params
  })
}
// 设备概览-总体统计分析-历史在线率统计
export function onlineRateStatistics(params) {
  return request({
    url: '/spotService/api/basisSpot/statistics/onlineRateStatistics',
    method: 'get',
    params
  })
}
// 设备概览-总体统计分析
export function overallAnalyse(params) {
  return request({
    url: `/spotService/api/basisSpot/statistics/overallAnalyse`,
    method: 'get',
    params
  })
}

export function getDeviceTypeIconInfo() {
  return request({
    url: `/cmdbService/confCiType/getDeviceTypeIconInfo`,
    method: 'get'
  })
}
//2025 03 21 获取设备种类
export function getDeviceListLength() {
  return request({
    url: '/cmdbService/confCiType/innerGetList',
    method: 'get',
  })
}
// 2025 03 12 新增 工单概览 接口
// 工单等级分析
export function getOrderLevelAnalysis(data) {
  return request({
    url: '/spotService/api/basisSpot/getOrderLevelAnalysis',
    method: 'get',
    data
  })
}
// 工单状态分析
export function getOrderStatusAnalysis(data) {
  return request({
    url: '/spotService/api/basisSpot/getOrderStatusAnalysis',
    method: 'get',
    data
  })
}
//2025 03 20 新增 实时告警 接口
// 获取实时告警
export function getNewAlarmList(data) {
  return request({
    url: '/spotService/api/basisSpot/newAlarmDataList',
    method: 'post',
    data
  })
}
// 获取告警详情
export function getNewAlarmDetail(data) {
  return request({
    url: '/spotService/api/basisSpot/alarmDetailNew',
    method: 'post',
    data
  })
}