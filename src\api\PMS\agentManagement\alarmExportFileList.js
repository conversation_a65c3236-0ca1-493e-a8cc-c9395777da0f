/**
 * 2023-02-08
 * 增加离线历史告警和性能历史告警的异步导出功能
 */
import request from '@/utils/request'
import axios from 'axios'  
const CancelToken = axios.CancelToken   

export function fetchList(data) {
  return request({
    url: '/devService/api/alertHistoryExport/list',
    method: 'post',
    data
  })
}

// 性能历史告警导出方式
export function getAlertRecordHistoryType(data) {
  return request({
    url: '/devService/api/alertRecord/getAlertRecordHistoryType',
    method: 'post',
    data
  })
}

// 离线历史告警导出方式
export function getOfflineAlertHistoryType(data) {
  return request({
    url: '/devService/api/deviceOfflineAlert/getOfflineAlertHistoryType',
    method: 'post',
    data
  })
}

// 性能历史告警异步导出
export function asyncExportAlert(data, that) {
  return request({
    url: '/devService/api/alertRecord/exportExcelOffline/history',
    method: 'post',
    data,
    cancelToken: new CancelToken(function executor(c) {
      that.cancel = c
    })  
  })
}

// 离线历史告警异步导出
export function asyncExportOffline(data, that) {
  return request({
    url: '/devService/api/deviceOfflineAlert/exportExcelOffline/history',
    method: 'post',
    data,
    cancelToken: new CancelToken(function executor(c) {
      that.cancel = c
    })  
  })
}

// 导出下载
export function fileDownload(params) {
  return request({
    url: '/devService/api/alertHistoryExport/download',
    method: 'GET',
    params,
  })
}