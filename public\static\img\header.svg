<svg width="1921" height="82" viewBox="0 0 1921 82" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2673_3766)">
<rect x="0.240234" y="1" width="1920" height="81" fill="#081245" fill-opacity="0.77"/>
<rect x="0.240234" y="1" width="1920" height="81" fill="url(#paint0_linear_2673_3766)" fill-opacity="0.15"/>
<path d="M496.318 48L470.777 80H0.240234V0H556.24L518.912 48H496.318Z" fill="url(#paint1_linear_2673_3766)" fill-opacity="0.15"/>
<path d="M556.24 0L518.912 48H496.318L470.777 80H0.240234" stroke="url(#paint2_linear_2673_3766)"/>
<path d="M457.45 80.5L1920.24 80.5" stroke="url(#paint3_linear_2673_3766)" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M1617.22 8L1570.24 71H1909.78V9L1617.22 8Z" fill="url(#paint4_linear_2673_3766)" fill-opacity="0.1"/>
<path d="M1821.03 0L1814.05 9.5H1612.19L1564.75 72" stroke="url(#paint5_linear_2673_3766)" stroke-opacity="0.6" stroke-linecap="round"/>
<path d="M546.24 20H599.728L615.542 32H653.682L668.566 48" stroke="url(#paint6_linear_2673_3766)" stroke-opacity="0.15" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M615.752 37L599.008 25H545.054L529.24 47H609.24L615.752 37Z" fill="url(#paint7_linear_2673_3766)" fill-opacity="0.15"/>
<path d="M506.24 81L528.566 52H659.729L663.45 47H716.473L729.496 31H1440.19" stroke="url(#paint8_linear_2673_3766)" stroke-opacity="0.15"/>
<g filter="url(#filter0_f_2673_3766)">
<path d="M30.0078 81C30.0078 81 195.589 77 230.938 77C266.287 77 429.078 81 429.078 81H30.0078Z" fill="#01FFFF"/>
</g>
<g filter="url(#filter1_d_2673_3766)">
<path d="M76.4789 50.136V51.088H69.2549V53.104H79.6149V55.288H55.0309V53.104H65.4189V51.088H58.3629V50.136H54.8349L58.5869 45.992H55.2269V43.864H58.1109V34.96H55.7869V32.832H58.1109V30.704H61.8069V32.832H72.7829V30.704H76.4789V32.832H78.9149V34.96H76.4789V43.864H79.5589V45.992H76.1989L79.9509 50.136H76.4789ZM72.7829 34.96H61.8069V36.5H72.7829V34.96ZM72.7829 38.572H61.8069V40.224H72.7829V38.572ZM72.7829 42.296H61.8069V43.864H72.7829V42.296ZM71.7469 45.992H63.0389L60.4349 48.876H65.4189V46.916H69.2549V48.876H74.3509L71.7469 45.992ZM93.3069 55.316V44.48H96.8349V53.048H98.8789V43.108H93.5309V32.636H96.9469V40.84H98.8789V30.732H102.407V40.84H104.339V32.636H107.755V43.108H102.407V53.048H104.451V44.48H107.979V55.316H93.3069ZM89.9749 34.064L88.0989 39.972H92.1589V54.728H84.1789V45.068H82.8629L86.3909 34.064H83.5069V31.824H92.5509V34.064H89.9749ZM88.7989 42.212H87.5389V52.46H88.7989V42.212ZM136.259 41.12H130.631V33.308H125.339V38.264L123.491 41.12H119.151L121.559 37.48V31.068H134.411V38.852H137.295L136.259 41.12ZM115.175 36.92L111.647 30.788H115.903L119.431 36.92H115.175ZM132.311 55.316L128.139 51.844L124.303 55.316H119.291L125.507 49.66L120.243 45.32H125.367L127.943 47.448L131.107 44.564H119.711V42.324H136.007V44.732L130.575 49.632L137.435 55.316H132.311ZM114.363 55.54V40.868H111.815V38.628H118.143V51.06L121.027 49.604V52.152L114.363 55.54ZM155.635 34.932L154.403 37.228H150.511L154.011 30.76H157.903L156.867 32.692H165.435V34.932H155.635ZM164.875 55.316H151.939V44.48L150.903 44.76V42.268L151.939 41.988V38.068H155.299V41.148L156.755 40.756V35.884H160.115V39.888L164.931 38.656V50.808H161.039L160.115 48.848V52.068H156.755V43.248L155.299 43.64V53.076H165.967L164.875 55.316ZM145.191 35.716V39.412H150.427V55.316H145.975L144.911 53.076H147.067V41.652H145.191V51.004L143.707 55.316H140.151L141.831 50.388V35.716H140.599V33.476H144.043L143.203 30.76H147.095L147.935 33.476H150.931V35.716H145.191ZM161.571 42.016L160.115 42.38V48.54H161.571V42.016ZM177.559 33.308V31.068H193.043V33.308H177.559ZM190.271 50.724L189.459 48.96L188.367 50.724H176.943V48.484L181.815 40.336H176.719V38.096H193.967V40.336H185.875L181.031 48.456H189.235L186.351 42.268H190.299L194.191 50.724H190.271ZM172.099 37.116L168.851 30.76H173.079L176.327 37.116H172.099ZM193.519 55.316H177.419L174.283 52.964L172.827 55.316H168.571L171.819 50.08V40.448H169.131V38.208H175.599V50.5L179.043 53.076H194.611L193.519 55.316ZM208.527 55.316V40.756H207.043L210.263 30.76H214.071L212.727 34.932H215.919L215.135 30.76H218.831L219.587 34.932H222.331V37.172H219.279V40.98H222.051V43.248H219.279V47.028H222.051V49.296H219.279V53.076H222.583V55.316H208.527ZM202.423 38.432H206.791V40.672L202.311 46.692H207.715L206.623 48.96H197.887V46.692L202.339 40.672H197.971V38.432L203.655 30.76H208.107L202.423 38.432ZM197.607 55.54V53.104L207.323 50.892V53.328L197.607 55.54ZM215.555 37.172H212.251V40.98H215.555V37.172ZM215.555 43.248H212.251V47.028H215.555V43.248ZM215.555 49.296H212.251V53.076H215.555V49.296ZM248.987 42.492V47.616H231.739V48.904H249.883V55.316H227.987V42.492H226.587V37.9H236.835L236.415 36.92H233.895L232.579 34.232H231.179L229.835 36.92H225.775L228.883 30.732H232.915L232.243 32.076H238.767V34.232H236.415L237.451 36.36H238.459L241.287 30.732H245.319L244.647 32.076H250.835V34.232H248.623L249.939 36.92H246.103L244.787 34.232H243.583L242.239 36.92H240.587L241.035 37.9H250.835V42.492H248.987ZM247.083 40.056H230.339V41.54H247.083V40.056ZM245.235 43.668H231.739V45.488H245.235V43.668ZM246.131 51.032H231.739V53.188H246.131V51.032ZM263.491 55.344V53.076H269.903V50.08H264.471V47.812H269.903V45.292H264.303V31.068H279.199V45.292H273.627V47.812H278.919V50.08H273.627V53.076H279.955V55.344H263.491ZM254.755 54.952V52.656L257.471 51.872V43.136H255.091V40.896H257.471V33.56H254.811V31.32H263.631V33.56H261.167V40.896H263.323V43.136H261.167V50.808L263.799 50.052V52.348L254.755 54.952ZM275.643 33.336H273.627V36.976H275.643V33.336ZM269.903 33.336H267.887V36.976H269.903V33.336ZM275.643 39.244H273.627V43.024H275.643V39.244ZM269.903 39.244H267.887V43.024H269.903V39.244ZM303.587 43.276H299.527L303.307 35.044H307.367L303.587 43.276ZM297.679 47.252V55.4H293.899V47.252H283.287V45.012H293.899V33.308H283.707V31.068H307.871V33.308H297.679V45.012H308.291V47.252H297.679ZM287.991 43.276L284.211 35.044H288.271L292.051 43.276H287.991ZM333.015 42.324L331.447 40.42L330.411 42.324H311.903V40.084L321.143 30.76H325.763L316.495 40.084H331.195L326.463 34.288H330.803L337.327 42.324H333.015ZM313.443 55.316V44.48H335.227V55.316H313.443ZM331.447 46.72H317.223V53.076H331.447V46.72Z" fill="#01FFFF"/>
</g>
<rect x="15.2402" y="43" width="20" height="20" rx="2" transform="rotate(-45 15.2402 43)" fill="#01FFFF"/>
<foreignObject x="29.0688" y="30.3433" width="23.3135" height="23.3135"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_1_2673_3766_clip_path);height:100%;width:100%"></div></foreignObject><rect data-figma-bg-blur-radius="4" x="32.2402" y="42" width="12" height="12" rx="2" transform="rotate(-45 32.2402 42)" fill="white" fill-opacity="0.8"/>
</g>
<defs>
<filter id="filter0_f_2673_3766" x="22.0078" y="69" width="415.07" height="20" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4" result="effect1_foregroundBlur_2673_3766"/>
</filter>
<filter id="filter1_d_2673_3766" x="52.835" y="30.7041" width="286.492" height="28.8359" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2673_3766"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2673_3766" result="shape"/>
</filter>
<clipPath id="bgblur_1_2673_3766_clip_path" transform="translate(-29.0688 -30.3433)"><rect x="32.2402" y="42" width="12" height="12" rx="2" transform="rotate(-45 32.2402 42)"/>
</clipPath><linearGradient id="paint0_linear_2673_3766" x1="960.24" y1="1" x2="960.24" y2="82" gradientUnits="userSpaceOnUse">
<stop stop-color="#0095FF" stop-opacity="0"/>
<stop offset="1" stop-color="#0095FF"/>
</linearGradient>
<linearGradient id="paint1_linear_2673_3766" x1="278.24" y1="0" x2="278.24" y2="80" gradientUnits="userSpaceOnUse">
<stop stop-color="#0095FF" stop-opacity="0"/>
<stop offset="1" stop-color="#0095FF"/>
</linearGradient>
<linearGradient id="paint2_linear_2673_3766" x1="470.777" y1="80" x2="534.998" y2="2.62137" gradientUnits="userSpaceOnUse">
<stop stop-color="#01FFFF"/>
<stop offset="1" stop-color="#01FFFF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_2673_3766" x1="357.481" y1="5.49756e+11" x2="2298.86" y2="5.49756e+11" gradientUnits="userSpaceOnUse">
<stop stop-color="#01FFFF"/>
<stop offset="1" stop-color="#01FFFF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint4_linear_2673_3766" x1="1844.95" y1="67.5532" x2="1571.05" y2="67.5532" gradientUnits="userSpaceOnUse">
<stop stop-color="#0095FF" stop-opacity="0"/>
<stop offset="1" stop-color="#0095FF"/>
</linearGradient>
<linearGradient id="paint5_linear_2673_3766" x1="1850.63" y1="-18.1352" x2="1562.69" y2="57.0261" gradientUnits="userSpaceOnUse">
<stop stop-color="#01FFFF" stop-opacity="0"/>
<stop offset="1" stop-color="#01FFFF"/>
</linearGradient>
<linearGradient id="paint6_linear_2673_3766" x1="533.58" y1="6.15727e+13" x2="701.27" y2="6.15727e+13" gradientUnits="userSpaceOnUse">
<stop stop-color="#01FFFF"/>
<stop offset="1" stop-color="#01FFFF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint7_linear_2673_3766" x1="523.445" y1="4.83785e+13" x2="638.116" y2="4.83785e+13" gradientUnits="userSpaceOnUse">
<stop stop-color="#01FFFF"/>
<stop offset="1" stop-color="#01FFFF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint8_linear_2673_3766" x1="506.24" y1="81" x2="1440.19" y2="81" gradientUnits="userSpaceOnUse">
<stop stop-color="#01FFFF"/>
<stop offset="1" stop-color="#01FFFF" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_2673_3766">
<rect width="1920" height="82" fill="white" transform="translate(0.240234)"/>
</clipPath>
</defs>
</svg>
