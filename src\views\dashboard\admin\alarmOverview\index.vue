<!--
 * @Descripttion: 
 * @Author: wangl
 * @version: 
 * @Date: 2025-03-04 09:08:12
 * @LastEditors: wangl
 * @LastEditTime: 2025-03-24 11:40:49
-->
<template>
  <div style="height: 100%">
    <chart-box title="告警总览" style="height:26%" @click="showFailDetail()">
      <warningView ref="warningView" :sys-type="sysType" :key-model="alarmOverviewData" />
    </chart-box>
    <chart-box title="工单概况" style="height:30%" @click="showFailDetail()">
      <!-- <failRateRank :key-model="alarmOverviewData.alarmList" /> -->
      <workOrder />
    </chart-box>
    <chart-box title="最新告警" style="height:43%">
      <newAlarmList :sys-type="sysType" />
    </chart-box>
    <el-dialog v-dialogDrag append-to-body class="layerManager dashboard-dialog" title="告警情况统计分析"
      :visible.sync="gjqktjfxdialogFormVisible" width="101.85vh" height="37.04vh">
      <alarmStatics :show-dialog="gjqktjfxdialogFormVisible" @handCancle="gjqktjfxhandCancle" />
    </el-dialog>
  </div>
</template>

<script>
import warningView from './warningView'
import failRateRank from './failRateRank'
import newAlarmList from './newAlarmList'
import alarmStatics from '../../../PMS/statisticAnalysis/alarmStatics'
import chartBox from '../chartBox'
import workOrder from '../equipMentObject/workOrder'

export default {
  components: {
    warningView,
    failRateRank,
    newAlarmList,
    alarmStatics,
    chartBox,
    workOrder
  },
  props: ['alarmOverviewData', 'warningViewModel', 'sysType'],
  data() {
    return {
      gjqktjfxdialogFormVisible: false
    }
  },
  mounted() {

  },
  methods: {
    gjqktjfxhandCancle() {
      this.gjqktjfxdialogFormVisible = true
    },
    showGjtjDetail() {
      this.gjqktjfxdialogFormVisible = true
    }
  }
}
</script>
