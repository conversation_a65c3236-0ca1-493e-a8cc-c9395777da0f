<!--
 * @Descripttion: 
 * @Author: wangl
 * @version: 
 * @Date: 2025-03-04 09:08:12
 * @LastEditors: wangl
 * @LastEditTime: 2025-03-25 09:21:50
-->
<template>
  <div class="app-container">
    <search v-model="query" @search="get" />
    <div style="clear:both"></div>
    <div class="table-button">
      <el-button 
        v-if="checkButtonPermission('324241')"
        v-waves 
        icon="el-icon-download" 
        style="height: 3.24vh" 
        :loading="exportLoading"
        @click.stop="handleExportExcel"
      >导出</el-button>
    </div>
    <el-table v-loading="waiting" :data="dataList" v-bind="tableConfig" @sort-change="sortChange">
      <el-table-column width="100" type="index" label="序号" v-bind="columnConifg" />
      <el-table-column min-width="23.15vh" prop="alarmMsg" label="工单内容（告警信息）" sortable="custom" v-bind="columnConifg" />
      <el-table-column min-width="23.15vh" prop="resourceName" label="资源名称" sortable="custom" v-bind="columnConifg" />
      <el-table-column min-width="13.89vh" prop="assetTypeName" label="设备类型" sortable="custom" v-bind="columnConifg" />
      <el-table-column min-width="13.89vh" prop="comment" label="处理意见" sortable="custom" v-bind="columnConifg" />
      <el-table-column min-width="13.89vh" prop="receiverName" label="接收人" sortable="custom" v-bind="columnConifg" />
      <el-table-column min-width="13.89vh" prop="orderLevel" label="工单级别" sortable="custom" v-bind="columnConifg" />
      <el-table-column min-width="13.89vh" prop="influenceScope" label="影响范围" sortable="custom" v-bind="columnConifg">
        <template slot-scope="scope">
          <span>
            {{ scope.row.influenceScope === '1' ? '大' : scope.row.influenceScope === '2' ? '中' :
              scope.row.influenceScope
                === '3' ? '小' : '' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column min-width="13.89vh" prop="orderType" label="工单状态" sortable="custom" v-bind="columnConifg" />
      <el-table-column min-width="11.11vh" prop="assetOnlineStatus" label="在线状态" sortable="custom"
        v-bind="columnConifg">
        <template slot-scope="scope">
          <span v-if="scope.row.assetOnlineStatus === null">-</span>
          <el-tag v-else :type="scope.row.assetOnlineStatus === 0 ? 'info' : 'success'">{{ scope.row.assetOnlineStatus
            === 0 ? '离线' : '在线' }}</el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column min-width="120px" prop="assetAlarmStatus" label="告警信息" sortable="custom" v-bind="columnConifg" >
        <template slot-scope="scope">
          <span v-if="scope.row.assetAlarmStatus === null">-</span>
          <el-tag v-else
            :type="scope.row.assetAlarmStatus === 1 ? 'danger' : 'success'"
          >{{ scope.row.assetAlarmStatus === 1 ? '有' : '无' }}</el-tag>
        </template>
      </el-table-column> -->
      <el-table-column min-width="13.89vh" prop="signUserName" label="签单人" sortable="custom" v-bind="columnConifg" />
      <el-table-column min-width="13.89vh" prop="areaName" label="设备所在地" sortable="custom" show-overflow-tooltip />
      <el-table-column min-width="13.89vh" prop="dispatchTime" label="派单时间" sortable="custom"
        :formatter="table.formatMinute" v-bind="columnConifg" />
      <el-table-column min-width="13.89vh" prop="updateTime" label="更新时间" sortable="custom"
        :formatter="table.formatMinute" v-bind="columnConifg" />
      <el-table-column v-bind="columnFixConifg" :width="tableButtonWidth">
        <template slot-scope="scope">
          <div v-if="hasAnyActionPermission(['324242'])">
            <template v-for="item in tableButtonList">
              <el-button 
                v-if="item.isShow(scope.row) && checkButtonPermission('324242')" 
                :key="item.name + random.getUuid()" 
                type="text"
                @click="item.click(scope)"
              >{{ item.name }}</el-button>
            </template>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <app-pager />
  </div>
</template>
<script>
import search from '../search'
import { listPage } from '@/views/otherResource/soc/mixins'
import { flowListPage } from '@/views/otherResource/soc/flow/mixins/'
import download from '@/views/otherResource/soc/mixins/download'
import permission from '@/views/otherResource/soc/mixins/permission'

export default {
  components: { search },
  mixins: [listPage, flowListPage, download, permission],
  data() {
    return {
      exportLoading: false,
      query: {
        page: 1,
        limit: 10
      }
    }
  },
  computed: {
    tableButtonList() {
      return [
        { name: '详情', isShow: (row) => row.orderType, click: (scope) => this.openDone({ ...scope.row }) }
      ]
    }
  },
  created() {
    this.get()
  },
  methods: {
    get() {
      const data = { ...this.query }
      if (data.areaId) {
        data.areaId = data.areaId[data.areaId.length - 1]
      }
      this.getBase({
        url: '/flowService/act-query/getMyCreateList',
        data,
        method: 'post'
      })
    },
    sortChange(row) {
      this.query.orderBy = row.prop
      this.query.order = row.order === 'ascending' ? 'ASC' : row.order === 'descending' ? 'DESC' : null
      this.get()
    },
    // 列表导出
    handleExportExcel() {
      // 处理loading
      this.exportLoading = true
      const data = { ...this.query }
      if (data.areaId) {
        data.areaId = data.areaId[data.areaId.length - 1]
      }
      delete data.page
      delete data.limit
      const url = '/flowService/act-query/myListExportExcel'
      const title = `发起工单导出报表`

      this.download({
        url,
        data,
        method: 'post',
        fileName: `${title}.xlsx`
      }, 'application/vnd.ms-excel').then(res => {
        this.exportLoading = false
      }).catch(() => {
        this.exportLoading = false
      })
    },
  }
}
</script>
<style lang="scss" scoped>
.table-button {
  margin-top: 0.93vh;
  margin-bottom: 0.93vh;
  float: right;
}
</style>
