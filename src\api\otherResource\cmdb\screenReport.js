import request from '@/utils/request'

const url = '/cmdbService'

// 资源概况查询
export function getOverview(params) {
  return request({
    url: url + '/instance-statistics/getOverview',
    method: 'get',
    params: params
  })
}

// 专题统计查询
export function getSubject(params) {
  return request({
    url: url + '/instance-statistics/getSubject',
    method: 'get',
    params: params
  })
}
// 区域统计查询
export function getArea(params) {
  return request({
    url: url + '/instance-statistics/getArea',
    method: 'get',
    params: params
  })
}
// 设备类型统计查询
export function getInstanceType(params) {
  return request({
    url: url + '/instance-statistics/getInstanceType',
    method: 'get',
    params: params
  })
}
// 厂商设备统计查询
export function getManufacturerOrTypeCode(params) {
  return request({
    url: url + '/instance-statistics/getManufacturerOrTypeCode',
    method: 'get',
    params: params
  })
}
