import request from '@/utils/request'
// export function fetchPollingInfo(data) {
//   return request({
//     url: '/alarm/getPollingInfo',
//     method: 'post',
//     data
//   })
// }

export function fetchPollingList(data) {
  return request({
    url: '/alarm/pollingList',
    method: 'post',
    data
  })
}

export function fetchList(data) {
  return request({
    url: '/alarm/list',
    method: 'post',
    data
  })
}

export function fetchDetail(queryData) {
  return request({
    url: '/alarm/detail',
    method: 'get',
    params: queryData
  })
}

export function fetchDelete(deptId) {
  var data = { deptId }
  return request({
    url: '/alarm/delete',
    method: 'post',
    data
  })
}

export function fetchDeletes(deptIds) {
  var data = deptIds
  return request({
    url: '/alarm/deletes',
    method: 'post',
    data
  })
}

export function fetchCreate(data) {
  return request({
    url: '/dept/create',
    method: 'post',
    data
  })
}

export function fetchUpdate(data) {
  return request({
    url: '/alarm/update',
    method: 'post',
    data
  })
}

export function fetchPushAlarm(data) {
  return request({
    url: '/estateManager/pushAlarm',
    method: 'post',
    data
  })
}

export function fetchSelectCollectionAllAlarm(data) {
  return request({
    url: '/alarm/selectCollectionAllAlarm',
    method: 'post',
    data
  })
}

