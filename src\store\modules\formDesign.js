// @ts-nocheck
import common from '@/utils/common'

export default {
  namespaced: true,
  state: {
    showType: '',
    activeKey: '',
    formList: [],
    activeForm: null,
    rules: {},
    grid: {},
    formAttr: {},
    ruleForm: {}
  },
  mutations: { // 同步更新事件
    updateShowType(state, value) {
      state.showType = value
    },
    updateActiveKey(state, value) {
      state.activeKey = value
    },
    updateFormAttr(state, value) {
      state.formAttr = value
    },
    updateFormList(state, value) {
      state.formList = value
      value.forEach(element => {
        if (element.type !== 'grid') {
          state.rules[element.key] = [{ required: element.options.required, message: '必填项不能为空', trigger: 'blur' }]
        } else {
          element.cols.forEach(element2 => {
            element2.list.forEach(element3 => {
              state.rules[element3.key] = [{ required: element3.options.required, message: '必填项不能为空', trigger: 'blur' }]
            })
          })
        }
        state.ruleForm[element.key] = element.value
      })
      console.log('state.rules', state.rules)
    },
    updateActiveForm(state, value) {
      state.activeForm = value
    },
    updateRules(state, value) {
      state.rules = value
    },
    updateGrid(state, value) {
      state.grid[value.key] = value.value
    }
  },
  actions: { // 异步提交事件
    setFormAttr({ commit }, value) {
      commit('updateFormAttr', value)
    },
    setFormList({ commit }, value) {
      window.localStorage.formList = JSON.stringify(value)
      commit('updateFormList', value)
    },
    addFormList({ commit, state }, form) {
      console.log('addFormList')
      // let newFormList = JSON.parse(window.localStorage.formList).push(form);

      const newFormList = common.deepClone(state.formList)
      newFormList.push(form)
      window.localStorage.formList = JSON.stringify(newFormList)
      commit('updateFormList', newFormList)
    },
    removeFormList({ commit }, key) {
      const newFormList = JSON.parse(window.localStorage.formList)
      let delIndex = -1
      for (let i = 0; i < newFormList.length; i++) {
        const element = newFormList[i]

        if (element.key === key) {
          delIndex = i
        }
      }
      if (delIndex !== -1) {
        newFormList.splice(delIndex, 1)
      } else {
        console.warn('未找到此表单key', key)
      }
      window.localStorage.formList = JSON.stringify(newFormList)
      commit('updateFormList', newFormList)
    }
  },
  getters: {
    getFormRules(state) {
      const rules = {}
      state.formList.forEach(element => {
        rules[element.key] = [{ required: element.options.required, message: '必填项不能为空', trigger: 'blur' }]
      })
      console.log('rules', rules)
      return rules
    }
  }
}
